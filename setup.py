from setuptools import setup, find_packages

# 讀取 requirements.txt 作為基礎依賴
def read_requirements(filename):
    with open(filename, 'r') as f:
        return [line.strip() for line in f
                if line.strip() and not line.startswith('#') and not line.startswith('-r')]

def read_test_requirements():
    with open('requirements-test.txt', 'r') as f:
        return [line.strip() for line in f
                if line.strip() and not line.startswith('#') and not line.startswith('-r')]

setup(
    name="lta-user-stats",
    version="0.1.0",
    packages=find_packages(),
    install_requires=read_requirements('requirements.txt'),
    extras_require={
        'test': read_test_requirements(),
        'dev': read_test_requirements(),  # 開發環境也需要測試依賴
    },
    python_requires=">=3.8",
    author="Tagtoo",
    description="LTA User Statistics Analysis Service",
    long_description="A service for analyzing user statistics and generating dynamic audience rules.",
)