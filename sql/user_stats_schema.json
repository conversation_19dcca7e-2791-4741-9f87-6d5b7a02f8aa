[{"name": "ec_id", "type": "INTEGER", "mode": "REQUIRED"}, {"name": "permanent", "type": "STRING", "mode": "REQUIRED"}, {"name": "purchase_count", "type": "INTEGER", "mode": "REQUIRED"}, {"name": "registration_time", "type": "TIMESTAMP"}, {"name": "first_interaction_time", "type": "TIMESTAMP", "mode": "REQUIRED"}, {"name": "last_interaction_time", "type": "TIMESTAMP", "mode": "REQUIRED"}, {"name": "first_purchase_time", "type": "TIMESTAMP"}, {"name": "last_purchase_time", "type": "TIMESTAMP"}, {"name": "total_purchase_amount", "type": "FLOAT", "mode": "REQUIRED"}, {"name": "total_sessions", "type": "INTEGER", "mode": "REQUIRED"}, {"name": "created_at", "type": "TIMESTAMP"}, {"name": "updated_at", "type": "TIMESTAMP"}, {"name": "first_add_to_cart_time", "type": "TIMESTAMP"}, {"name": "last_add_to_cart_time", "type": "TIMESTAMP"}, {"name": "first_view_item_time", "type": "TIMESTAMP"}, {"name": "last_view_item_time", "type": "TIMESTAMP"}, {"name": "active_days", "type": "INTEGER"}, {"name": "daily_sessions", "type": "INTEGER"}, {"name": "daily_purchase_count", "type": "INTEGER"}, {"name": "daily_purchase_amount", "type": "FLOAT"}, {"name": "first_purchase_of_day", "type": "TIMESTAMP"}, {"name": "last_purchase_of_day", "type": "TIMESTAMP"}, {"name": "first_add_to_cart_of_day", "type": "TIMESTAMP"}, {"name": "last_add_to_cart_of_day", "type": "TIMESTAMP"}, {"name": "first_view_item_of_day", "type": "TIMESTAMP"}, {"name": "last_view_item_of_day", "type": "TIMESTAMP"}, {"name": "first_interaction_of_day", "type": "TIMESTAMP"}, {"name": "last_interaction_of_day", "type": "TIMESTAMP"}]