-- 新增 created_at 欄位，設定預設值
ALTER TABLE `tagtoo-tracking.event_prod.user_stats`
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP();

-- 新增 updated_at 欄位，設定預設值
ALTER TABLE `tagtoo-tracking.event_prod.user_stats`
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP();

-- 更新現有資料的時間戳記
UPDATE `tagtoo-tracking.event_prod.user_stats`
SET
  created_at = COALESCE(created_at, TIMESTAMP('2025-01-07')),
  updated_at = COALESCE(updated_at, TIMESTAMP('2025-01-07'))
WHERE
  created_at IS NULL
  OR updated_at IS NULL;