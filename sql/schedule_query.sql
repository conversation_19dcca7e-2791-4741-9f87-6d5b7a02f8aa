-- 用於每日更新用戶統計資料
-- 每天執行一次，處理前一天的資料
-- 在 schedule query 那邊設定 01:30 UTC+0 執行
-- 因為 event_prod 資料目前寫入延遲約一個小時（在 streaming buffer 中）
-- 所以這樣設定，避免有漏掉的資料沒有新增到。
DECLARE ec_id_param INT64 DEFAULT 107;
DECLARE target_date DATE DEFAULT DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY);
DECLARE start_timestamp TIMESTAMP DEFAULT TIMESTAMP(target_date);  -- 前一天 00:00:00
DECLARE end_timestamp TIMESTAMP DEFAULT TIMESTAMP_ADD(TIMESTAMP(target_date), INTERVAL 1 DAY);  -- 前一天 24:00:00

MERGE INTO `tagtoo-tracking.event_prod.user_stats` T
USING (
  SELECT
    ec_id,
    permanent,
    COUNT(IF(event.name = 'purchase', 1, NULL)) as purchase_count,
    MIN(IF(event.name = 'register', event_time, NULL)) as registration_time,
    MIN(event_time) as first_interaction_time,
    MAX(event_time) as last_interaction_time,
    MIN(IF(event.name = 'purchase', event_time, NULL)) as first_purchase_time,
    MAX(IF(event.name = 'purchase', event_time, NULL)) as last_purchase_time,
    SUM(IF(event.name = 'purchase', event.value, 0)) as total_purchase_amount,
    COUNT(DISTINCT session.id) as total_sessions
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = ec_id_param
    AND event_time >= start_timestamp
    AND event_time < end_timestamp  -- 使用半開區間 [start, end)
  GROUP BY
    ec_id,
    permanent
) S
ON T.ec_id = S.ec_id AND T.permanent = S.permanent
WHEN MATCHED THEN
  UPDATE SET
    purchase_count = T.purchase_count + S.purchase_count,
    registration_time = LEAST(T.registration_time, S.registration_time),
    first_interaction_time = LEAST(T.first_interaction_time, S.first_interaction_time),
    last_interaction_time = GREATEST(T.last_interaction_time, S.last_interaction_time),
    first_purchase_time = LEAST(COALESCE(T.first_purchase_time, S.first_purchase_time),
                               COALESCE(S.first_purchase_time, T.first_purchase_time)),
    last_purchase_time = GREATEST(COALESCE(T.last_purchase_time, S.last_purchase_time),
                                 COALESCE(S.last_purchase_time, T.last_purchase_time)),
    total_purchase_amount = T.total_purchase_amount + S.total_purchase_amount,
    total_sessions = T.total_sessions + S.total_sessions,
    updated_at = CURRENT_TIMESTAMP()
WHEN NOT MATCHED THEN
  INSERT (
    ec_id,
    permanent,
    purchase_count,
    registration_time,
    first_interaction_time,
    last_interaction_time,
    first_purchase_time,
    last_purchase_time,
    total_purchase_amount,
    total_sessions,
    created_at,
    updated_at
  )
  VALUES (
    S.ec_id,
    S.permanent,
    S.purchase_count,
    S.registration_time,
    S.first_interaction_time,
    S.last_interaction_time,
    S.first_purchase_time,
    S.last_purchase_time,
    S.total_purchase_amount,
    S.total_sessions,
    CURRENT_TIMESTAMP(),
    CURRENT_TIMESTAMP()
  );