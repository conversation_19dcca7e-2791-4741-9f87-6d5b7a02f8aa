DECLARE ec_id_param INT64 DEFAULT 107;

WITH active_users AS (
  -- 計算各時段的購買次數
  SELECT
    permanent,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 DAY), 1, NULL)) as purchase_count_2m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 180 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY), 1, NULL)) as purchase_count_3m_6m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 210 DAY), 1, NULL)) as purchase_count_7m_12m,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 120 DAY), 1, NULL)) as purchase_count_4m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 270 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 150 DAY), 1, NULL)) as purchase_count_5m_9m,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 270 DAY), 1, NULL)) as purchase_count_9m
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = ec_id_param
    AND event.name = 'purchase'
  GROUP BY permanent
),

user_activity_base AS (
  SELECT
    s.permanent as permanent_id,
    s.purchase_count,
    s.last_interaction_time,
    s.last_purchase_time,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) as days_since_last_interaction,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_purchase_time), DAY) as days_since_last_purchase,
    a.*
  FROM `tagtoo-tracking.event_prod.user_stats` s
  LEFT JOIN active_users a ON s.permanent = a.permanent
  WHERE s.ec_id = ec_id_param
),

-- 建立所有可能的分群組合
segment_combinations AS (
  SELECT * FROM UNNEST([
    STRUCT('F0客' as segment, '尚未購買過第一次的人的全部受眾（有資料無購買）' as description),
    ('F1新客(AY)', '近2個月內第一次購買的會員'),
    ('F1新客(BY)', '前3個月~前6個月內第一次購買的會員'),
    ('F1新客(ES)', '前7個月~1年內第一次購買的會員'),
    ('F淺層客(Z淺)', '近4個月內購買過第2~第5次的會員'),
    ('F淺層客(X)', '前5個月~前9個月內購買過2~5次的會員'),
    ('F深層客(Z深)', '前9個月內購買過6次以上的會員'),
    ('脫離預備客', '曾購買過1次及以上，第10個月~1年以內(含1年)未回購的會員'),
    ('脫離預備客', '曾購買過2次及以上，第10個月~1年以內(含1年)未回購的會員'),
    ('睡眠客', '曾購買過1次及以上，1年以上(不含1年)未回購的會員'),
    ('睡眠客', '曾購買過2次及以上，1年以上(不含1年)未回購的會員')
  ])
),

user_segments AS (
  SELECT
    permanent_id,
    CASE
      WHEN purchase_count = 0 THEN 'F0客'
      -- F1 新客細分
      WHEN purchase_count = 1 AND days_since_last_purchase <= 60 THEN 'F1新客(AY)'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 90 AND 180 THEN 'F1新客(BY)'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 210 AND 365 THEN 'F1新客(ES)'
      -- 淺層客戶細分
      WHEN purchase_count_4m BETWEEN 2 AND 5 THEN 'F淺層客(Z淺)'
      WHEN purchase_count_5m_9m BETWEEN 2 AND 5 THEN 'F淺層客(X)'
      -- 深層客戶
      WHEN purchase_count_9m >= 6 THEN 'F深層客(Z深)'
      -- 睡眠客和脫離預備客細分
      WHEN purchase_count >= 2 AND days_since_last_purchase > 365 THEN '睡眠客'
      WHEN purchase_count = 1 AND days_since_last_purchase > 365 THEN '睡眠客'
      WHEN purchase_count >= 2 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '脫離預備客'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '脫離預備客'
    END as segment,
    CASE
      WHEN purchase_count = 0 THEN '尚未購買過第一次的人的全部受眾（有資料無購買）'
      WHEN purchase_count = 1 AND days_since_last_purchase <= 60 THEN '近2個月內第一次購買的會員'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 90 AND 180 THEN '前3個月~前6個月內第一次購買的會員'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 210 AND 365 THEN '前7個月~1年內第一次購買的會員'
      WHEN purchase_count_4m BETWEEN 2 AND 5 THEN '近4個月內購買過第2~第5次的會員'
      WHEN purchase_count_5m_9m BETWEEN 2 AND 5 THEN '前5個月~前9個月內購買過2~5次的會員'
      WHEN purchase_count_9m >= 6 THEN '前9個月內購買過6次以上的會員'
      WHEN purchase_count >= 2 AND days_since_last_purchase > 365 THEN '曾購買過2次及以上，1年以上(不含1年)未回購的會員'
      WHEN purchase_count >= 1 AND days_since_last_purchase > 365 THEN '曾購買過1次及以上，1年以上(不含1年)未回購的會員'
      WHEN purchase_count >= 2 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '曾購買過2次及以上，第10個月~1年以內(含1年)未回購的會員'
      WHEN purchase_count >= 1 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '曾購買過1次及以上，第10個月~1年以內(含1年)未回購的會員'
    END as description
  FROM user_activity_base
  WHERE purchase_count >= 0  -- 包含所有用戶
)

-- 輸出用戶分群統計
SELECT
  c.segment as `用戶分群`,
  c.description as `描述`,
  COUNT(DISTINCT s.permanent_id) as `用戶數`,
  ROUND(COUNT(DISTINCT s.permanent_id) * 100.0 / NULLIF(SUM(COUNT(DISTINCT s.permanent_id)) OVER(), 0), 2) as `百分比`,
  CONCAT(CAST(ROUND(COUNT(DISTINCT s.permanent_id) * 100.0 / NULLIF(SUM(COUNT(DISTINCT s.permanent_id)) OVER(), 0), 2) as STRING), '%') as `百分比顯示`
FROM segment_combinations c
LEFT JOIN user_segments s ON c.segment = s.segment AND c.description = s.description
GROUP BY c.segment, c.description
ORDER BY
  CASE c.segment
    WHEN 'F0客' THEN 1
    WHEN 'F1新客(AY)' THEN 2
    WHEN 'F1新客(BY)' THEN 3
    WHEN 'F1新客(ES)' THEN 4
    WHEN 'F淺層客(Z淺)' THEN 5
    WHEN 'F淺層客(X)' THEN 6
    WHEN 'F深層客(Z深)' THEN 7
    WHEN '脫離預備客' THEN 8
    WHEN '睡眠客' THEN 9
    ELSE 10
  END,
  c.description;