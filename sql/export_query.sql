EXPORT DATA
OPTIONS(
  uri='gs://tagtoo-ml-workflow/tagtoo_event_107/*.parquet',
  format='PARQUET',
  compression='SNAPPY'
) AS
SELECT
  permanent,
  event_time,
  event.name as event_name,
  event.value as event_value,    -- 用於計算 total_purchase_amount
  event.items,                   -- 用於分析商品類別購買行為
  session.id as session_id,      -- 用於計算 total_sessions
  user_agent.is_bot             -- 用於過濾機器人流量
FROM `tagtoo-tracking.event_prod.tagtoo_event`
WHERE
  ec_id = 107
  AND event_time >= TIMESTAMP('2022-01-01 00:00:00')
  AND event_time < TIMESTAMP('2025-01-09 00:00:00')