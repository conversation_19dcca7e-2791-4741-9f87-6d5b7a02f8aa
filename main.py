from flask import jsonify
from src.main import main, update_user_stats_table
import json


def main_router(request):
    """HTTP 路由處理函數 - Cloud Functions 原生 HTTP 處理"""

    # 處理 /calculate-user-stats
    if request.path == '/calculate-user-stats' and request.method == 'POST':
        return main(request)

    # 處理 /update-user-stats-table
    elif request.path == '/update-user-stats-table' and request.method == 'POST':
        try:
            request_json = request.get_json(silent=True)
            if request_json is None:
                return jsonify({'error': 'Invalid request: No JSON body provided.'}), 400

            ec_ids = request_json.get('ec_ids', [])
            start_time = request_json.get('start_time')
            end_time = request_json.get('end_time')
            timezone_str = request_json.get('timezone_str', 'UTC')

            if not ec_ids or not start_time or not end_time:
                return jsonify({'error': 'ec_ids, start_time, and end_time are required'}), 400

            result = update_user_stats_table(ec_ids, start_time, end_time, timezone_str)
            return jsonify(result), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # 處理無效的 endpoint
    else:
        return jsonify({
            'error': 'Invalid endpoint'
        }), 404