# Polars 高效能實現指南

## 概述

LTA User Stats 系統已成功遷移至 Polars 高效能實現，提供更快的資料處理速度和更高效的記憶體使用。本文檔詳細說明 Polars 實現的架構、使用方式和效能優勢。

**更新日期**: 2025-07-29
**版本**: 1.0.0

## 雙實現架構

### 設計原則

系統採用雙實現架構，確保穩定性和效能的最佳平衡：

1. **Polars 實現** (預設)
   - 使用 `polars-lts-cpu==1.31.0`
   - 預設啟用 (`use_polars=True`)
   - 高效能資料處理

2. **Pandas 實現** (備用)
   - 保留原始實現
   - 向後兼容性保證
   - 穩定性備案

### 核心函數簽名

```python
def update_user_stats_with_daily_data(
    base_stats: pd.DataFrame,
    daily_stats: pd.DataFrame,
    use_polars: bool = True  # 預設使用 Polars
) -> pd.DataFrame:
    """
    更新用戶統計資料，支援 Polars 和 Pandas 兩種實現

    Args:
        base_stats: 基礎用戶統計資料
        daily_stats: 每日新增統計資料
        use_polars: 是否使用 Polars 高效能實現（預設啟用）

    Returns:
        更新後的用戶統計資料 DataFrame
    """
```

## 實現細節

### Polars 實現優勢

1. **記憶體效率**
   - 懶惰評估 (Lazy Evaluation)
   - 更高效的記憶體管理
   - 減少中間結果的記憶體佔用

2. **處理速度**
   - 向量化操作
   - 並行處理能力
   - 優化的查詢執行計畫

3. **資料類型優化**
   - 更精確的資料類型推斷
   - 減少不必要的類型轉換
   - 更好的 NULL 值處理

### 關鍵技術實現

#### Join Key 處理

Polars 實現中使用 `pl.coalesce()` 確保 join key 在 full outer join 時不會變成 null：

```python
# 處理 join key 欄位 - 確保不會變成 null
pl.coalesce([pl.col("ec_id"), pl.col("ec_id_daily")]).alias("ec_id"),
pl.coalesce([pl.col("permanent"), pl.col("permanent_daily")]).alias("permanent"),
```

#### Sessions 累加邏輯

修復了原始 pandas 實現中的 sessions 累加錯誤：

```python
# Polars 實現 - 正確的累加邏輯
pl.when(pl.col("total_sessions").is_null())
  .then(pl.col("daily_sessions"))
  .otherwise(pl.col("total_sessions") + pl.col("daily_sessions").fill_null(0))
  .alias("total_sessions")
```

## 使用指南

### 預設使用方式

系統預設使用 Polars 實現，無需額外配置：

```python
# 預設使用 Polars 實現
result = update_user_stats_with_daily_data(base_stats, daily_stats)
```

### 切換至 Pandas 實現

如需使用 pandas 實現（例如調試或兼容性需求）：

```python
# 明確指定使用 Pandas 實現
result = update_user_stats_with_daily_data(
    base_stats,
    daily_stats,
    use_polars=False
)
```

### API 端點使用

在 HTTP API 中，系統會自動使用 Polars 實現。如需切換，可在未來版本中添加參數支援。

## 效能基準測試

### 測試環境

- **平台**: Linux (Docker 環境)
- **Python**: 3.12.11
- **CPU**: 8 核心
- **記憶體**: 11.73 GB

### 測試結果

根據 `test_pandas_vs_polars_implementation_consistency` 測試：

- **執行時間**: 約 28 秒（包含一致性驗證）
- **記憶體使用**: 313.04 MB
- **結果一致性**: 100% 相同

### 預期效能提升

基於 Polars 的特性，預期在生產環境中獲得：

- **處理速度**: 2-5x 提升（取決於資料大小）
- **記憶體使用**: 20-40% 減少
- **大資料集處理**: 顯著改善

## 品質保證

### 一致性測試

`test_pandas_vs_polars_implementation_consistency` 測試確保：

1. **完全相同的輸出**: 兩種實現產生位元級相同的結果
2. **邊界條件處理**: 空資料、NULL 值、極值等情況
3. **複雜場景**: 新用戶、現有用戶、混合場景

### 驗證邏輯保留

所有原始驗證邏輯完整保留：

```python
# NULL 值驗證
null_first = result_df['first_interaction_time'].isna().sum()
null_last = result_df['last_interaction_time'].isna().sum()
if null_first > 0 or null_last > 0:
    logger.error(f"最終結果中有 NULL 值: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")
    raise ValueError(f"互動時間不能為 NULL: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")
```

## 故障排除

### 常見問題

1. **PerformanceWarning**:
   ```
   PerformanceWarning: Determining the column names of a LazyFrame requires resolving its schema
   ```
   - **原因**: 使用 `.columns` 屬性觸發 schema 解析
   - **解決**: 未來版本將使用 `collect_schema().names()` 優化

2. **記憶體使用異常**:
   - **檢查**: 確認 `use_polars=True` 正確設定
   - **監控**: 觀察記憶體使用模式

### 回滾方案

如遇到問題，可快速回滾至 pandas 實現：

```python
# 臨時回滾至 pandas 實現
result = update_user_stats_with_daily_data(
    base_stats,
    daily_stats,
    use_polars=False
)
```

## 未來規劃

### 短期優化

1. **消除 PerformanceWarning**
2. **進一步記憶體優化**
3. **效能基準測試擴展**

### 長期考慮

1. **評估移除 pandas 實現的可行性**
2. **探索更多 Polars 高級功能**
3. **整合更多向量化操作**

## 相關文檔

- [DEVELOPMENT.md](../DEVELOPMENT.md) - 開發環境設定
- [README.md](../README.md) - 專案概述
- [測試文檔](../tests/unit/test_data_processing.py) - 一致性測試實現

## 聯絡資訊

如有問題或建議，請聯絡 Tagtoo Data Team。
