# Parquet Time Range Checker

這個工具用於檢查存儲在 Google Cloud Storage 中的 parquet 檔案的時間範圍，幫助了解數據覆蓋範圍並識別時間序列數據中的任何空隙。

## 功能特色

- 🔍 **檔案發現**: 自動列出指定 GCS bucket 中的所有 parquet 檔案
- ⏰ **時間分析**: 提取並分析檔案中的時間戳資訊
- 📊 **覆蓋範圍**: 計算整體時間覆蓋範圍和統計資訊
- 🕳️ **空隙識別**: 自動識別時間序列中的空隙
- 📈 **詳細報告**: 生成包含統計資訊、時間範圍和空隙的詳細報告
- 🏷️ **日期分組**: 按日期分組檔案，便於理解數據分布

## 安裝需求

這個腳本需要以下 Python 套件：

```
pandas>=2.2.3
pyarrow>=19.0.0
google-cloud-storage>=2.10.0
gcsfs>=2025.3.0
python-dateutil
pytz
```

這些套件已經包含在專案的 `requirements.txt` 中。

## 使用方法

### 1. 命令列使用

#### 基本用法

```bash
# 檢查指定 bucket 中的所有 parquet 檔案
python scripts/parquet_time_range_checker.py --bucket-name your-data-bucket

# 檢查特定路徑下的檔案
python scripts/parquet_time_range_checker.py --bucket-name your-data-bucket --prefix data/2024/

# 指定自定義時間戳欄位
python scripts/parquet_time_range_checker.py \
  --bucket-name your-data-bucket \
  --timestamp-columns event_time created_at processing_time

# 限制分析檔案數量（用於測試）
python scripts/parquet_time_range_checker.py \
  --bucket-name your-data-bucket \
  --max-files 10

# 將報告儲存到檔案
python scripts/parquet_time_range_checker.py \
  --bucket-name your-data-bucket \
  --output analysis_report.txt
```

#### 完整選項

```bash
python scripts/parquet_time_range_checker.py --help
```

### 2. Docker 環境中使用

```bash
# 先建立測試環境
make build-test

# 在 Docker 容器中執行
docker run --rm \
  -v ~/.config/gcloud:/root/.config/gcloud \
  -e GOOGLE_APPLICATION_CREDENTIALS=/root/.config/gcloud/application_default_credentials.json \
  asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:main-test \
  python scripts/parquet_time_range_checker.py --bucket-name your-bucket
```

### 3. 程式化使用

```python
from scripts.parquet_time_range_checker import ParquetTimeRangeChecker

# 建立檢查器
checker = ParquetTimeRangeChecker(
    bucket_name="your-data-bucket",
    prefix="data/parquet/",
    timestamp_columns=["event_time", "created_at"]
)

# 執行完整檢查
report = checker.check_time_range(max_files=50)
print(report)

# 或者分步驟執行
files = checker.list_parquet_files()
file_infos = [checker.extract_timestamp_info(f) for f in files[:10]]
analysis = checker.analyze_time_coverage(file_infos)
```

## 設定 GCS 認證

### 方法 1: 使用服務帳戶金鑰

```bash
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

### 方法 2: 使用 gcloud CLI

```bash
gcloud auth application-default login
```

### 方法 3: 在 Cloud Run/Cloud Functions 中

服務會自動使用分配的服務帳戶身份。

## 輸出報告範例

```
================================================================================
PARQUET 檔案時間範圍分析報告
================================================================================
Bucket: my-data-bucket
Prefix: data/events/
分析時間: 2024-01-15 14:30:25

整體統計:
  檔案數量: 156
  總資料行數: 1,234,567
  總檔案大小: 2,847.32 MB

時間範圍:
  開始時間: 2024-01-01 00:00:00 UTC+0 (2024-01-01 08:00:00 CST+8)
  結束時間: 2024-01-14 23:59:59 UTC+0 (2024-01-15 07:59:59 CST+8)
  總天數: 14 天

發現 2 個時間空隙:
  空隙 1:
    開始: 2024-01-03 18:00:00 UTC+0 (2024-01-04 02:00:00 CST+8)
    結束: 2024-01-04 06:00:00 UTC+0 (2024-01-04 14:00:00 CST+8)
    持續時間: 0.50 天 (12.00 小時)

  空隙 2:
    開始: 2024-01-10 20:00:00 UTC+0 (2024-01-11 04:00:00 CST+8)
    結束: 2024-01-11 08:00:00 UTC+0 (2024-01-11 16:00:00 CST+8)
    持續時間: 0.50 天 (12.00 小時)

按日期分組的檔案:
  20240101: 12 個檔案
  20240102: 11 個檔案
  20240103: 8 個檔案
  ...

================================================================================
```

## 支援的時間戳欄位

腳本會自動識別以下常見的時間戳欄位名稱：

- `timestamp`
- `created_at`
- `updated_at`
- `event_time`
- `date`
- `datetime`
- `time`
- `_timestamp`

您也可以使用 `--timestamp-columns` 參數指定自定義的欄位名稱。

## 測試

```bash
# 執行單元測試
make test

# 執行特定測試
pytest tests/test_parquet_time_range_checker.py -v

# 執行範例程式
python examples/parquet_checker_examples.py
```

## 效能考量

- 對於大型檔案，建議先使用 `--max-files` 參數進行測試
- 腳本會嘗試只讀取必要的欄位以提升效能
- 大型 bucket 可能需要較長時間來列出所有檔案

## 故障排除

### 常見問題

1. **認證錯誤**
   ```
   google.auth.exceptions.DefaultCredentialsError
   ```
   - 確保已設定 `GOOGLE_APPLICATION_CREDENTIALS` 環境變數
   - 或使用 `gcloud auth application-default login`

2. **權限不足**
   ```
   google.api_core.exceptions.Forbidden
   ```
   - 確保服務帳戶有 `Storage Object Viewer` 權限

3. **找不到檔案**
   ```
   在 bucket 中未找到 parquet 檔案
   ```
   - 檢查 bucket 名稱和路徑前綴是否正確
   - 確認檔案副檔名為 `.parquet`

4. **記憶體不足**
   - 使用 `--max-files` 限制檔案數量
   - 考慮分批處理大量檔案

### 除錯模式

```bash
python scripts/parquet_time_range_checker.py \
  --bucket-name your-bucket \
  --log-level DEBUG
```

## 貢獻

歡迎提交 Issue 和 Pull Request 來改善這個工具。

## 授權

此專案使用與主專案相同的授權條款。
