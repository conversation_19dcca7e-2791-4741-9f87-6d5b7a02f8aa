# LTA User Stats Terraform 變數定義檔案
# 在部署時可以通過 terraform.tfvars 檔案覆蓋這些預設值

# 必要變數
project_id = "tagtoo-ml-workflow"
region     = "asia-east1"

# EC IDs 配置
ec_ids = [107, 2808, 2980, 3819, 3820]

# 排程配置
schedule  = "30 01 * * *"  # 每天 01:30 (台北時間)
time_zone = "Asia/Taipei"

# Cloud Scheduler 超時設定
cloud_scheduler_timeout = 1800  # 30 分鐘，Cloud Scheduler API 最大值限制

# 標籤 (可選)
# labels = {
#   environment = "production"
#   team        = "ml-team"
#   cost_center = "lta-user-stats"
# }
