from functools import wraps
from google.api_core.exceptions import GoogleAPIError
import logging

logger = logging.getLogger(__name__)

class LTAError(Exception):
    """基礎錯誤類別"""
    def __init__(self, message="LTA processing error", detail=None):
        self.message = message
        self.detail = detail
        super().__init__(message)

class DataValidationError(LTAError):
    """資料驗證錯誤"""

class CostCalculationError(LTAError):
    """成本計算錯誤"""

def handle_error(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except GoogleAPIError as e:
            logger.error(f"Google API Error: {str(e)}")
            return {'error': 'Cloud service error'}, 503
        except DataValidationError as e:
            logger.warning(f"Data validation failed: {e.detail}")
            return {'error': e.message}, 400
        except Exception as e:
            logger.critical(f"Unhandled exception: {str(e)}")
            return {'error': 'Internal server error'}, 500
    return wrapper