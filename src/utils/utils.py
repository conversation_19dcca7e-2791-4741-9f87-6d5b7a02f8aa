"""
通用工具函數模組
"""
import os
import logging
from typing import Optional
import google.auth
from google.auth.exceptions import DefaultCredentialsError

from src.utils.logging_setup import configure_logging
from src.utils.time_utils import get_taiwan_date_str  # 重新匯出

logger = configure_logging()

def get_project_id() -> str:
    """獲取 Google Cloud Project ID

    Returns:
        str: Google Cloud Project ID

    Raises:
        ValueError: 無法獲取 Project ID 時拋出
    """
    # 方法 1: 從環境變數獲取
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCP_PROJECT')

    if project_id:
        logger.debug(f"從環境變數獲取到 Project ID: {project_id}")
        return project_id

    # 方法 2: 從 Google Auth 預設憑證獲取
    try:
        credentials, project_id = google.auth.default()
        if project_id:
            logger.debug(f"從 Google Auth 預設憑證獲取到 Project ID: {project_id}")
            return project_id
    except DefaultCredentialsError:
        logger.debug("無法從 Google Auth 預設憑證獲取 Project ID")
    except Exception as e:
        logger.debug(f"從 Google Auth 獲取 Project ID 時發生錯誤: {e}")

    # 方法 3: 硬編碼預設值（用於測試或特定環境）
    default_project_id = "tagtoo-ml-workflow"
    logger.warning(f"無法從其他來源獲取 Project ID，使用預設值: {default_project_id}")
    return default_project_id


# 重新匯出其他常用函數，保持向後相容性
__all__ = ['get_project_id', 'get_taiwan_date_str']
