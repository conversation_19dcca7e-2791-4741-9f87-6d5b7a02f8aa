from datetime import datetime, timedelta, timezone
from dateutil.parser import parse as date_parse
import pytz
from dateutil import tz, parser
from src.utils.logging_setup import configure_logging

TAIPEI_TZ = pytz.timezone('Asia/Taipei')
logger = configure_logging()

def get_taiwan_date_str(utc_dt: datetime) -> str:
    """將 UTC 時間轉換為台灣時間的日期字串 (YYYYMMDD)

    Args:
        utc_dt: UTC 時間

    Returns:
        str: 台灣時間的日期字串，格式為 YYYYMMDD
    """
    # 確保輸入時間有時區資訊
    if utc_dt.tzinfo is None:
        logger.warning(f"輸入時間 {utc_dt} 沒有時區資訊，假設為 UTC")
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)

    # 轉換為台灣時間
    taiwan_dt = utc_dt.astimezone(TAIPEI_TZ)
    taiwan_date_str = taiwan_dt.strftime('%Y%m%d')

    # 記錄轉換過程，增加更詳細的時區資訊
    logger.debug(f"時區轉換: UTC {utc_dt.strftime('%Y-%m-%d %H:%M:%S %Z')} [UTC+0] -> "
                 f"台灣 {taiwan_dt.strftime('%Y-%m-%d %H:%M:%S %Z')} [UTC+8] -> {taiwan_date_str}")

    return taiwan_date_str

def format_datetime_with_timezone(dt: datetime, include_both_timezones=True) -> str:
    """格式化日期時間，顯示時區信息

    Args:
        dt: 日期時間
        include_both_timezones: 是否同時顯示 UTC 和台灣時間

    Returns:
        str: 格式化後的日期時間字符串
    """
    # 確保輸入時間有時區資訊
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    # 轉換為台灣時間
    taiwan_dt = dt.astimezone(TAIPEI_TZ)

    if include_both_timezones:
        # 同時顯示 UTC 和台灣時間
        utc_dt = dt.astimezone(timezone.utc)
        return f"{taiwan_dt.strftime('%Y-%m-%d %H:%M:%S')} [台灣時間/UTC+8] (UTC: {utc_dt.strftime('%Y-%m-%d %H:%M:%S')})"
    else:
        # 只顯示台灣時間
        return f"{taiwan_dt.strftime('%Y-%m-%d %H:%M:%S')} [台灣時間/UTC+8]"

def get_local_cache_path(date: datetime, prefix: str = "daily_stats") -> str:
    """生成本地快取檔案路徑

    Args:
        date: 日期
        prefix: 檔案前綴

    Returns:
        str: 本地快取檔案路徑
    """
    date_str = get_taiwan_date_str(date)
    return f"/tmp/{prefix}_{date_str}.parquet"

def parse_time_range(request_data: dict) -> tuple:
    """整合 request_data 參數解析與預設時間邏輯"""
    def parse_with_fallback(dt_str: str, default: datetime) -> datetime:
        """解析時間字串，無時區資訊視為台灣時區"""
        try:
            dt = parser.parse(dt_str)
            if not dt.tzinfo:
                dt = TAIPEI_TZ.localize(dt)  # 使用台灣時區
            return dt.astimezone(timezone.utc)
        except Exception as e:
            logger.warning(f"無法解析時間參數 {dt_str}，使用預設值: {default.isoformat()}")
            return default

    # 預設時間計算 (台灣時間昨日 00:00 ~ 今日 00:00 轉換為 UTC)
    try:
        tw_tz = tz.gettz('Asia/Taipei')
        now_tw = datetime.now(tw_tz).replace(hour=0, minute=0, second=0, microsecond=0)
        default_end = now_tw.astimezone(timezone.utc)
        default_start = (now_tw - timedelta(days=1)).astimezone(timezone.utc)
    except Exception as e:
        logger.error(f"預設時間計算失敗: {str(e)}")
        raise ValueError("無法計算預設時間範圍") from e

    # 解析請求參數
    try:
        # 優先使用 request_data 參數，無參數時使用預設值
        start_time = (
            parse_with_fallback(request_data['start_time'], default_start)
            if 'start_time' in request_data
            else default_start
        )

        end_time = (
            parse_with_fallback(request_data['end_time'], default_end)
            if 'end_time' in request_data
            else default_end
        )

        logger.info(f"start_time: {start_time}, end_time: {end_time}")
        # 強制時間範圍限制
        if start_time >= end_time:
            raise ValueError("開始時間必須早於結束時間")

        if (end_time - start_time) > timedelta(days=31):
            raise ValueError("時間範圍不能超過31天")

        logger.info(f"最終時間範圍 (UTC): {start_time.isoformat()} - {end_time.isoformat()}")
        return start_time, end_time

    except KeyError:
        logger.info("未提供時間參數，使用預設範圍")
        return default_start, default_end
    except ValueError as ve:
        logger.error(f"時間參數錯誤: {str(ve)}")
        raise
    except Exception as e:
        logger.error(f"無法解析時間參數: {str(e)}")
        raise ValueError("無效的時間參數格式") from e