from json_logic import jsonLogic
from datetime import datetime, timezone

class RuleEvaluator:
    """搬移自 dynamic_audience_rules.py 的評估邏輯"""

    @staticmethod
    def evaluate(rule: dict, user_data: dict) -> bool:
        """強化型規則評估"""
        processed = {
            k: user_data[k].astimezone(timezone.utc)
            if isinstance(user_data[k], datetime)
            else user_data[k]
            for k in rule.get('vars', [])
        }
        return bool(jsonLogic(rule['logic'], processed))