import logging
import os
import tempfile
from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from google.cloud import bigquery
import pytz
from collections import defaultdict
import time

from src.optimization.parallel_processing import parallel_process
from src.utils.time_utils import get_taiwan_date_str
from src.core.cloud_integration import BigQueryClient, StorageManager
from src.utils import logging_setup, split_into_chunks, vectorized_evaluate
from src.utils.format_utils import format_size
from src.config import LOCAL_CACHE_DIR
from src.core import queries
from src.core.cloud_integration import save_user_stats_snapshot, write_to_special_lta
from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

# 設定 logger
logger = logging.getLogger(__name__)

def validate_ec_ids(ec_ids: Optional[List[int]]) -> List[int]:
    """驗證 EC IDs"""
    if not ec_ids:
        return [107]  # 預設值
    if isinstance(ec_ids, (int, str)):
        return [int(ec_ids)]
    if isinstance(ec_ids, list):
        return [int(id_) for id_ in ec_ids]
    raise ValueError(f"Invalid ec_ids format: {ec_ids}")

def process_lta_chunk(chunk, ec_id, current_date, pre_calculated_segments=None):
    """處理 LTA 分塊的獨立函數

    Args:
        chunk: 使用者統計資料
        ec_id: 電商 ID
        current_date: 當前日期
        pre_calculated_segments: 預先計算好的分群結果，如果提供則直接使用而不重新計算

    Returns:
        dict: 處理結果
    """
    if pre_calculated_segments is not None:
        # 如果提供了預先計算好的分群，則直接使用
        from src.core.cloud_integration import write_to_special_lta_with_calculated_data
        return write_to_special_lta_with_calculated_data(chunk, ec_id, current_date, pre_calculated_segments)
    else:
        # 如果沒有提供預先計算好的分群，則使用原始函數
        from src.core.cloud_integration import write_to_special_lta
        return write_to_special_lta(chunk, ec_id, current_date)

def calculate_user_stats(
    ec_ids: List[int],
    date_range: List[datetime],
    should_calculate_stats: bool = True,
    should_save_snapshot: bool = True,
    should_write_lta: bool = True,
    add_to_dxp: bool = True,
    auto_repair_missing_days: bool = True,
    max_repair_days: int = 30,
    should_update_user_stats_table: bool = True,
    skip_time_standardization: bool = False,
    only_standardize_required_columns: bool = True
) -> Dict[str, Any]:
    """
    計算指定電商 ID 的用戶統計資料

    Args:
        ec_id: 電商 ID 列表
        date_range: 時間範圍 [start_time, end_time]
        should_calculate_stats: 是否計算統計資料
        should_save_snapshot: 是否儲存快照
        should_write_lta: 是否寫入 LTA 資料
        add_to_dxp: 是否加入 DXP 處理
        auto_repair_missing_days: 是否自動修復缺失的天數
        max_repair_days: 最多修復多少天（防止處理太長時間）
        should_update_user_stats_table: 是否更新 BigQuery 的 user_stats 表格
        skip_time_standardization: 是否跳過時間標準化（提高性能但可能影響數據一致性）
        only_standardize_required_columns: 是否只標準化必要的時間欄位

    Returns:
        Dict: 包含處理結果的字典
    """
    start_time = date_range[0]
    end_time = date_range[1]

    # 確保時區為 UTC
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=timezone.utc)
    if end_time.tzinfo is None:
        end_time = end_time.replace(tzinfo=timezone.utc)

    # 初始化結果資料
    result = {
        'calculate_stats': False,
        'save_snapshot': False,
        'write_lta': False,
        'update_user_stats': False,  # 新增標記
        'total_cost_usd': 0,
        'total_cost_twd': 0,
        'total_bytes_processed': 0,
        'snapshot_used': False,
        'snapshot_type': None,
        'user_stats_df': None,  # 添加用戶統計資料欄位
        'segment_stats': {},  # 添加動態分群結果欄位
        'repaired_dates': [],  # 添加修復日期欄位
        'days_repaired': 0,  # 添加修復天數欄位
        'daily_segment_stats': {}  # 添加每日分群統計欄位
    }

    try:
        if not should_calculate_stats:
            logger.info("跳過統計資料計算（應使用者要求）")
            return result
        result['calculate_stats'] = True

        # 初始化 BigQuery 客戶端
        client = bigquery.Client()
        total_bytes_processed = 0

        # 對每個電商 ID 進行處理
        for current_ec_id in ec_ids:
            logger.info(f"開始處理電商 ID: {current_ec_id}")

            # 首先嘗試找到最近可用的快照
            snapshot_date, snapshot_path, snapshot_type = find_latest_snapshot(start_time, current_ec_id)
            user_stats = None
            base_stats_df = None

            if snapshot_date is not None:
                logger.info(f"找到{(end_time - snapshot_date).days}天前的快照: {snapshot_path} (來源: {snapshot_type})")
                try:
                    # 載入快照
                    if snapshot_type == "local":
                        base_stats_df = pd.read_parquet(snapshot_path)
                    else:  # "gcs"
                        storage_manager = StorageManager()

                        # 使用與儲存快照時相同的檔名格式
                        taiwan_date_str = get_taiwan_date_str(snapshot_date)
                        local_temp = f"{LOCAL_CACHE_DIR}/user_stats_{current_ec_id}_{taiwan_date_str}.parquet"

                        storage_manager.bucket.blob(snapshot_path).download_to_filename(local_temp)
                        base_stats_df = pd.read_parquet(local_temp)

                    # 如果載入成功，過濾出當前 ec_id 的資料
                    if not base_stats_df.empty:
                        base_stats_df = base_stats_df[base_stats_df['ec_id'] == current_ec_id]
                        if base_stats_df.empty:
                            logger.warning(f"快照中沒有電商 ID {current_ec_id} 的資料")
                        else:
                            logger.info(f"成功從快照載入 {len(base_stats_df)} 筆電商 ID {current_ec_id} 的資料")
                            result['snapshot_used'] = True
                            result['snapshot_type'] = snapshot_type

                            # 如果啟用自動修復功能，則逐日處理從 snapshot_date 到 end_time 的資料
                            if auto_repair_missing_days and base_stats_df is not None and not base_stats_df.empty:
                                logger.info(f"啟用自動修復功能，將逐日處理從 {snapshot_date.date()} 到 {end_time.date()} 的資料")

                                # 計算需要修復的天數
                                days_to_repair = (end_time - snapshot_date).days

                                # 檢查 end_time 是否少於當天的 16:00 UTC (台灣時間 00:00)
                                # 如果是，則需要減少一天。
                                if end_time.hour <= 16 and end_time.minute == 0 and end_time.second == 0:
                                    logger.info(f"end_time 是 {end_time.date()} 的 16:00 UTC (台灣時間 00:00)，需要調整 days_to_repair")
                                    days_to_repair -= 1
                                    logger.info(f"days_to_repair = {days_to_repair}")

                                # 確保 days_to_repair 至少為 0
                                days_to_repair = max(0, days_to_repair)

                                if days_to_repair > max_repair_days:
                                    logger.warning(f"需要修復的天數({days_to_repair})超過最大限制({max_repair_days})，將只修復最近的 {max_repair_days} 天")
                                    days_to_repair = max_repair_days
                                    snapshot_date = end_time - timedelta(days=max_repair_days)

                                if days_to_repair > 0:
                                    # 初始化處理資料的變數
                                    current_stats_df = base_stats_df.copy()

                                    # 從 snapshot_date 的後一天開始處理，而不是從 snapshot_date 開始
                                    # 注意: 使用台灣時間格式化日期，確保log明確表示使用台灣時區
                                    taiwan_date_str = get_taiwan_date_str(snapshot_date)
                                    logger.info(f"將從快照日期後一天開始處理資料，快照日期: {snapshot_date.date()} (台灣日期: {taiwan_date_str})")
                                    current_date = snapshot_date

                                    # 逐日處理資料
                                    for day_offset in range(1, days_to_repair + 1):
                                        day_start = current_date + timedelta(days=1)  # 從快照日期的後一天開始
                                        day_end = day_start + timedelta(days=1)
                                        logger.info(f"處理第 {day_offset} 天 ({day_start.date()} -> {day_end.date()}) 的資料")

                                        # 記錄修復日期 - 使用台灣時間格式
                                        if 'repaired_dates' not in result:
                                            result['repaired_dates'] = []
                                        # 將UTC時間轉換為台灣時間的日期字串，格式為YYYY-MM-DD
                                        taiwan_dt = day_start.astimezone(pytz.timezone('Asia/Taipei'))
                                        taiwan_date_str = taiwan_dt.strftime('%Y-%m-%d')
                                        result['repaired_dates'].append(taiwan_date_str)

                                        # 查詢該天的資料
                                        try:
                                            daily_query = queries.daily_user_stats_query(current_ec_id, day_start, day_end)
                                            logger.debug(f"每日查詢 SQL:\n{daily_query}")
                                            daily_job = client.query(daily_query)
                                            daily_results = daily_job.result()
                                            daily_stats_df = daily_results.to_dataframe()
                                            total_bytes_processed += daily_job.total_bytes_processed

                                            if daily_stats_df.empty:
                                                logger.warning(f"電商 ID {current_ec_id} 在 {day_start.date()} 沒有新資料")
                                            else:
                                                logger.info(f"成功查詢到 {len(daily_stats_df)} 筆電商 ID {current_ec_id} 在 {day_start.date()} 的資料")

                                                # 檢查結果中是否包含互動時間欄位
                                                logger.info(f"每日數據欄位: {daily_stats_df.columns.tolist()}")

                                                # 檢查是否有互動時間欄位
                                                has_first_of_day = 'first_interaction_of_day' in daily_stats_df.columns
                                                has_last_of_day = 'last_interaction_of_day' in daily_stats_df.columns

                                                if not has_first_of_day:
                                                    logger.error("查詢結果中缺少 first_interaction_of_day 欄位，這將導致無法為新用戶設置首次互動時間")
                                                else:
                                                    null_first_count = daily_stats_df['first_interaction_of_day'].isna().sum()
                                                    total_count = len(daily_stats_df)
                                                    null_percentage = (null_first_count / total_count) * 100 if total_count > 0 else 0
                                                    logger.info(f"查詢結果中有 {null_first_count}/{total_count} 筆 first_interaction_of_day 為 NULL ({null_percentage:.2f}%)")

                                                if not has_last_of_day:
                                                    logger.error("查詢結果中缺少 last_interaction_of_day 欄位，這將導致無法為新用戶設置最後互動時間")
                                                else:
                                                    null_last_count = daily_stats_df['last_interaction_of_day'].isna().sum()
                                                    total_count = len(daily_stats_df)
                                                    null_percentage = (null_last_count / total_count) * 100 if total_count > 0 else 0
                                                    logger.info(f"查詢結果中有 {null_last_count}/{total_count} 筆 last_interaction_of_day 為 NULL ({null_percentage:.2f}%)")

                                                # 更新統計資料
                                                current_stats_df = update_user_stats_with_daily_data(current_stats_df, daily_stats_df)

                                                # 對於每一天的統計資料，都儲存對應的快照
                                                if should_save_snapshot:
                                                    # 創建儲存管理器
                                                    storage_manager = StorageManager()

                                                    # 使用 day_end 作為快照日期
                                                    # 因為 day_end 是 UTC 時間，在台灣時區表示的是當天的日期
                                                    # 例如：day_end = 2025-02-28 16:00:00 UTC = 2025-03-01 00:00:00 台灣時間
                                                    # 這樣在台灣時區顯示的才是正確的日期（代表這個快照包含了 2025-02-28 整天的資料）

                                                    # 儲存前標準化所有時間欄位
                                                    logger.info(f"準備儲存快照，資料大小: {len(current_stats_df)} 筆記錄")
                                                    # 使用淺拷貝避免修改原始數據
                                                    snapshot_df = current_stats_df.copy()

                                                    # 使用 save_user_stats_snapshot 函數，傳遞時間標準化選項
                                                    snapshot_path = save_user_stats_snapshot(
                                                        snapshot_df,
                                                        current_ec_id,
                                                        day_end,
                                                        skip_time_standardization=skip_time_standardization,
                                                        only_required_columns=only_standardize_required_columns
                                                    )

                                                    snapshot_result = {'success': bool(snapshot_path), 'path': snapshot_path}
                                                    logger.info(f"已保存 {day_end.date()} 的快照: {snapshot_result}")

                                                    # 只將最後一天計入結果
                                                    if day_offset == days_to_repair:
                                                        result['save_snapshot'] = snapshot_result.get('success', False)
                                                        if snapshot_result.get('success', False):
                                                            logger.info(f"成功儲存最終快照: {snapshot_result.get('path', '')}")
                                                        else:
                                                            logger.error(f"儲存最終快照失敗: {snapshot_result.get('error', '未知錯誤')}")

                                                    # 關鍵修改：將當前日期作為下一天的起點，從快照中讀取數據
                                                    # 這樣可以確保每天的數據處理都是基於已驗證過的快照數據
                                                    if snapshot_result.get('success', False) and day_offset < days_to_repair:
                                                        # 讀取剛剛儲存的快照作為下一天的基礎數據
                                                        logger.info(f"從最新快照 {snapshot_path} 讀取數據作為下一天的基礎")
                                                        try:
                                                            if snapshot_path.startswith('gs://'):
                                                                # 如果是 GCS 路徑，需要先下載
                                                                taiwan_date_str = get_taiwan_date_str(day_end)
                                                                local_temp = f"{LOCAL_CACHE_DIR}/user_stats_{current_ec_id}_{taiwan_date_str}.parquet"

                                                                storage_manager.bucket.blob(snapshot_path.replace('gs://tagtoo-ml-workflow/', '')).download_to_filename(local_temp)
                                                                current_stats_df = pd.read_parquet(local_temp)
                                                                logger.info(f"成功從 GCS 快照讀取 {len(current_stats_df)} 筆記錄")

                                                                # 讀取完成後刪除臨時文件
                                                                if os.path.exists(local_temp):
                                                                    os.remove(local_temp)
                                                            else:
                                                                # 本地文件直接讀取
                                                                current_stats_df = pd.read_parquet(snapshot_path)
                                                                logger.info(f"成功從本地快照讀取 {len(current_stats_df)} 筆記錄")

                                                            # 過濾出當前 ec_id 的數據
                                                            if 'ec_id' in current_stats_df.columns:
                                                                current_stats_df = current_stats_df[current_stats_df['ec_id'] == current_ec_id]

                                                            # 驗證快照數據
                                                            null_first = current_stats_df['first_interaction_time'].isna().sum() if 'first_interaction_time' in current_stats_df.columns else 0
                                                            null_last = current_stats_df['last_interaction_time'].isna().sum() if 'last_interaction_time' in current_stats_df.columns else 0

                                                            if null_first > 0 or null_last > 0:
                                                                logger.warning(f"讀取的快照中有 NULL 值：first_interaction_time={null_first}, last_interaction_time={null_last}")

                                                        except Exception as e:
                                                            logger.error(f"從快照讀取數據失敗: {str(e)}")
                                                            logger.exception("詳細錯誤堆疊")
                                                            # 發生錯誤時繼續使用當前數據
                                                            logger.warning("將繼續使用內存中的數據進行處理")

                                                # 寫入 LTA
                                                if should_write_lta:
                                                    # 即使不是最後一天，也計算分群統計資料
                                                    daily_segments = calculate_audience_segments_dynamic(current_stats_df, current_ec_id, add_to_dxp)
                                                    # 計算分群統計資訊
                                                    segment_counts = {segment_id: len(permanents) for segment_id, permanents in daily_segments.items()}
                                                    segments_per_user = defaultdict(int)
                                                    user_segment_counts = defaultdict(int)

                                                    # 計算每個用戶屬於幾個分群
                                                    for segment_id, permanents in daily_segments.items():
                                                        for permanent in permanents:
                                                            user_segment_counts[permanent] += 1

                                                    # 統計每種分群數量的用戶數
                                                    for user, count in user_segment_counts.items():
                                                        segments_per_user[f"{count}_個分群"] += 1

                                                    # 構建每日分群統計資料
                                                    daily_stats = {
                                                        'segment_counts': segment_counts,
                                                        'segments_per_user': dict(segments_per_user),
                                                        'total_users': len(user_segment_counts)
                                                    }
                                                    result['segment_stats'].update(daily_stats)

                                                    # 存儲每日統計資料，以台灣日期為鍵
                                                    # 將 UTC 時間轉換為台灣時間
                                                    taiwan_dt = day_start.astimezone(pytz.timezone('Asia/Taipei'))
                                                    taiwan_date_str = taiwan_dt.strftime('%Y-%m-%d')
                                                    result['daily_segment_stats'][taiwan_date_str] = daily_stats
                                                    logger.info(f"已計算 {taiwan_date_str} 的分群統計資料: {len(segment_counts)} 個分群，{len(user_segment_counts)} 個用戶")

                                                    # 使用 write_to_special_lta_with_calculated_data 直接使用已計算好的分群資料，避免重複計算
                                                    from src.core.cloud_integration import write_to_special_lta_with_calculated_data
                                                    lta_result = write_to_special_lta_with_calculated_data(
                                                        current_stats_df,
                                                        current_ec_id,
                                                        day_start,
                                                        daily_segments,
                                                        add_to_dxp
                                                    )
                                                    # 更新結果
                                                    result['write_lta'] = bool(lta_result.get('success', False))
                                                    if 'cost_usd' in lta_result:
                                                        result['total_cost_usd'] += lta_result['cost_usd']
                                                    if 'cost_twd' in lta_result:
                                                        result['total_cost_twd'] += lta_result['cost_twd']
                                                    if 'bytes_processed' in lta_result:
                                                        result['total_bytes_processed'] += lta_result['bytes_processed']

                                        except Exception as e:
                                            logger.error(f"處理電商 ID {current_ec_id} 日期 {day_start.date()} 時發生錯誤: {str(e)}")
                                            logger.exception("詳細錯誤堆疊")
                                            # 不要中斷循環，繼續處理下一天
                                            continue

                                        # 更新當前日期
                                        current_date = day_start  # 更新為剛處理的日期

                                    # 設置修復天數
                                    result['days_repaired'] = days_to_repair
                                    logger.info(f"已修復 {days_to_repair} 天的資料")

                                    # 設置最終的使用者統計資料
                                    if 'user_stats_df' not in result or result['user_stats_df'] is None:
                                        result['user_stats_df'] = current_stats_df

                                    # 更新 BigQuery 中的 user_stats 表格
                                    # 這裡移動到迭代外部，和 save_snapshot 相同層級
                                    if should_update_user_stats_table:
                                        try:
                                            logger.info(f"開始更新 BigQuery 的 user_stats 表格")
                                            bigquery_client = BigQueryClient()
                                            bigquery_client.update_user_stats_table(
                                                current_stats_df,
                                                current_ec_id,
                                                end_time,
                                                skip_time_standardization=skip_time_standardization,
                                                only_required_columns=only_standardize_required_columns
                                            )
                                            result['update_user_stats'] = True
                                            logger.info(f"成功更新 BigQuery 的 user_stats 表格")
                                        except Exception as e:
                                            logger.error(f"更新 BigQuery 表格時發生錯誤: {str(e)}")
                                            logger.exception("詳細錯誤堆疊")
                                            result['update_user_stats'] = False
                    else:
                        logger.warning(f"載入的快照為空")
                except Exception as e:
                    logger.error(f"載入快照時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    base_stats_df = None
        return result
    except Exception as e:
        logger.error(f"計算使用者統計資料時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        # 不再拋出異常，而是返回結果
        result['error'] = str(e)
        return result

def process_user_stats_chunk(chunk: pd.DataFrame, rules: dict) -> Dict[str, List[str]]:
    """處理使用者統計資料的分塊"""
    return vectorized_evaluate(chunk, rules)

def validate_snapshot(df: pd.DataFrame) -> bool:
    """驗證 snapshot 的資料是否正確

    Args:
        df: 要驗證的 DataFrame

    Returns:
        bool: 驗證是否通過
    """
    logger = logging.getLogger(__name__)
    import time
    validation_start = time.time()

    if df is None or df.empty:
        logger.error("Snapshot 是空的")
        return False

    logger.info(f"開始驗證 Snapshot，資料筆數: {len(df)}")

    # 檢查必要欄位是否存在
    cols_check_start = time.time()
    required_cols = [
        'ec_id', 'permanent', 'first_interaction_time', 'last_interaction_time',
        'total_sessions', 'purchase_count', 'total_purchase_amount'
    ]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"Snapshot 缺少必要欄位: {missing_cols}")
        return False
    logger.info(f"欄位存在性檢查完成，耗時: {time.time() - cols_check_start:.2f} 秒")

    # 檢查 NULL 值
    null_check_start = time.time()
    required_non_null_cols = [
        'ec_id', 'permanent', 'first_interaction_time', 'last_interaction_time'
    ]
    # purchase_count、total_purchase_amount 和 total_sessions 允許包含 NULL 值，可以在後續處理中轉換為 0
    null_counts = {col: df[col].isna().sum() for col in required_non_null_cols}
    if any(null_counts.values()):
        logger.error(f"Snapshot 包含 NULL 值: {null_counts}")
        return False

    # 單獨檢查並警告 purchase_count、total_purchase_amount 和 total_sessions 的 NULL 值，但不導致驗證失敗
    value_null_counts = {
        'purchase_count': df['purchase_count'].isna().sum(),
        'total_purchase_amount': df['total_purchase_amount'].isna().sum(),
        'total_sessions': df['total_sessions'].isna().sum()
    }
    if any(value_null_counts.values()):
        logger.warning(f"Snapshot 包含可轉換的 NULL 值: {value_null_counts}")

    logger.info(f"NULL 值檢查完成，耗時: {time.time() - null_check_start:.2f} 秒")

    # 檢查資料型別
    type_check_start = time.time()
    expected_types = {
        'ec_id': ['int64', 'int32', 'Float64', 'float64', 'Int64'],  # 增加 Int64 以兼容新版 pandas
        'permanent': ['object', 'string'],
        'first_interaction_time': ['datetime64[ns]', 'datetime64[ns, UTC]', 'object', 'datetime64[us, UTC]'],  # 增加 datetime64[us, UTC] 微秒精度支援
        'last_interaction_time': ['datetime64[ns]', 'datetime64[ns, UTC]', 'object', 'datetime64[us, UTC]'],  # 增加 datetime64[us, UTC] 微秒精度支援
        'total_sessions': ['int64', 'int32', 'Int64'],
        'purchase_count': ['int64', 'int32', 'Int64', 'UInt16'],  # 增加 UInt16 兼容優化後的類型
        'total_purchase_amount': ['float64', 'float32']  # 增加 float32 兼容優化後的類型
    }

    for col, expected_type in expected_types.items():
        current_dtype = str(df[col].dtype)
        if current_dtype not in expected_type:
            # 特殊處理可以自動轉換的情況
            if col in ['total_sessions', 'purchase_count'] and current_dtype.startswith('float'):
                logger.warning(f"欄位 {col} 為浮點類型 {current_dtype}，嘗試轉換為整數類型")
                try:
                    # 檢查是否有 NaN 值
                    na_count = df[col].isna().sum()
                    if na_count > 0:
                        logger.warning(f"欄位 {col} 包含 {na_count} 個 NULL 值，將填充為 0")
                        df[col] = df[col].fillna(0)

                    # 檢查是否有小數部分
                    has_decimals = (df[col] % 1 != 0).any()
                    if has_decimals:
                        logger.warning(f"欄位 {col} 包含小數部分，將進行截斷")

                    # 轉換為 int64
                    df[col] = df[col].astype('int64')
                    logger.info(f"成功將欄位 {col} 轉換為 {df[col].dtype}")
                    continue
                except Exception as e:
                    logger.error(f"轉換欄位 {col} 類型失敗: {str(e)}")
                    return False

            # 特殊處理 Int64 類型
            if col in ['ec_id', 'purchase_count', 'total_sessions'] and isinstance(df[col].dtype, pd.Int64Dtype):
                continue

            logger.error(f"欄位 {col} 的資料型別不正確: 預期是 {expected_type}，實際是 {df[col].dtype}")
            return False
    logger.info(f"資料型別檢查完成，耗時: {time.time() - type_check_start:.2f} 秒")

    # 檢查時間欄位的格式和時區
    time_check_start = time.time()
    logger.info("開始檢查時間欄位格式和時區...")
    time_cols = ['first_interaction_time', 'last_interaction_time']
    for col in time_cols:
        # 檢查時區資訊或字串格式的時間
        if df[col].dtype in ['datetime64[ns]', 'datetime64[ns, UTC]', 'datetime64[us, UTC]']:  # 增加對微秒精度的支援
            # 對於 datetime 類型，檢查時區資訊
            if not all(t.tzinfo is not None for t in df[col] if pd.notna(t)):
                logger.error(f"欄位 {col} 包含沒有時區資訊的時間")
                return False
        elif df[col].dtype == 'object':
            # 對於字串類型，檢查是否為有效的 ISO 格式
            logger.info(f"欄位 {col} 是字串類型，檢查是否為有效的 ISO 格式")
            # 只抽樣檢查前 5 個非空值，避免全部檢查消耗太多時間
            non_null_values = df[col].dropna().head(5)
            for val in non_null_values:
                try:
                    # 嘗試解析字串，檢查其格式
                    if isinstance(val, str):
                        dt = pd.to_datetime(val)
                except Exception as e:
                    logger.error(f"欄位 {col} 包含無效的時間字串格式: {val}, 錯誤: {str(e)}")
                    return False

        # 不再對每個值進行檢查，因為對於大型資料集這會非常耗時
        # 只會對 datetime 類型的欄位進行檢查
        if df[col].dtype in ['datetime64[ns]', 'datetime64[ns, UTC]', 'datetime64[us, UTC]']:  # 增加對微秒精度的支援
            try:
                for t in df[col].head(5):  # 只檢查前 5 個值
                    if pd.notna(t):
                        if isinstance(t, str):
                            # 嘗試解析字串格式的時間
                            pd.to_datetime(t)
                        elif not isinstance(t, (pd.Timestamp, datetime)):
                            raise ValueError(f"時間值 {t} 不是有效的時間格式")
            except Exception as e:
                logger.error(f"欄位 {col} 包含無效的時間格式: {str(e)}")
                return False
    logger.info(f"時間欄位格式和時區檢查完成，耗時: {time.time() - time_check_start:.2f} 秒")

    # 檢查時間範圍
    range_check_start = time.time()
    for col in time_cols:
        if df[col].dtype in ['datetime64[ns]', 'datetime64[ns, UTC]', 'datetime64[us, UTC]']:  # 增加對微秒精度的支援
            # 對於 datetime 類型
            min_time = df[col].min()
            max_time = df[col].max()
            if min_time.year < 2000 or max_time.year > 2100:
                logger.error(f"欄位 {col} 的時間範圍不合理: {min_time} ~ {max_time}")
                return False

            # 檢查是否有未來時間
            current_time = datetime.now(pytz.UTC)
            future_threshold = current_time + timedelta(days=1)
            future_times = df[df[col] > future_threshold]
            if not future_times.empty:
                logger.error(f"欄位 {col} 包含未來時間: {future_times[col].iloc[0]}")
                return False
        elif df[col].dtype == 'object':
            # 對於字串類型的時間，抽樣檢查
            logger.info(f"欄位 {col} 是字串類型，只進行輕量檢查")
            # 只抽樣檢查前後幾條記錄，避免全部轉換消耗太多時間
            try:
                # 取前 5 個和後 5 個非空值
                sample_times = pd.concat([
                    df[col].dropna().head(5),
                    df[col].dropna().tail(5)
                ]).reset_index(drop=True)

                # 轉換為 datetime 以進行範圍檢查
                converted_times = pd.to_datetime(sample_times)

                # 簡單檢查時間範圍 (2000 年到 2100 年)
                years = converted_times.dt.year
                if years.min() < 2000 or years.max() > 2100:
                    logger.error(f"欄位 {col} 的時間範圍不合理，包含過早或過晚的年份")
                    return False

                # 檢查是否有未來時間
                current_time = datetime.now(pytz.UTC)
                future_times = converted_times[converted_times > current_time + timedelta(days=1)]
                if not future_times.empty:
                    logger.error(f"欄位 {col} 可能包含未來時間")
                    return False

            except Exception as e:
                logger.warning(f"欄位 {col} 轉換或檢查時間範圍時出錯: {str(e)} - 跳過詳細檢查")
                # 不返回 False，允許繼續
    logger.info(f"時間範圍檢查完成，耗時: {time.time() - range_check_start:.2f} 秒")

    # 檢查數值欄位的合理性
    numeric_check_start = time.time()
    logger.info("開始檢查數值欄位的合理性...")
    if (df['total_sessions'].fillna(0) < 0).any():
        logger.error("發現負數的 total_sessions")
        return False

    if (df['purchase_count'].fillna(0) < 0).any():
        logger.error("發現負數的 purchase_count")
        return False

    if (df['total_purchase_amount'].fillna(0) < 0).any():
        logger.error("發現負數的 total_purchase_amount")
        return False
    logger.info(f"數值欄位合理性檢查完成，耗時: {time.time() - numeric_check_start:.2f} 秒")

    # 檢查 first_interaction_time 是否晚於 last_interaction_time
    time_order_check_start = time.time()
    logger.info("開始檢查時間順序...")
    invalid_time = (df['first_interaction_time'] > df['last_interaction_time']).sum()
    if invalid_time > 0:
        logger.error(f"發現 {invalid_time} 筆記錄的 first_interaction_time 晚於 last_interaction_time")
        return False
    logger.info(f"時間順序檢查完成，耗時: {time.time() - time_order_check_start:.2f} 秒")

    # 檢查時間欄位的一致性
    time_consistency_start = time.time()
    logger.info("開始檢查時間欄位的一致性...")

    # 優化：使用向量化操作替代 apply，大幅提升效能
    first_notna = pd.notna(df['first_interaction_time'])
    last_notna = pd.notna(df['last_interaction_time'])

    # 檢查是否有空值
    if not (first_notna.all() and last_notna.all()):
        null_first = (~first_notna).sum()
        null_last = (~last_notna).sum()
        logger.error(f"時間欄位包含空值 - first_interaction_time: {null_first}, last_interaction_time: {null_last}")
        return False

    # 檢查類型一致性：只需要檢查 dtype 即可，無需逐行檢查
    first_dtype = df['first_interaction_time'].dtype
    last_dtype = df['last_interaction_time'].dtype

    if first_dtype != last_dtype:
        logger.error(f"時間欄位的型別不一致 - first_interaction_time: {first_dtype}, last_interaction_time: {last_dtype}")
        return False

    logger.info(f"時間欄位一致性檢查完成，耗時: {time.time() - time_consistency_start:.2f} 秒")

    # 所有檢查通過
    total_validation_time = time.time() - validation_start
    logger.info(f"Snapshot 驗證通過，總耗時: {total_validation_time:.2f} 秒")
    return True

def find_latest_snapshot(target_date, ec_id=None, max_days_back=30):
    """尋找最近的可用暫存檔，最多往回查找30天

    Args:
        target_date: 目標日期
        ec_id: 電商 ID，如果提供則查找特定電商的快照
        max_days_back: 最多向前查找的天數

    Returns:
        (最近可用的日期, 暫存檔路徑, 來源類型('local'/'gcs'))
        如果找不到暫存檔，返回 (None, None, None)
    """
    import os
    import pandas as pd
    from src.core.cloud_integration import StorageManager
    import time

    logger.info(f"尋找 {target_date.strftime('%Y-%m-%d')} 最近的可用暫存檔，最多向前 {max_days_back} 天")
    storage_manager = StorageManager()
    start_time = time.time()

    def validate_snapshot_time_fields(df):
        """驗證快照中的時間欄位"""
        validation_start = time.time()
        time_cols = ['first_interaction_time', 'last_interaction_time']

        # 確保時間欄位存在且不為空
        for col in time_cols:
            if col not in df.columns:
                logger.error(f"快照缺少時間欄位: {col}")
                return False
            if df[col].isna().any():
                null_count = df[col].isna().sum()
                total_count = len(df)
                logger.error(f"快照中的 {col} 包含 {null_count}/{total_count} 筆 NULL 值 ({(null_count/total_count)*100:.2f}%)")
                return False

        # 嘗試將時間欄位轉換為 datetime 格式
        try:
            for col in time_cols:
                if df[col].dtype == 'object':
                    df[col] = pd.to_datetime(df[col])

                # 確保時區資訊
                if not all(t.tzinfo is not None for t in df[col] if pd.notna(t)):
                    logger.error(f"快照中的 {col} 包含沒有時區資訊的時間")
                    return False

                # 檢查數據類型 (增加對微秒精度的支援)
                valid_dtypes = ['datetime64[ns]', 'datetime64[ns, UTC]', 'datetime64[us, UTC]']
                if not any(str(df[col].dtype).startswith(dt) for dt in valid_dtypes):
                    logger.error(f"欄位 {col} 的資料型別不正確: 預期是 {valid_dtypes}，實際是 {df[col].dtype}")
                    return False
        except Exception as e:
            logger.error(f"轉換時間欄位時發生錯誤: {str(e)}")
            return False

        validation_time = time.time() - validation_start
        logger.info(f"時間欄位驗證完成，耗時: {validation_time:.2f} 秒")
        return True

    # 遍歷過去的日期，從最近的開始
    for days_back in range(1, max_days_back + 1):
        day_start_time = time.time()
        check_date = target_date - timedelta(days=days_back)
        check_date_str = get_taiwan_date_str(check_date)
        logger.info(f"檢查日期: {check_date_str} (第 {days_back}/{max_days_back} 天)")

        # 初始化 local_path 為 None
        local_path = None

        # 檢查本地快取 - 如果有提供 ec_id 則包含在檔案名稱中
        if ec_id is not None:
            local_path = f"{LOCAL_CACHE_DIR}/user_stats_{ec_id}_{check_date_str}.parquet"
        else:
            local_path = f"{LOCAL_CACHE_DIR}/user_stats_{check_date_str}.parquet"

        if local_path and os.path.exists(local_path):
            logger.info(f"找到本地暫存檔: {local_path}")
            # 本地暫存檔
            logger.info(f"找到本地暫存檔: {local_path}")
            try:
                import pandas as pd
                df = pd.read_parquet(local_path)
                logger.info(f"本地暫存檔讀取完成，共 {len(df)} 筆記錄，耗時: {time.time() - start_time:.2f} 秒")

                # 填充可轉換的 NULL 值
                if 'total_sessions' in df.columns and df['total_sessions'].isna().any():
                    null_count = df['total_sessions'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 total_sessions NULL 值為 0")
                    df['total_sessions'] = df['total_sessions'].fillna(0)

                if 'purchase_count' in df.columns and df['purchase_count'].isna().any():
                    null_count = df['purchase_count'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 purchase_count NULL 值為 0")
                    df['purchase_count'] = df['purchase_count'].fillna(0)

                if 'total_purchase_amount' in df.columns and df['total_purchase_amount'].isna().any():
                    null_count = df['total_purchase_amount'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 total_purchase_amount NULL 值為 0.0")
                    df['total_purchase_amount'] = df['total_purchase_amount'].fillna(0.0)

                # 嘗試轉換 ec_id 類型，增加兼容性
                if 'ec_id' in df.columns:
                    if str(df['ec_id'].dtype).startswith('float') or str(df['ec_id'].dtype) == 'Float64':
                        logger.info(f"本地暫存檔中 ec_id 為浮點類型 ({df['ec_id'].dtype})，轉換為 int64")
                        try:
                            if df['ec_id'].isna().any():
                                logger.error(f"ec_id 欄位包含 NULL 值，無法使用此暫存檔")
                                raise ValueError("ec_id 欄位不允許有 NULL 值")

                            # 轉換為 int64
                            df['ec_id'] = df['ec_id'].astype('int64')
                            logger.info(f"轉換完成，ec_id 現在類型為: {df['ec_id'].dtype}")
                        except Exception as e:
                            logger.error(f"轉換 ec_id 類型時出錯: {str(e)}")
                            return None, None, None

                # 驗證時間欄位
                if validate_snapshot_time_fields(df):
                    validation_start = time.time()
                    if validate_snapshot(df):
                        logger.info(f"本地暫存檔完整驗證通過，耗時: {time.time() - validation_start:.2f} 秒")
                        return check_date, local_path, 'local'
                    else:
                        logger.warning(f"本地暫存檔完整驗證失敗: {local_path}，耗時: {time.time() - validation_start:.2f} 秒")
                else:
                    logger.warning(f"本地暫存檔時間欄位驗證失敗: {local_path}")
            except Exception as e:
                logger.error(f"讀取本地暫存檔時發生錯誤: {str(e)}")

        # 檢查GCS快照
        gcs_check_start = time.time()
        base_path = "LTA/user_stats_snapshots"
        if ec_id is not None:
            # 使用新格式：按電商ID分類
            gcs_path = f"{base_path}/ec_{ec_id}/{check_date_str}.parquet"
        else:
            # 向後兼容舊格式
            gcs_path = f"{base_path}/user_stats_{check_date_str}.parquet"

        logger.info(f"開始檢查 GCS 暫存檔是否存在: gs://tagtoo-ml-workflow/{gcs_path}")
        blob = storage_manager.bucket.blob(gcs_path)

        blob_exists_check = time.time()
        exists = blob.exists()
        logger.info(f"GCS 檔案檢查耗時: {time.time() - blob_exists_check:.2f} 秒")

        if exists:
            logger.info(f"找到 GCS 暫存檔: gs://tagtoo-ml-workflow/{gcs_path}")
            # 下載並驗證 GCS 快照
            download_start = time.time()
            try:
                temp_local_path = f"/tmp/temp_snapshot_{check_date_str}.parquet"
                logger.info(f"開始從 GCS 下載暫存檔至: {temp_local_path}")
                blob.download_to_filename(temp_local_path)
                logger.info(f"GCS 暫存檔下載完成，耗時: {time.time() - download_start:.2f} 秒")

                read_start = time.time()
                logger.info(f"開始讀取下載的暫存檔...")
                temp_df = pd.read_parquet(temp_local_path)
                logger.info(f"GCS 暫存檔讀取完成，共 {len(temp_df)} 筆記錄，耗時: {time.time() - read_start:.2f} 秒")

                # 填充可轉換的 NULL 值
                if 'total_sessions' in temp_df.columns and temp_df['total_sessions'].isna().any():
                    null_count = temp_df['total_sessions'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 total_sessions NULL 值為 0")
                    temp_df['total_sessions'] = temp_df['total_sessions'].fillna(0)

                if 'purchase_count' in temp_df.columns and temp_df['purchase_count'].isna().any():
                    null_count = temp_df['purchase_count'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 purchase_count NULL 值為 0")
                    temp_df['purchase_count'] = temp_df['purchase_count'].fillna(0)

                if 'total_purchase_amount' in temp_df.columns and temp_df['total_purchase_amount'].isna().any():
                    null_count = temp_df['total_purchase_amount'].isna().sum()
                    logger.info(f"填充 {null_count} 筆 total_purchase_amount NULL 值為 0.0")
                    temp_df['total_purchase_amount'] = temp_df['total_purchase_amount'].fillna(0.0)

                # 嘗試轉換 ec_id 類型，增加兼容性
                if 'ec_id' in temp_df.columns:
                    if str(temp_df['ec_id'].dtype).startswith('float') or str(temp_df['ec_id'].dtype) == 'Float64':
                        logger.info(f"GCS 暫存檔中 ec_id 為浮點類型 ({temp_df['ec_id'].dtype})，轉換為 int64")
                        try:
                            if temp_df['ec_id'].isna().any():
                                logger.error(f"ec_id 欄位包含 NULL 值，無法使用此暫存檔")
                                os.remove(temp_local_path)
                                continue

                            # 轉換為 int64
                            temp_df['ec_id'] = temp_df['ec_id'].astype('int64')
                            logger.info(f"轉換完成，ec_id 現在類型為: {temp_df['ec_id'].dtype}")
                        except Exception as e:
                            logger.error(f"轉換 ec_id 類型時出錯: {str(e)}")
                            os.remove(temp_local_path)
                            continue

                # 先驗證時間欄位
                gcs_validation_start = time.time()
                logger.info(f"開始驗證 GCS 暫存檔時間欄位...")
                if not validate_snapshot_time_fields(temp_df):
                    logger.warning(f"GCS 暫存檔時間欄位驗證失敗: {gcs_path}")
                    os.remove(temp_local_path)
                    continue
                logger.info(f"GCS 暫存檔時間欄位驗證通過，耗時: {time.time() - gcs_validation_start:.2f} 秒")

                # 完整驗證
                gcs_full_val_start = time.time()
                logger.info(f"開始完整驗證 GCS 暫存檔...")
                if validate_snapshot(temp_df):
                    logger.info(f"GCS 暫存檔完整驗證通過，耗時: {time.time() - gcs_full_val_start:.2f} 秒")
                    logger.info(f"找到並驗證通過的 GCS 暫存檔: {check_date_str}" + (f" (ec_id: {ec_id})" if ec_id else ""))
                    os.remove(temp_local_path)
                    total_time = time.time() - start_time
                    logger.info(f"快照搜尋及驗證總耗時: {total_time:.2f} 秒")
                    return check_date, gcs_path, "gcs"
                else:
                    logger.warning(f"GCS 暫存檔完整驗證失敗: {gcs_path}，耗時: {time.time() - gcs_full_val_start:.2f} 秒")
                os.remove(temp_local_path)
            except Exception as e:
                logger.error(f"讀取 GCS 暫存檔時發生錯誤: {str(e)}")
                if os.path.exists(temp_local_path):
                    os.remove(temp_local_path)
        else:
            logger.info(f"GCS 暫存檔不存在: {gcs_path}")

        day_time = time.time() - day_start_time
        logger.info(f"檢查日期 {check_date_str} 完成，耗時: {day_time:.2f} 秒")

    # 都找不到
    total_time = time.time() - start_time
    logger.warning(f"過去{max_days_back}天內沒有找到任何可用暫存檔" + (f" (ec_id: {ec_id})" if ec_id else "") + f"，總耗時: {total_time:.2f} 秒")
    return None, None, None

def update_user_stats_with_daily_data(base_stats: pd.DataFrame, daily_stats: pd.DataFrame, use_polars: bool = True) -> pd.DataFrame:
    """更新使用者統計資料，合併基礎資料和每日資料

    Args:
        base_stats (pd.DataFrame): 基礎統計資料
        daily_stats (pd.DataFrame): 每日統計資料
        use_polars (bool): 是否使用 Polars 高效能實現（預設啟用）

    Returns:
        pd.DataFrame: 更新後的統計資料
    """
    logger = logging.getLogger(__name__)

    if use_polars:
        logger.info("使用 Polars 高效能實現")
        return _update_user_stats_with_daily_data_polars(base_stats, daily_stats)
    else:
        logger.info("使用 Pandas 標準實現")
        return _update_user_stats_with_daily_data_pandas(base_stats, daily_stats)


def _update_user_stats_with_daily_data_pandas(base_stats: pd.DataFrame, daily_stats: pd.DataFrame) -> pd.DataFrame:
    """Pandas 實現：更新使用者統計資料，合併基礎資料和每日資料

    Args:
        base_stats (pd.DataFrame): 基礎統計資料
        daily_stats (pd.DataFrame): 每日統計資料

    Returns:
        pd.DataFrame: 更新後的統計資料
    """
    logger = logging.getLogger(__name__)

    # 檢查輸入資料
    if base_stats is None or base_stats.empty:
        logger.warning("基礎統計資料為空，將只使用每日資料")
        if daily_stats is None or daily_stats.empty:
            return pd.DataFrame()

        # 新增：對於只有每日資料的情況，仍需確保時間欄位不為 NULL
        # 創建新的 DataFrame 來儲存結果
        result = daily_stats.copy()

        # 確保關鍵時間欄位存在
        if 'first_interaction_time' not in result.columns:
            result['first_interaction_time'] = None
        if 'last_interaction_time' not in result.columns:
            result['last_interaction_time'] = None

        # 建立統計欄位
        if 'purchase_count' not in result.columns:
            if 'daily_purchase_count' in result.columns:
                result['purchase_count'] = result['daily_purchase_count'].fillna(0).astype(pd.Int64Dtype())
            else:
                result['purchase_count'] = pd.Series(0, index=result.index, dtype=pd.Int64Dtype())

        if 'total_purchase_amount' not in result.columns:
            if 'daily_purchase_amount' in result.columns:
                result['total_purchase_amount'] = result['daily_purchase_amount'].fillna(0.0).astype('float64')
            else:
                result['total_purchase_amount'] = pd.Series(0.0, index=result.index, dtype='float64')

        if 'total_sessions' not in result.columns:
            if 'daily_sessions' in result.columns:
                result['total_sessions'] = result['daily_sessions']
            else:
                result['total_sessions'] = 0

        # 使用每日資料填充
        null_first_mask = result['first_interaction_time'].isna()
        if 'first_interaction_of_day' in daily_stats.columns:
            result.loc[null_first_mask, 'first_interaction_time'] = result.loc[null_first_mask, 'first_interaction_of_day']

        null_last_mask = result['last_interaction_time'].isna()
        if 'last_interaction_of_day' in daily_stats.columns:
            result.loc[null_last_mask, 'last_interaction_time'] = result.loc[null_last_mask, 'last_interaction_of_day']

        # 檢查是否仍有 NULL 值
        if result['first_interaction_time'].isna().any():
            raise ValueError(f"發現 {result['first_interaction_time'].isna().sum()} 筆記錄的 first_interaction_time 為 NULL")

        if result['last_interaction_time'].isna().any():
            raise ValueError(f"發現 {result['last_interaction_time'].isna().sum()} 筆記錄的 last_interaction_time 為 NULL")

        return result

    if daily_stats is None or daily_stats.empty:
        logger.warning("每日統計資料為空，將只使用基礎資料")
        return base_stats

    # 檢查每日數據中的互動時間欄位
    logger.info("檢查每日數據的互動時間欄位")

    # 檢查並輸出 daily_stats 的欄位
    logger.debug(f"每日數據的欄位: {daily_stats.columns.tolist()}")

    # 檢查 first_interaction_of_day 欄位
    has_first_of_day = 'first_interaction_of_day' in daily_stats.columns

    # 也檢查 first_interaction_time 欄位（因為可能已經在查詢中改名）
    has_first_interaction_time_in_daily = 'first_interaction_time' in daily_stats.columns

    if not has_first_of_day and not has_first_interaction_time_in_daily:
        logger.error("每日數據中缺少互動時間欄位，這將導致無法為新用戶設置首次互動時間")

    # 檢查 last_interaction_of_day 欄位
    has_last_of_day = 'last_interaction_of_day' in daily_stats.columns

    # 也檢查 last_interaction_time 欄位
    has_last_interaction_time_in_daily = 'last_interaction_time' in daily_stats.columns

    if not has_last_of_day and not has_last_interaction_time_in_daily:
        logger.error("每日數據中缺少互動時間欄位，這將導致無法為新用戶設置最後互動時間")

    # 檢查 NULL 值情況
    if has_first_of_day:
        null_first_count = daily_stats['first_interaction_of_day'].isna().sum()
        total_count = len(daily_stats)
        null_percentage = (null_first_count / total_count) * 100 if total_count > 0 else 0
        logger.info(f"每日數據中有 {null_first_count}/{total_count} 筆 first_interaction_of_day 為 NULL ({null_percentage:.2f}%)")
    elif has_first_interaction_time_in_daily:
        null_first_count = daily_stats['first_interaction_time'].isna().sum()
        total_count = len(daily_stats)
        null_percentage = (null_first_count / total_count) * 100 if total_count > 0 else 0
        logger.info(f"每日數據中有 {null_first_count}/{total_count} 筆 first_interaction_time 為 NULL ({null_percentage:.2f}%)")

    if has_last_of_day:
        null_last_count = daily_stats['last_interaction_of_day'].isna().sum()
        total_count = len(daily_stats)
        null_percentage = (null_last_count / total_count) * 100 if total_count > 0 else 0
        logger.info(f"每日數據中有 {null_last_count}/{total_count} 筆 last_interaction_of_day 為 NULL ({null_percentage:.2f}%)")
    elif has_last_interaction_time_in_daily:
        null_last_count = daily_stats['last_interaction_time'].isna().sum()
        total_count = len(daily_stats)
        null_percentage = (null_last_count / total_count) * 100 if total_count > 0 else 0
        logger.info(f"每日數據中有 {null_last_count}/{total_count} 筆 last_interaction_time 為 NULL ({null_percentage:.2f}%)")

    # 顯示每日數據中的幾個範例記錄來方便調試
    sample_size = min(5, len(daily_stats))
    if sample_size > 0:
        logger.debug(f"每日數據範例（{sample_size}筆）:")
        sample_records = daily_stats.sample(sample_size)
        for i, record in sample_records.iterrows():
            logger.debug(f"記錄 {i}:\n{record}")

    # 保存原有的 first_interaction_time
    original_first_interaction = {}
    if 'first_interaction_time' in base_stats.columns:
        for idx, row in base_stats.iterrows():
            key = (row['ec_id'], row['permanent'])
            if pd.notna(row['first_interaction_time']):
                original_first_interaction[key] = row['first_interaction_time']

    # 保存原有的 last_interaction_time
    original_last_interaction = {}
    if 'last_interaction_time' in base_stats.columns:
        for idx, row in base_stats.iterrows():
            key = (row['ec_id'], row['permanent'])
            if pd.notna(row['last_interaction_time']):
                original_last_interaction[key] = row['last_interaction_time']

    # 確保 permanent 欄位的類型一致
    base_stats['permanent'] = base_stats['permanent'].astype(str)
    daily_stats['permanent'] = daily_stats['permanent'].astype(str)

    # 合併前統計
    before_merge_stats = {
        'total_records': len(base_stats),
        'null_first_interaction': base_stats['first_interaction_time'].isna().sum() if 'first_interaction_time' in base_stats.columns else 0,
        'null_last_interaction': base_stats['last_interaction_time'].isna().sum() if 'last_interaction_time' in base_stats.columns else 0
    }
    logger.info(f"合併前統計: {before_merge_stats}")

    # 改進：在合併前過濾 base_stats 中的臨時欄位
    # 識別 base_stats 中的臨時欄位（帶有 _daily 後綴的欄位）
    temp_columns = [col for col in base_stats.columns if col.endswith('_daily')]
    if temp_columns:
        logger.info(f"在合併前從 base_stats 中移除以下臨時欄位: {temp_columns}")
        base_stats_clean = base_stats.drop(columns=temp_columns, errors='ignore')
    else:
        logger.debug("在 base_stats 中未發現臨時欄位")
        base_stats_clean = base_stats

    # 合併資料
    merged_stats = pd.merge(
        base_stats_clean,
        daily_stats,
        on=['ec_id', 'permanent'],
        how='outer',  # 使用 outer join 以包含新的使用者
        suffixes=('', '_daily')
    )

    # 輸出合併後的欄位，以便調試
    logger.debug(f"合併後的所有欄位: {merged_stats.columns.tolist()}")

    # 處理 first_interaction_time
    if 'first_interaction_time' not in merged_stats.columns:
        merged_stats['first_interaction_time'] = None

    # 處理 last_interaction_time
    if 'last_interaction_time' not in merged_stats.columns:
        merged_stats['last_interaction_time'] = None

    # 確定互動時間欄位的正確名稱（考慮合併後可能的重命名）
    first_interaction_daily_field = None
    last_interaction_daily_field = None

    # 檢查每種可能的欄位名稱 - 優先使用帶有 _daily 後綴的欄位
    for field in ['first_interaction_of_day_daily', 'first_interaction_time_daily', 'first_interaction_of_day']:
        if field in merged_stats.columns:
            first_interaction_daily_field = field
            logger.debug(f"找到首次互動時間欄位: {field}")
            break

    for field in ['last_interaction_of_day_daily', 'last_interaction_time_daily', 'last_interaction_of_day']:
        if field in merged_stats.columns:
            last_interaction_daily_field = field
            logger.debug(f"找到最後互動時間欄位: {field}")
            break

    # 如果沒有找到欄位，則記錄警告
    if first_interaction_daily_field is None:
        logger.warning("合併後找不到首次互動時間欄位，新用戶可能無法設置首次互動時間")

    if last_interaction_daily_field is None:
        logger.warning("合併後找不到最後互動時間欄位，新用戶可能無法設置最後互動時間")

    # 檢查是否有每日互動時間的列
    has_first_interaction_daily = first_interaction_daily_field is not None
    has_last_interaction_daily = last_interaction_daily_field is not None

    # 識別新增記錄（即 base_stats 中不存在但 daily_stats 中存在的記錄）
    new_users_mask = np.zeros(len(merged_stats), dtype=bool)
    for idx, row in merged_stats.iterrows():
        key = (row['ec_id'], row['permanent'])
        # 如果不在 original_first_interaction 和 original_last_interaction 中，說明是新用戶
        if key not in original_first_interaction and key not in original_last_interaction:
            new_users_mask[idx] = True

    # 統計新用戶數量
    new_users_count = new_users_mask.sum()
    logger.info(f"識別出 {new_users_count} 筆新用戶記錄")

    # 檢查每日互動時間欄位在新用戶中的情況
    if new_users_count > 0:
        has_first_day_count = 0
        has_last_day_count = 0

        if has_first_interaction_daily:
            has_first_day_count = merged_stats.loc[new_users_mask, first_interaction_daily_field].notna().sum()
            logger.debug(f"新用戶中有 {has_first_day_count}/{new_users_count} 筆有 {first_interaction_daily_field} 值")
        else:
            logger.warning(f"合併後找不到首次互動時間欄位，這可能導致新用戶的 first_interaction_time 無法設定")

        if has_last_interaction_daily:
            has_last_day_count = merged_stats.loc[new_users_mask, last_interaction_daily_field].notna().sum()
            logger.debug(f"新用戶中有 {has_last_day_count}/{new_users_count} 筆有 {last_interaction_daily_field} 值")
        else:
            logger.warning(f"合併後找不到最後互動時間欄位，這可能導致新用戶的 last_interaction_time 無法設定")

        # 確保 has_first_day_count 和 has_last_day_count 是數值而不是 Series
        if isinstance(has_first_day_count, pd.Series):
            has_first_day_count = has_first_day_count.sum() if not has_first_day_count.empty else 0
        if isinstance(has_last_day_count, pd.Series):
            has_last_day_count = has_last_day_count.sum() if not has_last_day_count.empty else 0

        if has_first_day_count < new_users_count or has_last_day_count < new_users_count:
            logger.warning(f"部分新用戶缺少互動時間：{new_users_count - has_first_day_count} 筆缺少首次互動時間，{new_users_count - has_last_day_count} 筆缺少最後互動時間")

    # 對所有記錄進行處理
    for idx, row in merged_stats.iterrows():
        key = (row['ec_id'], row['permanent'])

        # 針對新用戶處理
        if new_users_mask[idx]:
            # 設置 first_interaction_time（必須使用當天的互動時間）
            if has_first_interaction_daily:
                field_value = row.get(first_interaction_daily_field)
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 改進: 對於首次互動時間，使用最早的時間
                        field_value = field_value.min()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'first_interaction_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定首次互動時間: {field_value}")
                else:
                    logger.debug(f"新用戶 {row['permanent']} 缺少 {first_interaction_daily_field} 值")

            # 設置 last_interaction_time（必須使用當天的互動時間）
            if has_last_interaction_daily:
                field_value = row.get(last_interaction_daily_field)
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 改進: 對於最後互動時間，使用最晚的時間
                        field_value = field_value.max()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'last_interaction_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定最後互動時間: {field_value}")
                else:
                    logger.debug(f"新用戶 {row['permanent']} 缺少 {last_interaction_daily_field} 值")

            # 設置購買時間 (如果有)
            if 'first_purchase_of_day' in merged_stats.columns:
                field_value = row.get('first_purchase_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 改進: 使用最早的購買時間
                        field_value = field_value.min()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'first_purchase_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定首次購買時間: {field_value}")

            if 'last_purchase_of_day' in merged_stats.columns:
                field_value = row.get('last_purchase_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 改進: 使用最晚的購買時間
                        field_value = field_value.max()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'last_purchase_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定最後購買時間: {field_value}")

            # 設置加入購物車時間 (如果有)
            if 'first_add_to_cart_of_day' in merged_stats.columns:
                field_value = row.get('first_add_to_cart_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 使用最早的加入購物車時間
                        field_value = field_value.min()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'first_add_to_cart_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定首次加入購物車時間: {field_value}")

            if 'last_add_to_cart_of_day' in merged_stats.columns:
                field_value = row.get('last_add_to_cart_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 使用最晚的加入購物車時間
                        field_value = field_value.max()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'last_add_to_cart_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定最後加入購物車時間: {field_value}")

            # 設置瀏覽商品時間 (如果有)
            if 'first_view_item_of_day' in merged_stats.columns:
                field_value = row.get('first_view_item_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 使用最早的瀏覽商品時間
                        field_value = field_value.min()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'first_view_item_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定首次瀏覽商品時間: {field_value}")

            if 'last_view_item_of_day' in merged_stats.columns:
                field_value = row.get('last_view_item_of_day')
                # 檢查是否為 Series
                if isinstance(field_value, pd.Series):
                    if not field_value.empty:
                        # 使用最晚的瀏覽商品時間
                        field_value = field_value.max()
                    else:
                        field_value = None

                if pd.notna(field_value):
                    merged_stats.at[idx, 'last_view_item_time'] = field_value
                    logger.debug(f"新用戶 {row['permanent']} 設定最後瀏覽商品時間: {field_value}")
        else:
            # 處理既有用戶
            # 1. 如果是既有用戶且有原始的 first_interaction_time，使用原始值
            if key in original_first_interaction:
                current_first = original_first_interaction[key]
                # 如果當天有更早的互動時間，則更新
                if has_first_interaction_daily:
                    daily_first = row.get(first_interaction_daily_field)
                    # 檢查 daily_first 是否為 Series，如果是則取最早的值
                    if isinstance(daily_first, pd.Series):
                        if not daily_first.empty:
                            daily_first = daily_first.min()
                        else:
                            daily_first = None

                    # 現在 daily_first 應該是單一值，可以安全使用 pd.notna()
                    if pd.notna(daily_first):
                        # 將時間轉換為 datetime 以進行比較
                        if isinstance(current_first, str):
                            current_first = pd.to_datetime(current_first)
                        if isinstance(daily_first, str):
                            daily_first = pd.to_datetime(daily_first)

                        if daily_first < current_first:
                            logger.info(f"用戶 {row['permanent']} 在當天有更早的互動時間，從 {current_first} 更新為 {daily_first}")
                            current_first = daily_first

                merged_stats.at[idx, 'first_interaction_time'] = current_first

            # 3. 處理 last_interaction_time
            # 3.1 如果是既有用戶且有原始的 last_interaction_time
            if key in original_last_interaction:
                current_last = original_last_interaction[key]
                # 如果當天有更新的互動時間，則更新
                if has_last_interaction_daily:
                    daily_last = row.get(last_interaction_daily_field)
                    # 檢查 daily_last 是否為 Series，如果是則取最晚的值
                    if isinstance(daily_last, pd.Series):
                        if not daily_last.empty:
                            daily_last = daily_last.max()
                        else:
                            daily_last = None

                    # 現在 daily_last 應該是單一值，可以安全使用 pd.notna()
                    if pd.notna(daily_last):
                        # 將時間轉換為 datetime 以進行比較
                        if isinstance(current_last, str):
                            current_last = pd.to_datetime(current_last)
                        if isinstance(daily_last, str):
                            daily_last = pd.to_datetime(daily_last)

                        if daily_last > current_last:
                            logger.debug(f"用戶 {row['permanent']} 在當天有更新的互動時間，從 {current_last} 更新為 {daily_last}")
                            current_last = daily_last

                merged_stats.at[idx, 'last_interaction_time'] = current_last

    # 更新購買統計
    # 初始化購買次數和金額欄位（如果不存在）
    if 'purchase_count' not in merged_stats.columns:
        merged_stats['purchase_count'] = pd.Series(0, index=merged_stats.index, dtype=pd.Int64Dtype())
    if 'total_purchase_amount' not in merged_stats.columns:
        merged_stats['total_purchase_amount'] = pd.Series(0.0, index=merged_stats.index, dtype='float64')

    # 處理已有用戶的購買統計更新
    for idx, row in merged_stats.iterrows():
        key = (row['ec_id'], row['permanent'])
        # 只處理非新用戶
        if not new_users_mask[idx]:
            # 更新購買次數
            if 'daily_purchase_count' in merged_stats.columns:
                daily_count = row.get('daily_purchase_count')
                # 檢查是否為 Series
                if isinstance(daily_count, pd.Series):
                    if not daily_count.empty:
                        daily_count = daily_count.iloc[0]
                    else:
                        daily_count = 0

                # 確保 daily_count 是數值
                if pd.notna(daily_count):
                    # 確保是整數
                    daily_count = int(daily_count)
                    current_count = row.get('purchase_count', 0)
                    # 確保 current_count 是數值
                    if pd.isna(current_count):
                        current_count = 0
                    # 更新購買次數
                    merged_stats.at[idx, 'purchase_count'] = current_count + daily_count

            # 更新 sessions 數量
            if 'daily_sessions' in merged_stats.columns:
                daily_sessions = row.get('daily_sessions')
                # 檢查是否為 Series
                if isinstance(daily_sessions, pd.Series):
                    if not daily_sessions.empty:
                        daily_sessions = daily_sessions.iloc[0]
                    else:
                        daily_sessions = 0

                # 確保 daily_sessions 是數值
                if pd.notna(daily_sessions):
                    # 確保是整數
                    daily_sessions = int(daily_sessions)
                    current_sessions = row.get('total_sessions', 0)
                    # 確保 current_sessions 是數值
                    if pd.isna(current_sessions):
                        current_sessions = 0
                    # 更新 sessions 數量
                    merged_stats.at[idx, 'total_sessions'] = current_sessions + daily_sessions

                    # 更新購買時間
                    if daily_count > 0:
                        # 更新 last_purchase_time
                        if 'last_purchase_of_day' in merged_stats.columns:
                            last_purchase = row.get('last_purchase_of_day')
                            if isinstance(last_purchase, pd.Series):
                                if not last_purchase.empty:
                                    last_purchase = last_purchase.max()
                                else:
                                    last_purchase = None

                            if pd.notna(last_purchase):
                                merged_stats.at[idx, 'last_purchase_time'] = last_purchase
                                logger.debug(f"用戶 {row['permanent']} 更新最後購買時間為: {last_purchase}")

                        # 如果沒有首次購買時間，更新 first_purchase_time
                        if pd.isna(row.get('first_purchase_time')) and 'first_purchase_of_day' in merged_stats.columns:
                            first_purchase = row.get('first_purchase_of_day')
                            if isinstance(first_purchase, pd.Series):
                                if not first_purchase.empty:
                                    first_purchase = first_purchase.min()
                                else:
                                    first_purchase = None

                            if pd.notna(first_purchase):
                                merged_stats.at[idx, 'first_purchase_time'] = first_purchase
                                logger.debug(f"用戶 {row['permanent']} 設置首次購買時間為: {first_purchase}")

                    # 處理加入購物車時間（無論是否有購買都更新）
                    # 更新 last_add_to_cart_time
                    if 'last_add_to_cart_of_day' in merged_stats.columns:
                        last_add_to_cart = row.get('last_add_to_cart_of_day')
                        if isinstance(last_add_to_cart, pd.Series):
                            if not last_add_to_cart.empty:
                                last_add_to_cart = last_add_to_cart.max()
                            else:
                                last_add_to_cart = None

                        if pd.notna(last_add_to_cart):
                            # 如果已有值，比較並取較晚的時間
                            current_last_add_to_cart = row.get('last_add_to_cart_time')
                            if pd.isna(current_last_add_to_cart) or (pd.notna(current_last_add_to_cart) and pd.to_datetime(last_add_to_cart) > pd.to_datetime(current_last_add_to_cart)):
                                merged_stats.at[idx, 'last_add_to_cart_time'] = last_add_to_cart
                                logger.debug(f"用戶 {row['permanent']} 更新最後加入購物車時間為: {last_add_to_cart}")

                    # 如果沒有首次加入購物車時間，更新 first_add_to_cart_time
                    if pd.isna(row.get('first_add_to_cart_time')) and 'first_add_to_cart_of_day' in merged_stats.columns:
                        first_add_to_cart = row.get('first_add_to_cart_of_day')
                        if isinstance(first_add_to_cart, pd.Series):
                            if not first_add_to_cart.empty:
                                first_add_to_cart = first_add_to_cart.min()
                            else:
                                first_add_to_cart = None

                        if pd.notna(first_add_to_cart):
                            merged_stats.at[idx, 'first_add_to_cart_time'] = first_add_to_cart
                            logger.debug(f"用戶 {row['permanent']} 設置首次加入購物車時間為: {first_add_to_cart}")

                    # 處理瀏覽商品時間（無論是否有購買都更新）
                    # 更新 last_view_item_time
                    if 'last_view_item_of_day' in merged_stats.columns:
                        last_view_item = row.get('last_view_item_of_day')
                        if isinstance(last_view_item, pd.Series):
                            if not last_view_item.empty:
                                last_view_item = last_view_item.max()
                            else:
                                last_view_item = None

                        if pd.notna(last_view_item):
                            # 如果已有值，比較並取較晚的時間
                            current_last_view_item = row.get('last_view_item_time')
                            if pd.isna(current_last_view_item) or (pd.notna(current_last_view_item) and pd.to_datetime(last_view_item) > pd.to_datetime(current_last_view_item)):
                                merged_stats.at[idx, 'last_view_item_time'] = last_view_item
                                logger.debug(f"用戶 {row['permanent']} 更新最後瀏覽商品時間為: {last_view_item}")

                    # 如果沒有首次查看商品時間，更新 first_view_item_time
                    if pd.isna(row.get('first_view_item_time')) and 'first_view_item_of_day' in merged_stats.columns:
                        first_view_item = row.get('first_view_item_of_day')
                        if isinstance(first_view_item, pd.Series):
                            if not first_view_item.empty:
                                first_view_item = first_view_item.min()
                            else:
                                first_view_item = None

                        if pd.notna(first_view_item):
                            merged_stats.at[idx, 'first_view_item_time'] = first_view_item
                            logger.debug(f"用戶 {row['permanent']} 設置首次瀏覽商品時間為: {first_view_item}")

            # 更新購買金額
            if 'daily_purchase_amount' in merged_stats.columns:
                daily_amount = row.get('daily_purchase_amount')
                # 檢查是否為 Series
                if isinstance(daily_amount, pd.Series):
                    if not daily_amount.empty:
                        daily_amount = daily_amount.iloc[0]
                    else:
                        daily_amount = 0.0

                # 確保 daily_amount 是數值
                if pd.notna(daily_amount):
                    # 確保是浮點數
                    daily_amount = float(daily_amount)
                    current_amount = row.get('total_purchase_amount', 0.0)
                    # 確保 current_amount 是數值
                    if pd.isna(current_amount):
                        current_amount = 0.0
                    # 更新購買金額
                    merged_stats.at[idx, 'total_purchase_amount'] = current_amount + daily_amount
        else:
            # 處理新用戶的購買統計
            if 'daily_purchase_count' in merged_stats.columns:
                daily_count = row.get('daily_purchase_count')
                # 檢查是否為 Series
                if isinstance(daily_count, pd.Series):
                    if not daily_count.empty:
                        daily_count = daily_count.iloc[0]
                    else:
                        daily_count = 0

                # 確保 daily_count 是數值
                if pd.notna(daily_count):
                    # 確保是整數
                    daily_count = int(daily_count)
                    merged_stats.at[idx, 'purchase_count'] = daily_count

            # 處理新用戶的 sessions 統計
            if 'daily_sessions' in merged_stats.columns:
                daily_sessions = row.get('daily_sessions')
                # 檢查是否為 Series
                if isinstance(daily_sessions, pd.Series):
                    if not daily_sessions.empty:
                        daily_sessions = daily_sessions.iloc[0]
                    else:
                        daily_sessions = 0

                # 確保 daily_sessions 是數值
                if pd.notna(daily_sessions):
                    # 確保是整數
                    daily_sessions = int(daily_sessions)
                    merged_stats.at[idx, 'total_sessions'] = daily_sessions

            if 'daily_purchase_amount' in merged_stats.columns:
                daily_amount = row.get('daily_purchase_amount')
                # 檢查是否為 Series
                if isinstance(daily_amount, pd.Series):
                    if not daily_amount.empty:
                        daily_amount = daily_amount.iloc[0]
                    else:
                        daily_amount = 0.0

                # 確保 daily_amount 是數值
                if pd.notna(daily_amount):
                    # 確保是浮點數
                    daily_amount = float(daily_amount)
                    merged_stats.at[idx, 'total_purchase_amount'] = daily_amount

    # 清理合併後的 DataFrame，移除不需要的臨時欄位
    temp_columns = [col for col in merged_stats.columns if col.endswith('_of_day') or col.endswith('_daily')]
    if temp_columns:
        logger.info(f"從最終結果中移除臨時欄位: {temp_columns}")
        merged_stats = merged_stats.drop(columns=temp_columns, errors='ignore')

    # 確保所有必要欄位存在
    required_columns = ['first_interaction_time', 'last_interaction_time', 'purchase_count', 'total_purchase_amount']
    for col in required_columns:
        if col not in merged_stats.columns:
            logger.warning(f"最終結果中缺少必要欄位: {col}")

    # 檢查 NULL 值
    null_first = merged_stats['first_interaction_time'].isna().sum()
    null_last = merged_stats['last_interaction_time'].isna().sum()
    if null_first > 0 or null_last > 0:
        logger.error(f"最終結果中有 NULL 值: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")
        raise ValueError(f"first_interaction_time 和 last_interaction_time 不能為 NULL: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")

    return merged_stats


def _update_user_stats_with_daily_data_polars(base_stats: pd.DataFrame, daily_stats: pd.DataFrame) -> pd.DataFrame:
    """Polars 實現：更新使用者統計資料，合併基礎資料和每日資料

    這是高效能的 Polars 實現，保留所有原始驗證邏輯

    Args:
        base_stats (pd.DataFrame): 基礎統計資料
        daily_stats (pd.DataFrame): 每日統計資料

    Returns:
        pd.DataFrame: 更新後的統計資料
    """
    logger = logging.getLogger(__name__)

    try:
        import polars as pl
    except ImportError:
        logger.error("Polars 未安裝，回退到 Pandas 實現")
        return _update_user_stats_with_daily_data_pandas(base_stats, daily_stats)

    # 檢查輸入資料 - 保留原始驗證邏輯
    if base_stats is None or base_stats.empty:
        logger.warning("基礎統計資料為空，將只使用每日資料")
        if daily_stats is None or daily_stats.empty:
            return pd.DataFrame()

        # 對於只有每日資料的情況，仍需確保時間欄位不為 NULL
        result = daily_stats.copy()

        # 檢查並重命名欄位
        rename_mapping = {
            'daily_sessions': 'total_sessions',
            'daily_purchase_count': 'purchase_count',
            'daily_purchase_amount': 'total_purchase_amount',
            'first_interaction_of_day': 'first_interaction_time',
            'last_interaction_of_day': 'last_interaction_time',
            'first_purchase_of_day': 'first_purchase_time',
            'last_purchase_of_day': 'last_purchase_time',
            'first_add_to_cart_of_day': 'first_add_to_cart_time',
            'last_add_to_cart_of_day': 'last_add_to_cart_time',
            'first_view_item_of_day': 'first_view_item_time',
            'last_view_item_of_day': 'last_view_item_time'
        }

        # 只對存在的欄位進行 rename
        existing_rename_mapping = {k: v for k, v in rename_mapping.items() if k in result.columns}
        result = result.rename(columns=existing_rename_mapping)

        # 驗證時間欄位不為 NULL
        if 'first_interaction_time' in result.columns:
            null_first = result['first_interaction_time'].isna().sum()
            if null_first > 0:
                logger.error(f"每日資料中有 {null_first} 筆 first_interaction_time 為 NULL")
                raise ValueError("first_interaction_time 不能為 NULL")

        if 'last_interaction_time' in result.columns:
            null_last = result['last_interaction_time'].isna().sum()
            if null_last > 0:
                logger.error(f"每日資料中有 {null_last} 筆 last_interaction_time 為 NULL")
                raise ValueError("last_interaction_time 不能為 NULL")

        return result

    if daily_stats is None or daily_stats.empty:
        logger.warning("每日統計資料為空，將只使用基礎資料")
        return base_stats

    # 轉換為 Polars DataFrame 進行高效處理
    try:
        base_pl = pl.from_pandas(base_stats).with_columns(pl.col("permanent").cast(pl.String))
        daily_pl = pl.from_pandas(daily_stats).with_columns(pl.col("permanent").cast(pl.String))
    except Exception as e:
        logger.error(f"轉換為 Polars DataFrame 失敗: {str(e)}，回退到 Pandas 實現")
        return _update_user_stats_with_daily_data_pandas(base_stats, daily_stats)

    # 使用 Lazy API 進行高效處理
    base_lf = base_pl.lazy()
    daily_lf = daily_pl.lazy()

    # 合併資料 (Full Outer Join)
    merged_lf = base_lf.join(
        daily_lf,
        on=['ec_id', 'permanent'],
        how='full',
        suffix='_daily'
    )

    # 準備所有可能的每日欄位，如果不存在則填充 null
    daily_fields = [
        "daily_sessions", "daily_purchase_count", "daily_purchase_amount",
        "first_interaction_of_day", "last_interaction_of_day",
        "first_purchase_of_day", "last_purchase_of_day",
        "first_add_to_cart_of_day", "last_add_to_cart_of_day",
        "first_view_item_of_day", "last_view_item_of_day"
    ]

    # 檢查並添加缺失的欄位 - 避免使用 .columns 屬性
    # 先收集 schema 來檢查欄位
    try:
        merged_schema = merged_lf.collect_schema()
        existing_columns = set(merged_schema.names())
    except Exception:
        # 如果無法獲取 schema，使用原始方法但捕獲警告
        import warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            existing_columns = set(merged_lf.columns)

    # 添加缺失的每日欄位
    missing_fields = [field for field in daily_fields if field not in existing_columns]
    if missing_fields:
        for field in missing_fields:
            merged_lf = merged_lf.with_columns(pl.lit(None).alias(field))

    # 確保基礎統計欄位存在
    base_fields = ["total_sessions", "purchase_count", "total_purchase_amount",
                   "first_interaction_time", "last_interaction_time"]
    for field in base_fields:
        if field not in existing_columns:
            merged_lf = merged_lf.with_columns(pl.lit(None).alias(field))

    # 使用 Polars 表達式進行高效計算
    final_lf = merged_lf.with_columns([
        # 處理 join key 欄位 - 確保不會變成 null
        pl.coalesce([pl.col("ec_id"), pl.col("ec_id_daily")]).alias("ec_id"),
        pl.coalesce([pl.col("permanent"), pl.col("permanent_daily")]).alias("permanent"),

        # 合併數值欄位
        (pl.col("total_sessions").fill_null(0) + pl.col("daily_sessions").fill_null(0)).alias("total_sessions"),
        (pl.col("purchase_count").fill_null(0) + pl.col("daily_purchase_count").fill_null(0)).alias("purchase_count"),
        (pl.col("total_purchase_amount").fill_null(0) + pl.col("daily_purchase_amount").fill_null(0)).alias("total_purchase_amount"),

        # 更新時間欄位 - 保留原始邏輯
        pl.when(pl.col("first_interaction_time").is_null())
          .then(pl.col("first_interaction_of_day"))
          .when(pl.col("first_interaction_of_day").is_not_null())
          .then(pl.min_horizontal(["first_interaction_time", "first_interaction_of_day"]))
          .otherwise(pl.col("first_interaction_time"))
          .alias("first_interaction_time"),

        pl.when(pl.col("last_interaction_time").is_null())
          .then(pl.col("last_interaction_of_day"))
          .when(pl.col("last_interaction_of_day").is_not_null())
          .then(pl.max_horizontal(["last_interaction_time", "last_interaction_of_day"]))
          .otherwise(pl.col("last_interaction_time"))
          .alias("last_interaction_time"),
    ])

    # 處理其他時間欄位（如果存在）
    time_field_pairs = [
        ("first_purchase_time", "first_purchase_of_day"),
        ("last_purchase_time", "last_purchase_of_day"),
        ("first_add_to_cart_time", "first_add_to_cart_of_day"),
        ("last_add_to_cart_time", "last_add_to_cart_of_day"),
        ("first_view_item_time", "first_view_item_of_day"),
        ("last_view_item_time", "last_view_item_of_day")
    ]

    for base_field, daily_field in time_field_pairs:
        if base_field in base_pl.columns or daily_field in daily_pl.columns:
            if "first_" in base_field:
                # 對於 first_ 欄位，取較早的時間
                final_lf = final_lf.with_columns(
                    pl.when(pl.col(base_field).is_null())
                      .then(pl.col(daily_field))
                      .when(pl.col(daily_field).is_not_null())
                      .then(pl.min_horizontal([base_field, daily_field]))
                      .otherwise(pl.col(base_field))
                      .alias(base_field)
                )
            else:
                # 對於 last_ 欄位，取較晚的時間
                final_lf = final_lf.with_columns(
                    pl.when(pl.col(base_field).is_null())
                      .then(pl.col(daily_field))
                      .when(pl.col(daily_field).is_not_null())
                      .then(pl.max_horizontal([base_field, daily_field]))
                      .otherwise(pl.col(base_field))
                      .alias(base_field)
                )

    # 選擇最終欄位（移除 _daily 後綴的欄位）
    # 避免使用 .columns 屬性，改用 schema
    try:
        final_schema = final_lf.collect_schema()
        all_columns = final_schema.names()
    except Exception:
        # 如果無法獲取 schema，使用原始方法但捕獲警告
        import warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            all_columns = final_lf.columns

    final_columns = [col for col in all_columns if not col.endswith('_daily') and col not in daily_fields]
    final_lf = final_lf.select(final_columns)

    # 轉換回 Pandas DataFrame
    try:
        result_df = final_lf.collect().to_pandas()
    except Exception as e:
        logger.error(f"Polars 處理失敗: {str(e)}，回退到 Pandas 實現")
        return _update_user_stats_with_daily_data_pandas(base_stats, daily_stats)

    # 保留原始驗證邏輯
    null_first = result_df['first_interaction_time'].isna().sum()
    null_last = result_df['last_interaction_time'].isna().sum()
    if null_first > 0 or null_last > 0:
        logger.error(f"最終結果中有 NULL 值: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")
        raise ValueError(f"first_interaction_time 和 last_interaction_time 不能為 NULL: {null_first} 筆 first_interaction_time, {null_last} 筆 last_interaction_time")

    logger.info(f"Polars 實現成功處理 {len(result_df)} 筆記錄")
    return result_df


def calculate_cost_usd(bytes_processed: int) -> float:
    """
    計算 BigQuery 查詢成本（美元）

    Args:
        bytes_processed: 處理的位元組數

    Returns:
        float: 查詢成本（美元）
    """
    # BigQuery 價格為每 TB $5.0
    return bytes_processed / (1024 ** 4) * 5.0

def convert_to_twd(usd_amount: float) -> float:
    """
    將美元轉換為台幣

    Args:
        usd_amount: 美元金額

    Returns:
        float: 台幣金額
    """
    # 使用當前匯率，1 USD 約等於 31 TWD
    return usd_amount * 31.0

def standardize_datetime_columns(df: pd.DataFrame, skip_validation: bool = False, only_required_columns: bool = True) -> pd.DataFrame:
    """標準化 DataFrame 中的時間欄位，確保格式一致

    Args:
        df: 要處理的 DataFrame
        skip_validation: 是否跳過格式驗證
        only_required_columns: 是否只處理必要的時間欄位

    Returns:
        pd.DataFrame: 處理後的 DataFrame
    """
    logger = logging.getLogger(__name__)

    # 如果 df 為空，直接返回
    if df is None or df.empty:
        return df

    # 複製 DataFrame 以避免修改原始資料
    df = df.copy()

    # 定義需要處理的時間欄位
    datetime_columns = []
    required_columns = [
        'first_interaction_time', 'last_interaction_time',
        'first_purchase_time', 'last_purchase_time',
        'first_add_to_cart_time', 'last_add_to_cart_time',
        'first_view_item_time', 'last_view_item_time',
        'registration_time'
    ]

    if only_required_columns:
        # 只處理必要的時間欄位，如果它們存在
        datetime_columns = [col for col in required_columns if col in df.columns]
    else:
        # 嘗試識別所有時間欄位
        for col in df.columns:
            if col in required_columns:
                datetime_columns.append(col)
                continue

            if col.endswith('_time') or col.endswith('_date') or col.endswith('_at'):
                datetime_columns.append(col)

    logger.info(f"將處理以下時間欄位: {datetime_columns}")

    start_time = time.time()
    total_records = len(df)

    # 檢查是否太多記錄，如果太多可能需要分批處理
    batch_processing = total_records > 100000
    batch_size = 100000 if batch_processing else total_records  # 調整批次大小從 50000 到 100000
    progress_interval = max(1, batch_size // 10)  # 每處理 10% 輸出一次進度

    # 定義時間轉換函數
    def convert_time(x):
        # 如果是 None 或 NaT，直接返回
        if pd.isna(x):
            return None

        # 將不同類型的時間值轉換為字符串格式
        if isinstance(x, (datetime, pd.Timestamp)):
            # 確保時間有時區資訊
            if hasattr(x, 'tzinfo') and x.tzinfo is not None:
                return x.isoformat()
            else:
                # 如果沒有時區，假設是 UTC
                return x.replace(tzinfo=timezone.utc).isoformat()
        elif isinstance(x, str):
            try:
                # 嘗試解析字符串，然後再轉換回字符串
                dt = pd.to_datetime(x)
                if pd.isna(dt):
                    return None
                # 確保時間有時區資訊
                if hasattr(dt, 'tzinfo') and dt.tzinfo is not None:
                    return dt.isoformat()
                else:
                    # 如果沒有時區，假設是 UTC
                    return dt.replace(tzinfo=timezone.utc).isoformat()
            except:
                return x  # 如果解析失敗，返回原始字符串
        else:
            # 其他類型嘗試轉換為字符串
            try:
                return str(x)
            except:
                return None

    for col in datetime_columns:
        col_start_time = time.time()
        logger.info(f"開始處理欄位 {col}")

        # 檢查該欄位是否包含時間資料
        sample = df[col].dropna().head(100)
        has_datetime = False

        if not sample.empty:
            for x in sample:
                if isinstance(x, (datetime, pd.Timestamp)) or (isinstance(x, str) and '_' in x):
                    has_datetime = True
                    break

        if not has_datetime:
            logger.info(f"欄位 {col} 不包含時間資料，跳過處理")
            continue

        # 記錄處理前的 NULL 值數量
        null_count_before = df[col].isna().sum()

        # 批次處理
        processed_count = 0
        for start_idx in range(0, total_records, batch_size):
            batch_start_time = time.time()
            end_idx = min(start_idx + batch_size, total_records)
            # 處理一批數據
            batch = df.iloc[start_idx:end_idx]
            df.iloc[start_idx:end_idx, df.columns.get_loc(col)] = batch[col].apply(convert_time)

            processed_count += (end_idx - start_idx)

            # 每處理一定比例的數據輸出進度
            if processed_count % progress_interval < batch_size:
                progress_pct = (processed_count / total_records) * 100
                batch_time = time.time() - batch_start_time
                logger.info(f"欄位 {col} 處理進度: {progress_pct:.1f}%, 已處理 {processed_count}/{total_records} 筆記錄，批次耗時: {batch_time:.2f}秒")

        # 檢查處理後的 NULL 值數量
        null_count_after = df[col].isna().sum()
        col_processing_time = time.time() - col_start_time

        if null_count_after > null_count_before:
            logger.warning(f"欄位 {col} 標準化後 NULL 值增加: 從 {null_count_before} 增加到 {null_count_after} ({null_count_after-null_count_before} 筆增加)")

        logger.info(f"完成欄位 {col} 的標準化，耗時: {col_processing_time:.2f}秒，處理速度: {total_records/col_processing_time:.2f} 筆/秒")

    total_time = time.time() - start_time
    logger.info(f"所有時間欄位標準化完成，總耗時: {total_time:.2f}秒，平均處理速度: {total_records/total_time:.2f} 筆/秒")

    return df

def preprocess_user_stats(base_stats_df: pd.DataFrame, daily_stats_df: pd.DataFrame, ec_id: int, end_time: datetime) -> pd.DataFrame:
    """預處理使用者統計資料，合併基礎資料和每日資料

    Args:
        base_stats_df: 基礎統計資料
        daily_stats_df: 每日統計資料
        ec_id: 電商 ID
        end_time: 結束時間

    Returns:
        合併後的使用者統計資料
    """
    logger = logging.getLogger(__name__)
    logger.info(f"開始預處理電商 ID {ec_id} 的使用者統計資料")

    # 檢查輸入資料是否為空
    if base_stats_df is None or base_stats_df.empty:
        logger.warning(f"基礎統計資料為空")
        return pd.DataFrame()

    if daily_stats_df is None or daily_stats_df.empty:
        logger.warning(f"每日統計資料為空，將只使用基礎資料")
        return base_stats_df

    # 將兩個 DataFrame 中的 permanent 欄位轉換為相同的資料類型
    base_stats_df['permanent'] = base_stats_df['permanent'].astype(str)
    daily_stats_df['permanent'] = daily_stats_df['permanent'].astype(str)

    # 檢查並記錄 first_interaction_time 的狀態
    if 'first_interaction_time' in base_stats_df.columns:
        null_count = base_stats_df['first_interaction_time'].isna().sum()
        logger.info(f"基礎資料中有 {null_count} 筆 first_interaction_time 為 NULL")
        if null_count > 0:
            logger.warning("基礎資料中不應該有 NULL 的 first_interaction_time，這可能表示資料有問題")

    # 確定需要合併的欄位
    daily_cols = [
        'daily_sessions', 'active_days', 'daily_purchase_count', 'daily_purchase_amount',
        'first_purchase_of_day', 'last_purchase_of_day',
        'first_add_to_cart_of_day', 'last_add_to_cart_of_day',
        'first_view_item_of_day', 'last_view_item_of_day',
        'first_interaction_of_day', 'last_interaction_of_day'
    ]

    # 檢查所需的欄位是否在每日資料中
    missing_cols = [col for col in daily_cols if col not in daily_stats_df.columns]
    if missing_cols:
        logger.warning(f"每日資料中缺少以下欄位: {missing_cols}")
        # 在每日資料中新增缺少的欄位，賦予預設值
        for col in missing_cols:
            if 'time' in col or 'date' in col:
                daily_stats_df[col] = None
            else:
                daily_stats_df[col] = 0

    # 檢查 daily_stats_df 中的欄位
    logger.info(f"每日資料欄位: {daily_stats_df.columns.tolist()}")

    # 確定合併中使用的欄位集合
    base_required_cols = ['ec_id', 'permanent']
    daily_required_cols = ['ec_id', 'permanent']

    # 基礎資料中需要的欄位
    base_stats_cols = [
        'total_sessions', 'purchase_count', 'total_purchase_amount',
        'first_interaction_time', 'last_interaction_time',
        'first_purchase_time', 'last_purchase_time',
        'first_add_to_cart_time', 'last_add_to_cart_time',
        'first_view_item_time', 'last_view_item_time',
        'registration_time'
    ]

    # 根據欄位存在情況進行調整
    for col in base_stats_cols:
        if col in base_stats_df.columns:
            base_required_cols.append(col)
        elif col == 'first_interaction_time':
            logger.error("基礎資料中缺少必要欄位 first_interaction_time")
            raise ValueError("基礎資料中必須包含 first_interaction_time 欄位")

    # 每日資料需要的欄位，檢查是否存在
    for col in daily_cols:
        if col in daily_stats_df.columns:
            daily_required_cols.append(col)
        elif col not in base_stats_df.columns:
            logger.warning(f"欄位 {col} 在兩個數據集中都不存在，將使用預設值")
            # 新增預設值到基礎資料中
            if 'time' in col or 'date' in col:
                base_stats_df[col] = None
            else:
                base_stats_df[col] = 0
            base_required_cols.append(col)

    # 確保兩個數據框都有所有需要的欄位
    for col in base_required_cols:
        if col not in base_stats_df.columns:
            logger.warning(f"基礎資料中缺少欄位 {col}，新增預設值")
            base_stats_df[col] = None if 'time' in col or 'date' in col else 0

    for col in daily_required_cols:
        if col not in daily_stats_df.columns:
            logger.warning(f"每日資料中缺少欄位 {col}，新增預設值")
            daily_stats_df[col] = None if 'time' in col or 'date' in col else 0

    # 合併資料，使用安全的方式選擇欄位
    try:
        # 檢查欄位是否存在於資料框中
        base_cols_to_use = [col for col in base_required_cols if col in base_stats_df.columns]
        daily_cols_to_use = [col for col in daily_required_cols if col in daily_stats_df.columns]

        # 輸出合併前的欄位資訊
        logger.info(f"合併前 - 基礎資料欄位: {base_cols_to_use}")
        logger.info(f"合併前 - 每日資料欄位: {daily_cols_to_use}")

        # 保存原有的 first_interaction_time
        original_first_interaction = {}
        if 'first_interaction_time' in base_stats_df.columns:
            for idx, row in base_stats_df.iterrows():
                if pd.notna(row['first_interaction_time']):
                    original_first_interaction[row['ec_id'], row['permanent']] = row['first_interaction_time']

        # 合併資料
        user_stats = pd.merge(
            base_stats_df[base_cols_to_use],
            daily_stats_df[daily_cols_to_use],
            on=['ec_id', 'permanent'],
            how='outer',
            suffixes=('', '_daily')
        )

        logger.info(f"合併後資料筆數: {len(user_stats)}")

        # 處理 first_interaction_time
        # 1. 對於既有用戶，保持原有的 first_interaction_time
        # 2. 對於新用戶，使用 first_interaction_of_day
        for idx, row in user_stats.iterrows():
            key = (row['ec_id'], row['permanent'])
            if key in original_first_interaction:
                current_first = original_first_interaction[key]
                # 如果當天有更早的互動時間，則更新
                if 'first_interaction_of_day' in daily_stats_df.columns:
                    daily_first = row.get('first_interaction_of_day')
                    if pd.notna(daily_first):
                        # 將時間轉換為 datetime 以進行比較
                        if isinstance(current_first, str):
                            current_first = pd.to_datetime(current_first)
                        if isinstance(daily_first, str):
                            daily_first = pd.to_datetime(daily_first)

                        if daily_first < current_first:
                            user_stats.at[idx, 'first_interaction_time'] = daily_first

        # 驗證 first_interaction_time
        null_count = user_stats['first_interaction_time'].isna().sum()
        if null_count > 0:
            logger.error(f"合併後有 {null_count} 筆記錄的 first_interaction_time 為 NULL")
            # 不再嘗試修復，直接報錯
            raise ValueError(f"合併後仍有 {null_count} 筆記錄的 first_interaction_time 為 NULL，請檢查數據處理邏輯")

        # 驗證 last_interaction_time
        null_count = user_stats['last_interaction_time'].isna().sum()
        if null_count > 0:
            logger.error(f"合併後有 {null_count} 筆記錄的 last_interaction_time 為 NULL")
            # 不再嘗試修復，直接報錯
            raise ValueError(f"合併後仍有 {null_count} 筆記錄的 last_interaction_time 為 NULL，請檢查數據處理邏輯")

    except ValueError as ve:
        # ValueError 是我們有意識拋出的錯誤，需要向上傳播
        logger.error(f"發現關鍵欄位有 NULL 值: {str(ve)}")
        raise

    except Exception as e:
        # 其他類型的異常仍然可以捕獲並處理
        logger.error(f"合併資料時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return base_stats_df  # 如果合併失敗，返回基礎資料

    return user_stats