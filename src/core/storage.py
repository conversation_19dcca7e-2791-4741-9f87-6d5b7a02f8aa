import os
import json
import pandas as pd
import pyarrow
import gcsfs
import tempfile
import traceback
from datetime import datetime, timezone
from typing import Dict, Any

from google.cloud import storage
from src.utils import logging_setup
from src.utils.time_utils import get_taiwan_date_str

logger = logging_setup.configure_logging()

def _get_gcs_base_path(resource_type: str = "user_stats_snapshots") -> str:
    """
    根據環境返回適當的 GCS 路徑前綴

    Args:
        resource_type: 資源類型 ("user_stats_snapshots", "user_stats_configs" 等)

    Returns:
        str: GCS 路徑前綴
    """
    # 檢查是否在測試環境中
    in_test_env = os.environ.get('PYTEST_CURRENT_TEST') is not None

    if in_test_env:
        # 測試環境使用 LTA_TEST 前綴
        return f"LTA_TEST/{resource_type}"
    else:
        # 生產環境使用 LTA 前綴
        return f"LTA/{resource_type}"

class StorageManager:
    """雲端儲存管理類別"""

    def __init__(self, bucket_name: str = "tagtoo-ml-workflow"):
        """
        初始化 StorageManager

        Args:
            bucket_name: GCS bucket 名稱
        """
        self.client = storage.Client()
        self.bucket_name = bucket_name
        self.bucket = self.client.bucket(bucket_name)

    def save_snapshot(self, data_df: pd.DataFrame, date: datetime, ec_id: int = None) -> Dict[str, Any]:
        """儲存使用者統計資料快照到 GCS

        Args:
            data_df: 要儲存的 DataFrame
            date: 快照日期 (UTC)
            ec_id: 電商 ID，如果提供則按電商分類存放

        Returns:
            Dict[str, Any]: 包含儲存結果的字典
        """
        # 檢查是否在測試環境中運行
        in_test_env = os.environ.get('PYTEST_CURRENT_TEST') is not None

        # 確保日期有時區資訊
        if date.tzinfo is None:
            logger.warning(f"輸入日期 {date} 沒有時區資訊，假設為 UTC")
            date = date.replace(tzinfo=timezone.utc)

        # 使用 UTC 日期格式化檔案名稱
        utc_date_str = date.strftime('%Y%m%d')

        # 獲取台灣時間日期字串
        taiwan_date_str = get_taiwan_date_str(date)

        # 如果 UTC 日期與台灣時間日期不同，則記錄警告
        if utc_date_str != taiwan_date_str:
            logger.info(f"注意: UTC 日期 {utc_date_str} 與台灣時間日期 {taiwan_date_str} 不同")

        # 在儲存前清理資料中的臨時欄位
        data_df_clean = data_df.copy()

        # 識別臨時欄位（帶有 _of_day 或 _daily 後綴的欄位）
        temp_columns = [col for col in data_df_clean.columns if col.endswith('_of_day') or col.endswith('_daily')]
        if temp_columns:
            logger.info(f"在儲存快照前移除以下臨時欄位: {temp_columns}")
            data_df_clean = data_df_clean.drop(columns=temp_columns, errors='ignore')
        else:
            logger.debug("資料中未發現臨時欄位")

        # 強制轉換 ec_id 欄位為 int64 類型（非 Nullable Int64 類型）
        if 'ec_id' in data_df_clean.columns:
            original_type = data_df_clean['ec_id'].dtype

            # 檢查是否有 NULL 值
            null_count = data_df_clean['ec_id'].isna().sum()
            if null_count > 0:
                logger.warning(f"ec_id 欄位包含 {null_count} 個 NULL 值，這些值將被過濾掉")
                # 過濾掉 NULL 值的行
                data_df_clean = data_df_clean.dropna(subset=['ec_id'])
                # 如果過濾後沒有資料了，則記錄錯誤
                if data_df_clean.empty:
                    logger.error("過濾 NULL 值後，DataFrame 為空")
                    raise ValueError("ec_id 欄位全部為 NULL 值，無法繼續處理")

            # 使用 pd.api.types.is_integer_dtype 更穩健地檢查數據類型
            import pandas.api.types as types
            if types.is_integer_dtype(data_df_clean['ec_id']):
                if str(original_type) != 'int64':
                    logger.info(f"將 ec_id 欄位從 {original_type} 轉換為 int64 類型")
                    data_df_clean['ec_id'] = data_df_clean['ec_id'].astype('int64')
            else:
                logger.warning(f"ec_id 欄位不是整數類型 ({original_type})，嘗試強制轉換為 int64")
                try:
                    data_df_clean['ec_id'] = data_df_clean['ec_id'].astype('int64')
                except Exception as e:
                    logger.error(f"無法將 ec_id 轉換為 int64: {str(e)}")
                    raise ValueError(f"ec_id 欄位必須可轉換為整數，目前類型: {original_type}")

        # 構建儲存路徑
        if ec_id is not None:
            gcs_path = f"{_get_gcs_base_path()}/ec_{ec_id}/{utc_date_str}.parquet"
        else:
            gcs_path = f"{_get_gcs_base_path()}/user_stats_{utc_date_str}.parquet"

        # 記錄時間資訊
        logger.info(f"儲存快照，UTC日期: {date.strftime('%Y-%m-%d %H:%M:%S %Z')}, 檔名日期: {utc_date_str}")

        try:
            # 開始處理資料框
            logger.info(f"開始處理資料框：包含 {len(data_df_clean)} 列資料，{len(data_df_clean.columns)} 個欄位")

            # 確保關鍵欄位的資料類型正確
            logger.info("檢查並修正資料類型...")

            # 確保整數欄位的類型正確
            integer_columns = {
                'ec_id': 'int64',
                'total_sessions': 'int64',
                'purchase_count': 'int64'
            }

            for col, target_dtype in integer_columns.items():
                if col in data_df_clean.columns:
                    current_dtype = str(data_df_clean[col].dtype)

                    # 檢查是否需要轉換
                    if current_dtype.startswith('float') or current_dtype == 'Float64' or current_dtype not in ['int64', 'int32', 'Int64']:
                        logger.info(f"將欄位 {col} 從 {current_dtype} 轉換為 {target_dtype}")

                        # 處理 NaN 值（對於 total_sessions 和 purchase_count，NaN 應該轉為 0）
                        if col in ['total_sessions', 'purchase_count']:
                            na_count = data_df_clean[col].isna().sum()
                            if na_count > 0:
                                logger.warning(f"欄位 {col} 包含 {na_count} 個 NULL 值，將填充為 0")
                                data_df_clean[col] = data_df_clean[col].fillna(0)
                        elif col == 'ec_id':
                            # ec_id 不允許 NULL 值
                            na_count = data_df_clean[col].isna().sum()
                            if na_count > 0:
                                logger.error(f"ec_id 欄位不允許有 NULL 值，但發現 {na_count} 筆 NULL 記錄")
                                # 過濾掉 NULL 值的行
                                data_df_clean = data_df_clean.dropna(subset=[col])
                                logger.warning(f"已過濾掉 {na_count} 筆 ec_id 為 NULL 的記錄")

                        # 檢查是否有小數部分
                        non_null_values = data_df_clean[col].dropna()
                        if len(non_null_values) > 0 and current_dtype.startswith('float'):
                            has_decimals = (non_null_values % 1 != 0).any()
                            if has_decimals:
                                logger.warning(f"欄位 {col} 包含小數部分，將進行截斷")

                        # 執行類型轉換前，先安全地處理空字符串和 None
                        # 對於整數欄位，將空字符串和 None 轉為 0
                        data_df_clean[col] = pd.to_numeric(data_df_clean[col], errors='coerce').fillna(0)

                        # 執行類型轉換
                        try:
                            data_df_clean[col] = data_df_clean[col].astype(target_dtype)
                            logger.info(f"轉換完成，{col} 現在類型為: {data_df_clean[col].dtype}")
                        except Exception as e:
                            logger.error(f"轉換欄位 {col} 類型時發生錯誤: {str(e)}")
                            raise

            # 確保浮點數欄位的類型正確
            float_columns = {
                'total_purchase_amount': 'float64'
            }

            for col, target_dtype in float_columns.items():
                if col in data_df_clean.columns:
                    current_dtype = str(data_df_clean[col].dtype)
                    if current_dtype not in ['float64', 'float32']:
                        logger.info(f"將欄位 {col} 從 {current_dtype} 轉換為 {target_dtype}")

                        # 對於浮點數欄位，也先安全地處理空字符串和 None
                        data_df_clean[col] = pd.to_numeric(data_df_clean[col], errors='coerce').fillna(0.0)

                        # 執行類型轉換
                        try:
                            data_df_clean[col] = data_df_clean[col].astype(target_dtype)
                            logger.info(f"轉換完成，{col} 現在類型為: {data_df_clean[col].dtype}")
                        except Exception as e:
                            logger.error(f"轉換欄位 {col} 類型時發生錯誤: {str(e)}")
                            raise

            # 確保時間欄位都已正確轉換
            # 常見的時間欄位集合
            time_columns = [
                'registration_time', 'first_interaction_time', 'last_interaction_time',
                'first_purchase_time', 'last_purchase_time'
            ]

            # 檢查每個已知的時間欄位是否存在，並轉換為 datetime 格式
            for col in time_columns:
                if col in data_df_clean.columns:
                    logger.info(f"檢測到預定義時間欄位: {col}")
                    # 轉換時間欄位 (如果有空值，使用 pd.to_datetime 的 errors='coerce' 選項)
                    data_df_clean[col] = pd.to_datetime(data_df_clean[col], errors='coerce')

            # 使用 GCSFileSystem 直接上傳到 GCS
            # 為 gcsfs 添加正確的 bucket 名稱
            fs = gcsfs.GCSFileSystem()
            full_gcs_path = f"{self.bucket_name}/{gcs_path}"

            # 檢查路徑是否已存在
            if fs.exists(full_gcs_path) and not in_test_env:
                logger.warning(f"快照檔案已存在: {full_gcs_path}，將被覆蓋")

            # 將 DataFrame 儲存為 Parquet 格式
            try:
                with fs.open(full_gcs_path, 'wb') as f:
                    data_df_clean.to_parquet(f, index=False)
                logger.info(f"快照儲存成功: gs://{full_gcs_path}")
            except Exception as e:
                logger.error(f"使用 gcsfs 儲存快照時發生錯誤: {str(e)}")
                raise

            # 返回完整的 GCS 路徑，以便後續讀取時能正確識別
            complete_gcs_path = f"gs://{full_gcs_path}"
            return {
                "success": True,
                "path": complete_gcs_path,  # 返回 gs:// 前綴的完整路徑
                "records": len(data_df_clean),
                "columns": data_df_clean.columns.tolist(),
                "utc_date": utc_date_str,
                "taiwan_date": taiwan_date_str
            }

        except Exception as e:
            logger.error(f"儲存快照失敗: {str(e)}")
            # 輸出詳細的錯誤堆疊
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "error": str(e)
            }

    def load_audience_rules(self, ec_id: int, add_to_dxp: bool = True) -> dict:
        """載入受眾規則"""
        logger.info(f"loading audience rules: {ec_id}, {add_to_dxp}")

        # 獲取基礎路徑，測試環境使用 LTA_TEST 前綴
        base_path = _get_gcs_base_path("user_stats_configs")
        blob = self.bucket.blob(f'{base_path}/audience_rules.json')

        content = blob.download_as_text()
        all_rules = json.loads(content)

        ec_rules = all_rules.get(str(ec_id), {}).get('rules', {})
        prefix = f"tm:c_{'9999_' if add_to_dxp else ''}{ec_id}_c_"

        return {
            rule_id: rule_data
            for rule_id, rule_data in ec_rules.items()
            if rule_id.startswith(prefix)
        }

    def get_snapshot_path(self, ec_id: int, date: datetime) -> str:
        """獲取快照路徑

        Args:
            ec_id: 電商 ID
            date: 日期 (UTC)

        Returns:
            str: 快照路徑
        """
        # 使用 UTC 日期格式化檔案名稱
        utc_date_str = date.strftime('%Y%m%d')
        return f"/tmp/user_stats_{ec_id}_{utc_date_str}.parquet"

    def load_snapshot(self, date: datetime, ec_id: int = None) -> pd.DataFrame:
        """從 GCS 讀取使用者統計資料快照

        Args:
            date: 日期 (UTC)
            ec_id: 電商 ID，如果提供則讀取特定電商的快照

        Returns:
            pd.DataFrame: 載入的使用者統計資料
        """
        # 使用 UTC 日期格式化檔案名稱
        utc_date_str = date.strftime('%Y%m%d')

        # 獲取基礎路徑，測試環境使用 LTA_TEST 前綴
        base_path = _get_gcs_base_path("user_stats_snapshots")

        # 確定檔案路徑
        if ec_id is not None:
            # 使用新格式路徑（按電商分類）
            blob_name = f"{base_path}/ec_{ec_id}/{utc_date_str}.parquet"
        else:
            # 使用舊格式路徑
            blob_name = f"{base_path}/user_stats_{utc_date_str}.parquet"

        blob = self.bucket.blob(blob_name)

        # 增加更詳細的存在性檢查
        exists = blob.exists()
        logger.info(f"GCS 快照檢查: gs://{self.bucket.name}/{blob_name} {'存在' if exists else '不存在'}")

        if not exists:
            # 如果新格式不存在且有指定 ec_id，嘗試舊格式
            if ec_id is not None:
                old_blob_name = f"{base_path}/user_stats_{utc_date_str}.parquet"
                old_blob = self.bucket.blob(old_blob_name)
                if old_blob.exists():
                    logger.info(f"找到舊格式快照: gs://{self.bucket.name}/{old_blob_name}")
                    blob = old_blob
                    blob_name = old_blob_name
                    exists = True

            # 檢查是否在測試環境中
            if not exists:
                in_test_env = os.environ.get('PYTEST_CURRENT_TEST') is not None
                # 如果在測試環境，也檢查台灣時間格式的檔名（向後兼容）
                if in_test_env:
                    taiwan_date_str = get_taiwan_date_str(date)
                    if utc_date_str != taiwan_date_str:
                        if ec_id is not None:
                            alt_blob_name = f"{base_path}/ec_{ec_id}/{taiwan_date_str}.parquet"
                        else:
                            alt_blob_name = f"{base_path}/user_stats_{taiwan_date_str}.parquet"
                        alt_blob = self.bucket.blob(alt_blob_name)
                        if alt_blob.exists():
                            logger.info(f"找到舊格式快照: gs://{self.bucket.name}/{alt_blob_name}")
                            blob = alt_blob
                            blob_name = alt_blob_name
                            exists = True

            if not exists:
                logger.info(f"➤ 返回空 DataFrame，將從 BigQuery 獲取資料")
                return pd.DataFrame()

        try:
            logger.info(f"開始下載 GCS 快照: {blob_name}")
            with tempfile.NamedTemporaryFile(suffix='.parquet') as temp_file:
                blob.download_to_filename(temp_file.name)
                df = pd.read_parquet(temp_file.name)

                # 確保時間欄位是 timestamp 格式
                time_columns = [
                    'last_interaction_time', 'registration_time', 'first_interaction_time',
                    'first_purchase_time', 'last_purchase_time'
                ]
                for col in time_columns:
                    if col in df.columns and not pd.api.types.is_datetime64_any_dtype(df[col]):
                        logger.info(f"將 GCS 資料中的欄位 {col} 轉換為 timestamp 格式")
                        df[col] = pd.to_datetime(df[col], errors='coerce', utc=True)

                # 確保 ec_id 欄位是 int64 類型
                if 'ec_id' in df.columns:
                    # 檢查是否有 NULL 值
                    na_mask = df['ec_id'].isna()
                    if na_mask.any():
                        null_count = na_mask.sum()
                        logger.error(f"ec_id 欄位不允許有 NULL 值，但發現 {null_count} 筆 NULL 記錄")
                        raise ValueError(f"ec_id 欄位包含 {null_count} 筆 NULL 值")

                    # 檢查是否為浮點類型，並轉換為整數類型
                    if str(df['ec_id'].dtype).startswith('float') or str(df['ec_id'].dtype) == 'Float64':
                        logger.info(f"從 GCS 讀取的資料中 ec_id 為浮點類型 ({df['ec_id'].dtype})，轉換為 int64")
                        # 檢查是否有小數部分，如有則警告
                        has_decimals = (df['ec_id'] % 1 != 0).any()
                        if has_decimals:
                            logger.warning("ec_id 欄位包含小數部分，將進行截斷")

                        # 轉換為 int64
                        df['ec_id'] = df['ec_id'].astype('int64')
                        logger.info(f"轉換完成，ec_id 現在類型為: {df['ec_id'].dtype}")
                    elif df['ec_id'].dtype != 'int64' and df['ec_id'].dtype != 'int32':
                        logger.info(f"將 GCS 資料中的欄位 ec_id 從 {df['ec_id'].dtype} 轉換為 int64 格式")
                        # 轉換為標準 int64 型別
                        df['ec_id'] = df['ec_id'].astype('int64')

                # 確保 total_sessions 欄位是整數類型
                if 'total_sessions' in df.columns:
                    current_dtype = str(df['total_sessions'].dtype)
                    if current_dtype.startswith('float') or current_dtype == 'Float64':
                        logger.info(f"從 GCS 讀取的資料中 total_sessions 為浮點類型 ({df['total_sessions'].dtype})，轉換為 int64")

                        # 檢查是否有 NaN 值，如果有則填充為 0
                        na_mask = df['total_sessions'].isna()
                        if na_mask.any():
                            null_count = na_mask.sum()
                            logger.warning(f"total_sessions 欄位包含 {null_count} 個 NULL 值，將填充為 0")
                            df['total_sessions'] = df['total_sessions'].fillna(0)

                        # 檢查是否有小數部分，如有則警告
                        non_null_values = df['total_sessions'].dropna()
                        if len(non_null_values) > 0:
                            has_decimals = (non_null_values % 1 != 0).any()
                            if has_decimals:
                                logger.warning("total_sessions 欄位包含小數部分，將進行截斷")

                        # 轉換為 int64
                        df['total_sessions'] = df['total_sessions'].astype('int64')
                        logger.info(f"轉換完成，total_sessions 現在類型為: {df['total_sessions'].dtype}")
                    elif current_dtype not in ['int64', 'int32', 'Int64']:
                        logger.info(f"將 GCS 資料中的欄位 total_sessions 從 {df['total_sessions'].dtype} 轉換為 int64 格式")
                        # 先處理可能的 NaN 值
                        if df['total_sessions'].isna().any():
                            logger.warning(f"total_sessions 欄位包含 NULL 值，將填充為 0")
                            df['total_sessions'] = df['total_sessions'].fillna(0)
                        # 轉換為標準 int64 型別
                        df['total_sessions'] = df['total_sessions'].astype('int64')

                # 確保 purchase_count 欄位是整數類型
                if 'purchase_count' in df.columns:
                    current_dtype = str(df['purchase_count'].dtype)
                    if current_dtype.startswith('float') or current_dtype == 'Float64':
                        logger.info(f"從 GCS 讀取的資料中 purchase_count 為浮點類型 ({df['purchase_count'].dtype})，轉換為 int64")

                        # 檢查是否有 NaN 值，如果有則填充為 0
                        na_mask = df['purchase_count'].isna()
                        if na_mask.any():
                            null_count = na_mask.sum()
                            logger.warning(f"purchase_count 欄位包含 {null_count} 個 NULL 值，將填充為 0")
                            df['purchase_count'] = df['purchase_count'].fillna(0)

                        # 檢查是否有小數部分，如有則警告
                        non_null_values = df['purchase_count'].dropna()
                        if len(non_null_values) > 0:
                            has_decimals = (non_null_values % 1 != 0).any()
                            if has_decimals:
                                logger.warning("purchase_count 欄位包含小數部分，將進行截斷")

                        # 轉換為 int64
                        df['purchase_count'] = df['purchase_count'].astype('int64')
                        logger.info(f"轉換完成，purchase_count 現在類型為: {df['purchase_count'].dtype}")
                    elif current_dtype not in ['int64', 'int32', 'Int64', 'UInt16']:
                        logger.info(f"將 GCS 資料中的欄位 purchase_count 從 {df['purchase_count'].dtype} 轉換為 int64 格式")
                        # 先處理可能的 NaN 值
                        if df['purchase_count'].isna().any():
                            logger.warning(f"purchase_count 欄位包含 NULL 值，將填充為 0")
                            df['purchase_count'] = df['purchase_count'].fillna(0)
                        # 轉換為標準 int64 型別
                        df['purchase_count'] = df['purchase_count'].astype('int64')

            logger.info(f"成功下載 GCS 快照，資料量: {len(df)} 筆")
            return df
        except Exception as e:
            logger.error(f"下載 GCS 快照失敗: {str(e)}")
            return pd.DataFrame()

    def download_snapshot(self, snapshot_date: str, ec_id: int = None) -> str:
        """從 GCS 下載快照檔案到本地暫存位置

        Args:
            snapshot_date: 快照日期 (YYYYMMDD 格式)
            ec_id: 電商 ID，如果提供則下載特定電商的快照

        Returns:
            str: 下載後的本地檔案路徑，如果找不到檔案則返回 None
        """
        # 獲取基礎路徑，測試環境使用 LTA_TEST 前綴
        base_path = _get_gcs_base_path("user_stats_snapshots")
        logger.info(f"使用路徑前綴: {base_path}")

        # 建構完整的 blob 路徑
        if ec_id is not None:
            # 使用新格式路徑（按電商分類）
            blob_name = f"{base_path}/ec_{ec_id}/{snapshot_date}.parquet"
        else:
            # 使用舊格式路徑
            blob_name = f"{base_path}/user_stats_{snapshot_date}.parquet"

        logger.info(f"嘗試從 gs://{self.bucket_name}/{blob_name} 下載快照")

        # 檢查檔案是否存在
        blob = self.bucket.blob(blob_name)
        if not blob.exists():
            # 如果新格式不存在且有指定 ec_id，嘗試舊格式
            if ec_id is not None:
                alt_blob_name = f"{base_path}/user_stats_{snapshot_date}.parquet"
                logger.info(f"新格式不存在，嘗試舊格式: gs://{self.bucket_name}/{alt_blob_name}")
                alt_blob = self.bucket.blob(alt_blob_name)
                if alt_blob.exists():
                    logger.info(f"找到舊格式快照")
                    blob = alt_blob
                    blob_name = alt_blob_name
                else:
                    logger.warning(f"找不到快照檔案: {snapshot_date}" + (f" (ec_id: {ec_id})" if ec_id else ""))
                    return None
            else:
                logger.warning(f"找不到快照檔案: {snapshot_date}")
                return None

        # 下載到臨時檔案
        local_file_path = f"/tmp/user_stats_{snapshot_date}.parquet"
        blob.download_to_filename(local_file_path)
        logger.info(f"成功下載 GCS 快照 {blob_name} 到 {local_file_path}")

        return local_file_path

# Helper function to get GCS base path based on environment (moved to top-level for broader use if needed)
# def _get_gcs_base_path(resource_type: str = "user_stats_snapshots") -> str:
#     # ... (implementation as above)
#     pass

