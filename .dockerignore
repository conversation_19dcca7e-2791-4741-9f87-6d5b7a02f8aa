# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Test and coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Logs
logs/
*.log

# Local development
.env
.env.local

# Build artifacts
Dockerfile
.dockerignore
cloudbuild.yaml

# Documentation
docs/_build/

# macOS
.DS_Store

# Testing
test-reports/
.coverage.*
htmlcov/

# Local output files
init_snapshot/
local_data/
local_output/
logs/
payload.json
