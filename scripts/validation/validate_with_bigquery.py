#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
驗證腳本 2：與 BigQuery 直接查詢結果比較

此腳本用於比較 init_user_stats_for_ec_id.py 產出的 parquet 檔案
與直接從 BigQuery 使用相同邏輯查詢的結果，確認計算邏輯是否正確。

使用方式:
    python scripts/validation/validate_with_bigquery.py \
        --local-file ./local_data/user_stats_ec107_20250311_154703.parquet \
        --ec-id 107 \
        --start-date 2022-01-01 \
        --end-date 2025-03-10 \
        --report-file ./validation_bigquery_report.html

選項:
    --local-file PATH: 必填，本地 parquet 檔案路徑
    --ec-id EC_ID: 必填，電商 ID
    --start-date DATE: 必填，開始日期，格式為 YYYY-MM-DD
    --end-date DATE: 必填，結束日期，格式為 YYYY-MM-DD
    --sample-size SIZE: 選填，抽樣大小，默認為 1000
    --sample-rate RATE: 選填，抽樣率 (0-100)，例如：100 表示全部比對，50 表示比對一半。此參數優先於 --sample-size
    --report-file PATH: 選填，HTML 報告輸出路徑
    --max-compare INT: 選填，最大比較數量，防止大資料集處理時間過長
    --force-full-compare: 選填，強制比較全部資料，即使數量很大
"""

import argparse
import os
import sys
import time
import math
from datetime import datetime, timezone, timedelta
from pathlib import Path
import logging
import pandas as pd
import numpy as np
from google.cloud import bigquery
import pytz
from src.core.cloud_integration import BigQueryClient

# 將專案根目錄加入 sys.path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.append(str(ROOT_DIR))

from src.utils import logging_setup
from src.core.cloud_integration import BigQueryClient

# 設定 logger
logger = logging_setup.configure_logging()

def format_bytes(size_bytes):
    """格式化位元組大小為人類可讀格式"""
    if size_bytes == 0:
        return "0 B"
    size_name = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_name[i]}" if i > 0 else f"{int(s)} {size_name[i]}"

def parse_args():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(description="驗證本地 parquet 檔案與 BigQuery 資料的一致性")
    parser.add_argument("--local-file", "-l", required=True, help="本地 parquet 檔案路徑")
    parser.add_argument("--ec-id", "-e", required=True, type=int, help="電商 ID")
    parser.add_argument("--start-date", "-s", required=True, help="開始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", "-n", required=True, help="結束日期 (YYYY-MM-DD)")
    parser.add_argument("--sample-size", "-m", type=int, help="要比較的樣本數量")
    parser.add_argument("--sample-rate", "-r", type=float, default=10, help="要比較的資料比例 (%%)")
    parser.add_argument("--report-file", "-o", help="輸出報告的檔案路徑")
    parser.add_argument("--max-compare", type=int, default=10000, help="最大比較數量，防止大資料集處理時間過長")
    parser.add_argument("--force-full-compare", action="store_true", help="強制比較全部資料，即使數量很大")
    return parser.parse_args()

def query_bigquery(ec_id, start_date, end_date, sample_size=1000):
    """直接從 BigQuery 查詢用戶統計資料

    Args:
        ec_id: 電商 ID
        start_date: 開始日期
        end_date: 結束日期
        sample_size: 抽樣大小，如果為 None 則不限制查詢筆數

    Returns:
        pd.DataFrame: 用戶統計資料
    """
    logger.info(f"從 BigQuery 查詢 EC ID {ec_id} 的用戶統計資料")

    # 轉換日期為帶時區的 timestamp
    taipei_tz = pytz.timezone('Asia/Taipei')
    start_datetime = taipei_tz.localize(datetime.strptime(f"{start_date} 00:00:00", "%Y-%m-%d %H:%M:%S"))
    end_datetime = taipei_tz.localize(datetime.strptime(f"{end_date} 00:00:00", "%Y-%m-%d %H:%M:%S"))

    # 轉換為 UTC 時間
    start_utc = start_datetime.astimezone(timezone.utc)
    end_utc = end_datetime.astimezone(timezone.utc)

    logger.info(f"查詢時間範圍: {start_datetime.strftime('%Y-%m-%d %H:%M:%S %Z')} 到 {end_datetime.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    logger.info(f"UTC 時間範圍: {start_utc.isoformat()} 到 {end_utc.isoformat()}")

    # 直接使用初始化腳本中相同的查詢邏輯
    query = f"""
    WITH VisitorData AS (
        SELECT
            ec_id,
            permanent,
            MIN(event_time) AS first_interaction_time,
            MAX(event_time) AS last_interaction_time,
            COUNT(DISTINCT session.id) AS total_sessions
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE
            ec_id = {ec_id}
            AND event_time >= TIMESTAMP('{start_utc.isoformat()}')
            AND event_time < TIMESTAMP('{end_utc.isoformat()}')
        GROUP BY
            ec_id, permanent
    ),
    PurchaseData AS (
        SELECT
            ec_id,
            permanent,
            MIN(event_time) AS first_purchase_time,
            MAX(event_time) AS last_purchase_time,
            COUNT(*) AS purchase_count,
            SUM(IFNULL(event.value, 0)) AS total_purchase_amount
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE
            ec_id = {ec_id}
            AND event_time >= TIMESTAMP('{start_utc.isoformat()}')
            AND event_time < TIMESTAMP('{end_utc.isoformat()}')
            AND event.name = 'purchase'
        GROUP BY
            ec_id, permanent
    ),
    RegistrationData AS (
        SELECT
            ec_id,
            permanent,
            MIN(event_time) AS registration_time
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE
            ec_id = {ec_id}
            AND event_time >= TIMESTAMP('{start_utc.isoformat()}')
            AND event_time < TIMESTAMP('{end_utc.isoformat()}')
            AND event.name = 'register'
        GROUP BY
            ec_id, permanent
    )
    SELECT
        V.ec_id,
        V.permanent,
        IFNULL(P.purchase_count, 0) AS purchase_count,
        R.registration_time,
        V.first_interaction_time,
        V.last_interaction_time,
        P.first_purchase_time,
        P.last_purchase_time,
        IFNULL(P.total_purchase_amount, 0) AS total_purchase_amount,
        V.total_sessions
    FROM
        VisitorData V
    LEFT JOIN
        PurchaseData P
    ON
        V.ec_id = P.ec_id AND V.permanent = P.permanent
    LEFT JOIN
        RegistrationData R
    ON
        V.ec_id = R.ec_id AND V.permanent = R.permanent
    """ + (f"\nLIMIT {sample_size}" if sample_size is not None else "")

    # 估算查詢費用
    logger.info("估算 BigQuery 查詢費用...")
    bq_client = BigQueryClient()
    cost_info = bq_client.estimate_query_cost(query)

    bytes_processed = cost_info.get('bytes_processed', 0)
    tb_processed = cost_info.get('tb_processed', 0)
    estimated_cost_usd = cost_info.get('estimated_cost_usd', 0)

    # 格式化費用資訊，使其更易讀
    bytes_formatted = format_bytes(bytes_processed)

    logger.info(f"查詢費用估算:")
    logger.info(f"  • 預估處理數據量: {bytes_formatted} ({tb_processed:.6f} TB)")
    logger.info(f"  • 預估費用: ${estimated_cost_usd:.6f} USD")

    # 如果費用超過一定閾值，給出警告
    if estimated_cost_usd > 1.0:
        logger.warning(f"⚠️ 注意: 此查詢預估費用為 ${estimated_cost_usd:.2f} USD，超過 $1.00 USD")

    # 詢問用戶是否繼續
    if estimated_cost_usd > 5.0 and not os.environ.get('FORCE_QUERY', '').lower() in ('true', '1', 'yes'):
        user_input = input(f"⚠️ 此查詢預估費用較高 (${estimated_cost_usd:.2f} USD)，是否繼續? (y/N): ")
        if user_input.lower() not in ('y', 'yes'):
            logger.info("已取消高成本查詢")
            return pd.DataFrame()

    logger.info("執行 BigQuery 查詢...")
    query_start_time = time.time()

    # 執行查詢並轉換為 DataFrame
    query_job = bq_client.client.query(query)
    results = query_job.result()
    user_stats_df = results.to_dataframe()

    query_duration = time.time() - query_start_time
    logger.info(f"查詢完成，耗時 {query_duration:.2f} 秒")
    logger.info(f"查詢結果中有 {len(user_stats_df)} 筆用戶記錄")

    return user_stats_df

def choose_comparison_users(local_df, bq_df, sample_size=100, sample_rate=None):
    """選擇用於比較的用戶

    Args:
        local_df: 本地 parquet 檔案的 DataFrame
        bq_df: BigQuery 查詢結果的 DataFrame
        sample_size: 抽樣大小
        sample_rate: 抽樣率 (0-100)，優先於 sample_size

    Returns:
        list: 用於比較的用戶 ID 列表
    """
    # 找出兩個 DataFrame 中共同的用戶
    common_users = set(local_df['permanent']) & set(bq_df['permanent'])
    logger.info(f"兩個資料集中共有 {len(common_users)} 個共同用戶")

    if len(common_users) == 0:
        logger.warning("沒有共同用戶，無法進行比較")
        return []

    # 如果有指定抽樣率，則根據抽樣率計算樣本大小
    if sample_rate is not None:
        if sample_rate <= 0 or sample_rate > 100:
            logger.warning(f"抽樣率 {sample_rate} 無效，必須在 0-100 之間，將使用預設抽樣大小")
        else:
            sample_size = int(len(common_users) * sample_rate / 100)
            if sample_rate == 100:
                logger.info("使用全部資料進行比對")
            else:
                logger.info(f"根據抽樣率 {sample_rate}% 計算樣本大小: {sample_size}")

    # 從共同用戶中抽樣
    sample_size = min(sample_size, len(common_users))
    comparison_users = list(common_users)[:sample_size]
    logger.info(f"選擇 {len(comparison_users)} 個用戶進行詳細比較 ({len(comparison_users)/len(common_users)*100:.2f}% 的資料)")

    return comparison_users

def compare_users(local_df, bq_df, user_ids):
    """比較特定用戶的資料

    Args:
        local_df: 本地 parquet 檔案的 DataFrame
        bq_df: BigQuery 查詢結果的 DataFrame
        user_ids: 要比較的用戶 ID 列表

    Returns:
        dict: 比較結果
    """
    import multiprocessing
    from concurrent.futures import ProcessPoolExecutor, as_completed
    import tqdm

    # 計算可用核心數量，留出一個核心給系統使用
    num_cores = max(1, multiprocessing.cpu_count() - 1)
    logger.info(f"使用 {num_cores} 個處理器核心進行平行比對")

    results = {
        'users': [],
        'numeric_columns': ['purchase_count', 'total_purchase_amount', 'total_sessions'],
        'datetime_columns': ['first_interaction_time', 'last_interaction_time', 'first_purchase_time', 'last_purchase_time', 'registration_time'],
        'summary': {
            'total_users': len(user_ids),
            'matching_users': 0,
            'mismatching_users': 0,
            'column_mismatches': {}
        }
    }

    # 初始化欄位不匹配計數
    for col in results['numeric_columns'] + results['datetime_columns']:
        results['summary']['column_mismatches'][col] = 0

    # 創建本地和 BQ 資料的字典，加速查詢
    logger.info("建立索引加速比對...")
    local_user_dict = {user_id: row.to_dict() for user_id, row in local_df.set_index('permanent').iterrows() if user_id in user_ids}
    bq_user_dict = {user_id: row.to_dict() for user_id, row in bq_df.set_index('permanent').iterrows() if user_id in user_ids}

    # 批次處理，將用戶 ID 分成多個批次
    batch_size = max(100, len(user_ids) // (num_cores * 10))  # 每個處理器至少處理 10 個批次
    user_batches = [user_ids[i:i + batch_size] for i in range(0, len(user_ids), batch_size)]
    logger.info(f"將 {len(user_ids)} 個用戶分成 {len(user_batches)} 個批次進行處理")

    def compare_user_batch(batch):
        """比較一批用戶"""
        batch_results = []

        for user_id in batch:
            local_user = local_user_dict.get(user_id)
            bq_user = bq_user_dict.get(user_id)

            if local_user is None or bq_user is None:
                continue

            user_result = {
                'permanent': user_id,
                'match': True,
                'mismatches': []
            }

            # 比較數值欄位
            for col in results['numeric_columns']:
                local_val = float(local_user.get(col, 0) or 0)
                bq_val = float(bq_user.get(col, 0) or 0)

                # 檢查數值是否相等（容許微小差異）
                if abs(local_val - bq_val) > 1e-6:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'local_value': local_val,
                        'bq_value': bq_val,
                        'difference': local_val - bq_val,
                        'pct_difference': ((local_val - bq_val) / bq_val * 100) if bq_val != 0 else None
                    })

            # 比較時間欄位
            for col in results['datetime_columns']:
                local_val = local_user.get(col)
                bq_val = bq_user.get(col)

                # 如果兩者都是 None 或 NaT，視為相等
                if pd.isna(local_val) and pd.isna(bq_val):
                    continue

                # 如果只有一個是 None 或 NaT，視為不等
                if pd.isna(local_val) != pd.isna(bq_val):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'local_value': str(local_val),
                        'bq_value': str(bq_val),
                        'difference': 'NULL 值不一致'
                    })
                    continue

                # 否則，比較時間值
                try:
                    local_dt = pd.Timestamp(local_val).tz_convert(None) if hasattr(local_val, 'tz_convert') else pd.Timestamp(local_val)
                    bq_dt = pd.Timestamp(bq_val).tz_convert(None) if hasattr(bq_val, 'tz_convert') else pd.Timestamp(bq_val)

                    if local_dt != bq_dt:
                        time_diff_seconds = (local_dt - bq_dt).total_seconds()

                        # 如果時差超過 1 秒，視為不等
                        if abs(time_diff_seconds) > 1:
                            user_result['match'] = False
                            user_result['mismatches'].append({
                                'column': col,
                                'local_value': str(local_val),
                                'bq_value': str(bq_val),
                                'difference': f"{time_diff_seconds} 秒"
                            })
                except Exception as e:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'local_value': str(local_val),
                        'bq_value': str(bq_val),
                        'difference': f"比較錯誤: {str(e)}"
                    })

            batch_results.append(user_result)

        return batch_results

    # 使用多處理器平行比較用戶
    all_user_results = []
    logger.info("開始平行比對用戶資料...")

    # 添加進度顯示
    with ProcessPoolExecutor(max_workers=num_cores) as executor:
        futures = [executor.submit(compare_user_batch, batch) for batch in user_batches]

        # 添加進度條
        for future in tqdm.tqdm(as_completed(futures), total=len(futures), desc="比對進度"):
            try:
                batch_results = future.result()
                all_user_results.extend(batch_results)
            except Exception as e:
                logger.error(f"處理批次時發生錯誤: {str(e)}")

    # 整合結果
    results['users'] = all_user_results

    # 計算統計資料
    for user_result in results['users']:
        if user_result['match']:
            results['summary']['matching_users'] += 1
        else:
            results['summary']['mismatching_users'] += 1

            # 更新欄位不匹配計數
            for mismatch in user_result['mismatches']:
                col = mismatch['column']
                if col in results['summary']['column_mismatches']:
                    results['summary']['column_mismatches'][col] += 1

    return results

def generate_html_report(comparison_results, local_file, ec_id, start_date, end_date, report_file):
    """生成 HTML 比較報告

    Args:
        comparison_results: 比較結果
        local_file: 本地檔案路徑
        ec_id: 電商 ID
        start_date: 開始日期
        end_date: 結束日期
        report_file: 報告輸出路徑

    Returns:
        str: 報告檔案路徑
    """
    # 摘要統計
    summary = comparison_results['summary']
    matching_percent = (summary['matching_users'] / summary['total_users'] * 100) if summary['total_users'] > 0 else 0

    # 計算總體匹配度
    overall_status = 'success' if matching_percent >= 95 else 'warning' if matching_percent >= 80 else 'error'

    # 生成欄位不匹配的表格
    col_mismatch_rows = ""
    for col, count in summary['column_mismatches'].items():
        if count > 0:
            mismatch_percent = (count / summary['total_users'] * 100)
            status_class = 'success' if mismatch_percent < 5 else 'warning' if mismatch_percent < 20 else 'error'

            col_mismatch_rows += f"""
            <tr>
                <td>{col}</td>
                <td>{count}</td>
                <td class="{status_class}">{mismatch_percent:.2f}%</td>
            </tr>
            """

    # 生成不匹配用戶的表格
    mismatched_users_html = ""
    if summary['mismatching_users'] > 0:
        mismatched_users = [user for user in comparison_results['users'] if not user['match']]

        for user in mismatched_users[:10]:  # 限制只顯示前 10 個，避免報告太長
            mismatches_html = ""
            for mismatch in user['mismatches']:
                mismatches_html += f"""
                <tr>
                    <td>{mismatch['column']}</td>
                    <td>{mismatch['local_value']}</td>
                    <td>{mismatch['bq_value']}</td>
                    <td>{mismatch.get('difference', 'N/A')}</td>
                    <td>{f"{mismatch.get('pct_difference', 'N/A'):.2f}%" if mismatch.get('pct_difference') is not None else 'N/A'}</td>
                </tr>
                """

            mismatched_users_html += f"""
            <div class="user-mismatch">
                <h4>用戶 ID: {user['permanent']}</h4>
                <table>
                    <tr>
                        <th>欄位</th>
                        <th>本地值</th>
                        <th>BigQuery 值</th>
                        <th>差異</th>
                        <th>差異百分比</th>
                    </tr>
                    {mismatches_html}
                </table>
            </div>
            """

        if len(mismatched_users) > 10:
            mismatched_users_html += f"<p>還有 {len(mismatched_users) - 10} 個不匹配的用戶未顯示...</p>"

    # 完整 HTML
    html_content = f"""
    <html>
    <head>
        <title>User Stats BigQuery 驗證報告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3, h4 {{ color: #333; }}
            .summary {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .warning {{ color: orange; }}
            .error {{ color: red; }}
            .success {{ color: green; }}
            table {{ border-collapse: collapse; margin: 15px 0; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .user-mismatch {{ margin-bottom: 30px; }}
        </style>
    </head>
    <body>
        <h1>User Stats BigQuery 驗證報告</h1>
        <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

        <h2>驗證資訊</h2>
        <p>本地檔案: {local_file}</p>
        <p>電商 ID: {ec_id}</p>
        <p>時間範圍: {start_date} 到 {end_date}</p>

        <div class="summary">
            <h2>比較摘要</h2>
            <p>總比較用戶數: {summary['total_users']}</p>
            <p>完全匹配用戶數: {summary['matching_users']} ({matching_percent:.2f}%)</p>
            <p>不匹配用戶數: {summary['mismatching_users']} ({100 - matching_percent:.2f}%)</p>
            <p>整體匹配狀態: <span class="{overall_status}">
                {
                    "完全匹配" if overall_status == 'success' else
                    "部分匹配" if overall_status == 'warning' else
                    "顯著差異"
                }
            </span></p>

            <h3>欄位不匹配統計</h3>
            <table>
                <tr>
                    <th>欄位</th>
                    <th>不匹配用戶數</th>
                    <th>不匹配百分比</th>
                </tr>
                {col_mismatch_rows}
            </table>
        </div>

        <h2>不匹配用戶詳情</h2>
        {mismatched_users_html if summary['mismatching_users'] > 0 else "<p>所有用戶都完全匹配！</p>"}
    </body>
    </html>
    """

    # 寫入檔案
    with open(report_file, 'w') as f:
        f.write(html_content)

    logger.info(f"已生成報告: {report_file}")
    return report_file

def main():
    """主函數"""
    # 解析命令列參數
    args = parse_args()

    # 配置日誌
    # 設置日誌格式
    logger = logging.getLogger()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s')

    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    else:
        logger.handlers[0].setFormatter(formatter)

    logger.setLevel(logging.INFO)

    # 檢查本地檔案是否存在
    if not os.path.exists(args.local_file):
        logger.error(f"本地檔案不存在: {args.local_file}")
        sys.exit(1)

    try:
        # 讀取本地 Parquet 檔案
        logger.info(f"讀取本地 Parquet 檔案: {args.local_file}")
        local_df = pd.read_parquet(args.local_file)
        logger.info(f"本地檔案包含 {len(local_df)} 筆用戶資料")

        # 限制最大比較數量，避免處理時間過長
        if args.sample_rate == 100 and len(local_df) > args.max_compare and args.sample_size is None and not args.force_full_compare:
            logger.warning(f"資料量過大 ({len(local_df)} 筆)，將自動限制比較數量為 {args.max_compare} 筆")
            args.sample_size = args.max_compare
            args.sample_rate = (args.max_compare / len(local_df)) * 100
            logger.info(f"使用取樣率: {args.sample_rate:.2f}%")

        # 從 BigQuery 查詢對應資料
        # 如果要比對全部資料，則不限制查詢筆數
        bq_sample_size = None if args.sample_rate == 100 else args.sample_size
        bq_df = query_bigquery(args.ec_id, args.start_date, args.end_date, bq_sample_size)

        # 選擇要比較的用戶
        comparison_users = choose_comparison_users(local_df, bq_df, args.sample_size, args.sample_rate)

        if not comparison_users:
            logger.error("沒有可比較的用戶，驗證終止")
            sys.exit(1)

        # 比較用戶資料
        logger.info("開始比較用戶資料...")
        comparison_results = compare_users(local_df, bq_df, comparison_users)

        # 生成 HTML 報告
        report_path = generate_html_report(comparison_results, args.local_file, args.ec_id, args.start_date, args.end_date, args.report_file)

        logger.info(f"驗證報告已生成: {report_path}")
        logger.info(f"匹配度: {comparison_results['summary']['matching_users']} / {comparison_results['summary']['total_users']} ({comparison_results['summary']['matching_users'] / comparison_results['summary']['total_users'] * 100:.2f}%)")

        # 嘗試在瀏覽器中打開報告
        if sys.stdout.isatty():
            try:
                import webbrowser
                logger.info(f"嘗試在瀏覽器中打開報告...")
                webbrowser.open(f"file://{os.path.abspath(report_path)}")
            except Exception as e:
                logger.warning(f"無法自動打開報告: {str(e)}")

        # 根據匹配度設置退出碼
        if comparison_results['summary']['matching_users'] == comparison_results['summary']['total_users']:
            logger.info("驗證成功：所有用戶都完全匹配！")
            sys.exit(0)
        elif comparison_results['summary']['matching_users'] / comparison_results['summary']['total_users'] >= 0.95:
            logger.warning("驗證通過：大部分用戶匹配，但有少量差異")
            sys.exit(0)
        else:
            logger.error("驗證失敗：存在顯著差異")
            sys.exit(1)
    except Exception as e:
        logger.error(f"驗證過程中發生錯誤: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()