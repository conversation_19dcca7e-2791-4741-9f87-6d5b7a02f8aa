#!/usr/bin/env python3
"""
Parquet Time Range Checker

這個腳本用於檢查存儲在 Google Cloud Storage 中的 parquet 檔案的時間範圍，
幫助了解數據覆蓋範圍並識別時間序列數據中的任何空隙。

功能:
- 列出指定 GCS bucket 中的 parquet 檔案
- 提取每個檔案的時間戳資訊
- 分析時間覆蓋範圍
- 識別時間序列中的空隙
- 生成詳細的報告

使用方法:
    python parquet_time_range_checker.py --bucket-name my-bucket --prefix data/path/
"""

import argparse
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Set
import pandas as pd
import pyarrow.parquet as pq
from google.cloud import storage
import gcsfs
from dataclasses import dataclass
import logging
from pathlib import Path

# 將 src 目錄加入 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.utils.logging_setup import configure_logging
from src.utils.time_utils import format_datetime_with_timezone, get_taiwan_date_str

logger = configure_logging()

@dataclass
class ParquetFileInfo:
    """Parquet 檔案資訊"""
    path: str
    size_bytes: int
    modified_time: datetime
    min_timestamp: Optional[datetime] = None
    max_timestamp: Optional[datetime] = None
    row_count: Optional[int] = None
    timestamp_columns: List[str] = None


    def __post_init__(self):
        if self.timestamp_columns is None:
            self.timestamp_columns = []

@dataclass
class TimeGap:
    """時間空隙資訊"""
    start_time: datetime
    end_time: datetime
    duration: timedelta

    @property
    def duration_hours(self) -> float:
        return self.duration.total_seconds() / 3600


    @property
    def duration_hours(self) -> float:
        return self.duration.total_seconds() / 3600

    @property
    def duration_days(self) -> float:
        return self.duration.total_seconds() / (3600 * 24)

class ParquetTimeRangeChecker:
    """Parquet 檔案時間範圍檢查器"""

    def __init__(self, bucket_name: str, prefix: str = "",
                 timestamp_columns: List[str] = None):
        """
        初始化檢查器

        Args:
            bucket_name: GCS bucket 名稱
            prefix: 檔案路徑前綴
            timestamp_columns: 要檢查的時間戳欄位名稱列表
        """
        self.bucket_name = bucket_name
        self.prefix = prefix
        self.timestamp_columns = timestamp_columns or [
            'timestamp', 'created_at', 'updated_at', 'event_time',
            'date', 'datetime', 'time', '_timestamp'
        ]

        # 初始化 GCS 客戶端
        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(bucket_name)
        self.fs = gcsfs.GCSFileSystem()

        logger.info(f"初始化 ParquetTimeRangeChecker: bucket={bucket_name}, prefix={prefix}")

    def list_parquet_files(self) -> List[str]:
        """列出所有 parquet 檔案"""
        logger.info(f"正在列出 bucket '{self.bucket_name}' 中前綴為 '{self.prefix}' 的 parquet 檔案...")

        parquet_files = []
        blobs = self.bucket.list_blobs(prefix=self.prefix)

        for blob in blobs:
            if blob.name.endswith('.parquet'):
                parquet_files.append(f"gs://{self.bucket_name}/{blob.name}")

        logger.info(f"找到 {len(parquet_files)} 個 parquet 檔案")
        return parquet_files


        logger.info(f"初始化 ParquetTimeRangeChecker: bucket={bucket_name}, prefix={prefix}")

    def list_parquet_files(self) -> List[str]:
        """列出所有 parquet 檔案"""
        logger.info(f"正在列出 bucket '{self.bucket_name}' 中前綴為 '{self.prefix}' 的 parquet 檔案...")

        parquet_files = []
        blobs = self.bucket.list_blobs(prefix=self.prefix)

        for blob in blobs:
            if blob.name.endswith('.parquet'):
                parquet_files.append(f"gs://{self.bucket_name}/{blob.name}")

        logger.info(f"找到 {len(parquet_files)} 個 parquet 檔案")
        return parquet_files

    def get_file_basic_info(self, file_path: str) -> ParquetFileInfo:
        """獲取檔案基本資訊"""
        try:
            # 移除 gs:// 前綴
            blob_name = file_path.replace(f"gs://{self.bucket_name}/", "")
            blob = self.bucket.blob(blob_name)

            # 獲取檔案元數據
            blob.reload()


            # 獲取檔案元數據
            blob.reload()

            return ParquetFileInfo(
                path=file_path,
                size_bytes=blob.size,
                modified_time=blob.time_created
            )
        except Exception as e:
            logger.warning(f"無法獲取檔案 {file_path} 的基本資訊: {e}")
            return ParquetFileInfo(
                path=file_path,
                size_bytes=0,
                modified_time=datetime.now()
            )

    def extract_timestamp_info(self, file_path: str) -> ParquetFileInfo:
        """從 parquet 檔案中提取時間戳資訊"""
        logger.debug(f"正在分析檔案: {file_path}")

        # 獲取基本檔案資訊
        file_info = self.get_file_basic_info(file_path)


    def extract_timestamp_info(self, file_path: str) -> ParquetFileInfo:
        """從 parquet 檔案中提取時間戳資訊"""
        logger.debug(f"正在分析檔案: {file_path}")

        # 獲取基本檔案資訊
        file_info = self.get_file_basic_info(file_path)

        try:
            # 讀取 parquet 檔案的 schema
            with self.fs.open(file_path, 'rb') as f:
                parquet_file = pq.ParquetFile(f)
                schema = parquet_file.schema

                # 獲取行數
                file_info.row_count = parquet_file.metadata.num_rows


                # 獲取行數
                file_info.row_count = parquet_file.metadata.num_rows

                # 尋找時間戳欄位
                timestamp_cols = []
                for field in schema:
                    field_name = field.name.lower()
                    if any(ts_col in field_name for ts_col in self.timestamp_columns):
                        timestamp_cols.append(field.name)

                file_info.timestamp_columns = timestamp_cols

                if timestamp_cols:
                    # 讀取資料來分析時間範圍
                    df = pd.read_parquet(file_path, columns=timestamp_cols)

                    # 找到最早和最晚的時間戳
                    min_times = []
                    max_times = []


                file_info.timestamp_columns = timestamp_cols

                if timestamp_cols:
                    # 讀取資料來分析時間範圍
                    df = pd.read_parquet(file_path, columns=timestamp_cols)

                    # 找到最早和最晚的時間戳
                    min_times = []
                    max_times = []

                    for col in timestamp_cols:
                        if col in df.columns:
                            col_data = pd.to_datetime(df[col], errors='coerce')
                            col_data = col_data.dropna()

                            if not col_data.empty:
                                min_times.append(col_data.min())
                                max_times.append(col_data.max())

                    if min_times and max_times:
                        file_info.min_timestamp = min(min_times)
                        file_info.max_timestamp = max(max_times)

                        if not col_data.empty:
                            min_times.append(col_data.min())
                            max_times.append(col_data.max())

                    if min_times and max_times:
                        file_info.min_timestamp = min(min_times)
                        file_info.max_timestamp = max(max_times)

                        logger.debug(f"檔案 {file_path} 時間範圍: "
                                   f"{file_info.min_timestamp} - {file_info.max_timestamp}")
                else:
                    logger.warning(f"檔案 {file_path} 中未找到時間戳欄位")

        except Exception as e:
            logger.error(f"讀取檔案 {file_path} 時發生錯誤: {e}")

        return file_info

    def analyze_time_coverage(self, file_infos: List[ParquetFileInfo]) -> Dict:
        """分析時間覆蓋範圍"""
        logger.info("正在分析時間覆蓋範圍...")

        try:
            # 過濾出有時間戳資訊的檔案
            files_with_timestamps = [f for f in file_infos
                                if f.min_timestamp and f.max_timestamp]
        except Exception as e:
            logger.error(f"讀取檔案 {file_path} 時發生錯誤: {e}")

        return file_infos

    def analyze_time_coverage(self, file_infos: List[ParquetFileInfo]) -> Dict:
        """分析時間覆蓋範圍"""
        logger.info("正在分析時間覆蓋範圍...")

        # 過濾出有時間戳資訊的檔案
        files_with_timestamps = [f for f in file_infos
                               if f.min_timestamp and f.max_timestamp]

        if not files_with_timestamps:
            return {
                'status': 'error',
                'message': '沒有找到包含時間戳資訊的檔案'
            }

        # 計算整體時間範圍
        overall_min = min(f.min_timestamp for f in files_with_timestamps)
        overall_max = max(f.max_timestamp for f in files_with_timestamps)

        # 按時間排序檔案
        sorted_files = sorted(files_with_timestamps,
                            key=lambda x: x.min_timestamp)

        # 識別時間空隙
        gaps = self.find_time_gaps(sorted_files)


        # 計算整體時間範圍
        overall_min = min(f.min_timestamp for f in files_with_timestamps)
        overall_max = max(f.max_timestamp for f in files_with_timestamps)

        # 按時間排序檔案
        sorted_files = sorted(files_with_timestamps,
                            key=lambda x: x.min_timestamp)

        # 識別時間空隙
        gaps = self.find_time_gaps(sorted_files)

        # 計算統計資訊
        total_files = len(files_with_timestamps)
        total_rows = sum(f.row_count for f in files_with_timestamps if f.row_count)
        total_size_mb = sum(f.size_bytes for f in file_infos) / (1024 * 1024)


        return {
            'status': 'success',
            'overall_time_range': {
                'start': overall_min,
                'end': overall_max,
                'duration_days': (overall_max - overall_min).days
            },
            'file_count': total_files,
            'total_rows': total_rows,
            'total_size_mb': round(total_size_mb, 2),
            'time_gaps': gaps,
            'files_by_date': self.group_files_by_date(sorted_files)
        }

    def find_time_gaps(self, sorted_files: List[ParquetFileInfo],
                      min_gap_hours: float = 1.0) -> List[TimeGap]:
        """找出時間空隙"""
        gaps = []

        for i in range(len(sorted_files) - 1):
            current_end = sorted_files[i].max_timestamp
            next_start = sorted_files[i + 1].min_timestamp

            if next_start > current_end:
                gap_duration = next_start - current_end


    def find_time_gaps(self, sorted_files: List[ParquetFileInfo],
                      min_gap_hours: float = 1.0) -> List[TimeGap]:
        """找出時間空隙"""
        gaps = []

        for i in range(len(sorted_files) - 1):
            current_end = sorted_files[i].max_timestamp
            next_start = sorted_files[i + 1].min_timestamp

            if next_start > current_end:
                gap_duration = next_start - current_end

                # 只記錄超過最小空隙時間的空隙
                if gap_duration.total_seconds() / 3600 >= min_gap_hours:
                    gaps.append(TimeGap(
                        start_time=current_end,
                        end_time=next_start,
                        duration=gap_duration
                    ))

        return gaps

    def group_files_by_date(self, files: List[ParquetFileInfo]) -> Dict[str, List[str]]:
        """按日期分組檔案"""
        files_by_date = {}


        return gaps

    def group_files_by_date(self, files: List[ParquetFileInfo]) -> Dict[str, List[str]]:
        """按日期分組檔案"""
        files_by_date = {}

        for file_info in files:
            if file_info.min_timestamp:
                date_str = get_taiwan_date_str(file_info.min_timestamp)
                if date_str not in files_by_date:
                    files_by_date[date_str] = []
                files_by_date[date_str].append(file_info.path)

        return files_by_date


        return files_by_date

    def generate_report(self, analysis_result: Dict) -> str:
        """生成分析報告"""
        if analysis_result['status'] != 'success':
            return f"錯誤: {analysis_result['message']}"


        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("PARQUET 檔案時間範圍分析報告")
        report_lines.append("=" * 80)
        report_lines.append(f"Bucket: {self.bucket_name}")
        report_lines.append(f"Prefix: {self.prefix}")
        report_lines.append(f"分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")


        # 整體統計
        report_lines.append("整體統計:")
        report_lines.append(f"  檔案數量: {analysis_result['file_count']}")
        report_lines.append(f"  總資料行數: {analysis_result['total_rows']:,}")
        report_lines.append(f"  總檔案大小: {analysis_result['total_size_mb']:.2f} MB")
        report_lines.append("")


        # 時間範圍
        time_range = analysis_result['overall_time_range']
        start_time = time_range['start']
        end_time = time_range['end']


        report_lines.append("時間範圍:")
        report_lines.append(f"  開始時間: {format_datetime_with_timezone(start_time)}")
        report_lines.append(f"  結束時間: {format_datetime_with_timezone(end_time)}")
        report_lines.append(f"  總天數: {time_range['duration_days']} 天")
        report_lines.append("")


        # 時間空隙
        gaps = analysis_result['time_gaps']
        if gaps:
            report_lines.append(f"發現 {len(gaps)} 個時間空隙:")
            for i, gap in enumerate(gaps, 1):
                report_lines.append(f"  空隙 {i}:")
                report_lines.append(f"    開始: {format_datetime_with_timezone(gap.start_time)}")
                report_lines.append(f"    結束: {format_datetime_with_timezone(gap.end_time)}")
                report_lines.append(f"    持續時間: {gap.duration_days:.2f} 天 ({gap.duration_hours:.2f} 小時)")
                report_lines.append("")
        else:
            report_lines.append("✓ 未發現明顯的時間空隙")
            report_lines.append("")


        # 按日期分組的檔案
        files_by_date = analysis_result['files_by_date']
        if files_by_date:
            report_lines.append("按日期分組的檔案:")
            for date_str in sorted(files_by_date.keys()):
                file_count = len(files_by_date[date_str])
                report_lines.append(f"  {date_str}: {file_count} 個檔案")

        report_lines.append("")
        report_lines.append("=" * 80)

        return "\n".join(report_lines)


    def check_time_range(self, max_files: Optional[int] = None) -> str:
        """執行完整的時間範圍檢查"""
        logger.info("開始執行 parquet 檔案時間範圍檢查")

        try:
            # 列出 parquet 檔案
            parquet_files = self.list_parquet_files()

            if not parquet_files:
                return f"在 bucket '{self.bucket_name}' 的路徑 '{self.prefix}' 中未找到 parquet 檔案"

            # 限制檔案數量（用於測試）
            if max_files:
                parquet_files = parquet_files[:max_files]
                logger.info(f"限制分析檔案數量為 {max_files} 個")


            # 分析每個檔案
            file_infos = []
            for i, file_path in enumerate(parquet_files, 1):
                logger.info(f"正在處理檔案 {i}/{len(parquet_files)}: {file_path}")
                file_info = self.extract_timestamp_info(file_path)
                file_infos.append(file_info)

            # 分析時間覆蓋範圍
            analysis_result = self.analyze_time_coverage(file_infos)

            # 生成報告
            report = self.generate_report(analysis_result)

            logger.info("分析完成")
            return report


            # 分析時間覆蓋範圍
            analysis_result = self.analyze_time_coverage(file_infos)

            # 生成報告
            report = self.generate_report(analysis_result)

            logger.info("分析完成")
            return report

        except Exception as e:
            error_msg = f"執行時間範圍檢查時發生錯誤: {e}"
            logger.error(error_msg)
            return error_msg

def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description='檢查 GCS 中 parquet 檔案的時間範圍',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例:
    # 檢查特定 bucket 中的所有 parquet 檔案
    python parquet_time_range_checker.py --bucket-name my-data-bucket

    # 檢查特定路徑下的檔案
    python parquet_time_range_checker.py --bucket-name my-data-bucket --prefix data/2024/

    # 指定自定義時間戳欄位
    python parquet_time_range_checker.py --bucket-name my-data-bucket --timestamp-columns event_time created_at

    # 限制分析檔案數量（用於測試）
    python parquet_time_range_checker.py --bucket-name my-data-bucket --max-files 10
        """
    )

    parser.add_argument(
        '--bucket-name',
        required=True,
        help='GCS bucket 名稱'
    )

    parser.add_argument(
        '--prefix',
        default="",
        help='檔案路徑前綴 (預設: "")'
    )

    parser.add_argument(
        '--timestamp-columns',
        nargs='+',
        help='要檢查的時間戳欄位名稱 (預設: timestamp, created_at, updated_at, event_time, date, datetime, time)'
    )

    parser.add_argument(
        '--max-files',
        type=int,
        help='限制分析的檔案數量 (用於測試)'
    )

    parser.add_argument(
        '--output',
        help='輸出檔案路徑 (預設: 輸出到 stdout)'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日誌等級 (預設: INFO)'
    )

    args = parser.parse_args()

    # 設定日誌等級
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 建立檢查器
    checker = ParquetTimeRangeChecker(
        bucket_name=args.bucket_name,
        prefix=args.prefix,
        timestamp_columns=args.timestamp_columns
    )

    # 執行檢查
    report = checker.check_time_range(max_files=args.max_files)

    # 輸出結果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"報告已儲存至: {args.output}")
    else:
        print(report)

if __name__ == "__main__":
    main()
