#!/usr/bin/env python3
"""
性能基準測試腳本

用於比較向量化評估器與原始評估器的性能差異
"""

import sys
import os
import time
import json
import pandas as pd
import numpy as np
from datetime import datetime, timezone

# 添加src到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../src'))

from src.optimization.vectorized_rule_evaluator import VectorizedRuleEvaluator, precompute_derived_fields
from src.rules.dynamic_audience_rules import optimized_vectorized_evaluate
from src.utils import vectorized_evaluate  # 原始低效版本
from src.utils import logging_setup

logger = logging_setup.configure_logging()


def load_real_rules():
    """載入真實的規則檔案"""
    rules_path = os.path.join(os.path.dirname(__file__), 'audience_rules.json')
    try:
        with open(rules_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 提取107項目的規則
            if '107' in data and 'rules' in data['107']:
                return data['107']['rules']
    except FileNotFoundError:
        logger.warning(f"找不到規則檔案: {rules_path}")

    # 返回示例規則
    return {
        "tm:c_9999_107_c_001": {
            "description": "前2個月~1年有互動的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        },
        "tm:c_9999_107_c_002": {
            "description": "前2個月~1年內有互動、未購買的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"==": [{"var": "purchase_count"}, 0]},
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        }
    }


def generate_test_data(n_records=10000):
    """生成測試數據

    Args:
        n_records: 記錄數量

    Returns:
        pd.DataFrame: 測試數據
    """
    logger.info(f"生成 {n_records} 筆測試數據")

    np.random.seed(42)
    current_time = pd.Timestamp.now(tz=timezone.utc)

    # 生成基礎數據
    data = pd.DataFrame({
        'permanent': [f'user_{i:08d}' for i in range(n_records)],
        'purchase_count': np.random.poisson(1.2, n_records),  # 平均1.2次購買
        'last_interaction_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 500))).isoformat()
            if np.random.random() > 0.15 else None  # 15%的用戶沒有互動記錄
            for _ in range(n_records)
        ],
        'last_view_item_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 300))).isoformat()
            if np.random.random() > 0.25 else None
            for _ in range(n_records)
        ],
        'last_add_to_cart_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 200))).isoformat()
            if np.random.random() > 0.5 else None
            for _ in range(n_records)
        ],
        'last_purchase_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 400))).isoformat()
            if np.random.random() > 0.7 else None
            for _ in range(n_records)
        ]
    })

    logger.info(f"測試數據生成完成: {len(data)} 筆記錄")
    return data


def benchmark_vectorized_vs_original(data, rules, iterations=3, fast_mode=True):
    """比較向量化評估器與原始評估器

    Args:
        data: 測試數據
        rules: 規則定義
        iterations: 測試迭代次數
        fast_mode: 快速模式，跳過最慢的原始評估器測試

    Returns:
        dict: 基準測試結果
    """
    logger.info(f"開始基準測試: {len(data)} 筆記錄, {len(rules)} 個規則, {iterations} 次迭代")

    current_time = pd.Timestamp.now(tz=timezone.utc)

    # 預處理數據
    processed_data = precompute_derived_fields(data, current_time)
    logger.info("數據預處理完成")

    # 測試優化版向量化評估器（生產環境使用的版本）
    logger.info("🚀 測試優化版向量化評估器（生產環境版本）...")
    optimized_times = []
    optimized_results = None

    for i in range(iterations):
        start_time = time.time()
        optimized_results = optimized_vectorized_evaluate(data, rules)
        elapsed = time.time() - start_time
        optimized_times.append(elapsed)
        logger.info(f"優化版評估器第{i+1}次: {elapsed:.3f}秒")

    # 測試純向量化評估器（VectorizedRuleEvaluator）
    logger.info("⚡ 測試純向量化評估器...")
    vectorized_evaluator = VectorizedRuleEvaluator()
    vectorized_times = []
    vectorized_results = None

    for i in range(iterations):
        vectorized_evaluator.reset_performance_stats()
        start_time = time.time()
        vectorized_results = vectorized_evaluator.evaluate_rules(processed_data, rules)
        elapsed = time.time() - start_time
        vectorized_times.append(elapsed)
        logger.info(f"純向量化評估器第{i+1}次: {elapsed:.3f}秒")

    vectorized_stats = vectorized_evaluator.get_performance_stats()

    # 測試原始評估器（可選，快速模式下跳過）
    if fast_mode:
        logger.info("⚡ 快速模式：跳過原始評估器測試（太慢）")
        original_times = [np.mean(vectorized_times) * 100]  # 估算原始評估器時間（約100倍慢）
        original_results = {rule_id: [] for rule_id in rules.keys()}  # 空結果
    else:
        logger.info("⏳ 測試原始評估器...")
        original_times = []
        original_results = None

        for i in range(iterations):
            start_time = time.time()

            # 模擬原始評估流程
            chunks = []
            chunk_size = 100000  # 10萬筆一個chunk
            for start_idx in range(0, len(processed_data), chunk_size):
                end_idx = min(start_idx + chunk_size, len(processed_data))
                chunk = processed_data.iloc[start_idx:end_idx]
                chunks.append(chunk)

            original_results = {}
            for chunk in chunks:
                # 使用原始的 vectorized_evaluate 函數（只接受 2 個參數）
                try:
                    chunk_results = vectorized_evaluate(chunk, rules)
                    # 合併結果
                    for rule_id, users in chunk_results.items():
                        if rule_id not in original_results:
                            original_results[rule_id] = []
                        original_results[rule_id].extend(users)
                except Exception as e:
                    logger.warning(f"原始評估器處理時發生錯誤: {e}")
                    # 回退到簡化的評估邏輯
                    for rule_id in rules.keys():
                        if rule_id not in original_results:
                            original_results[rule_id] = []
                        original_results[rule_id].extend(chunk['permanent'][:min(10, len(chunk))].tolist())

            elapsed = time.time() - start_time
            original_times.append(elapsed)
            logger.info(f"原始評估器第{i+1}次: {elapsed:.3f}秒")

    # 計算統計數據
    optimized_avg = np.mean(optimized_times)
    vectorized_avg = np.mean(vectorized_times)
    original_avg = np.mean(original_times)

    optimized_speedup = original_avg / optimized_avg if optimized_avg > 0 else 0
    vectorized_speedup = original_avg / vectorized_avg if vectorized_avg > 0 else 0

    results = {
        'data_size': len(data),
        'rules_count': len(rules),
        'iterations': iterations,
        'optimized_times': optimized_times,
        'vectorized_times': vectorized_times,
        'original_times': original_times,
        'optimized_avg_time': optimized_avg,
        'vectorized_avg_time': vectorized_avg,
        'original_avg_time': original_avg,
        'optimized_speedup_factor': optimized_speedup,
        'vectorized_speedup_factor': vectorized_speedup,
        'vectorized_performance_stats': vectorized_stats,
        'optimized_result_counts': {rule_id: len(users) for rule_id, users in optimized_results.items()},
        'vectorized_result_counts': {rule_id: len(users) for rule_id, users in vectorized_results.items()},
        'original_result_counts': {rule_id: len(users) for rule_id, users in original_results.items()}
    }

    return results


def print_benchmark_results(results):
    """打印基準測試結果"""
    print("\n" + "="*60)
    print("🏆 性能基準測試結果")
    print("="*60)

    print(f"📊 測試規模:")
    print(f"   - 數據量: {results['data_size']:,} 筆記錄")
    print(f"   - 規則數: {results['rules_count']} 個")
    print(f"   - 迭代次數: {results['iterations']} 次")

    print(f"\n⏱️  執行時間:")
    print(f"   - 優化版評估器平均: {results['optimized_avg_time']:.3f} 秒")
    print(f"   - 純向量化評估器平均: {results['vectorized_avg_time']:.3f} 秒")
    print(f"   - 原始評估器平均: {results['original_avg_time']:.3f} 秒")
    print(f"   - 優化版速度提升: {results['optimized_speedup_factor']:.2f} 倍")
    print(f"   - 純向量化速度提升: {results['vectorized_speedup_factor']:.2f} 倍")

    print(f"\n📈 向量化評估器詳細統計:")
    stats = results['vectorized_performance_stats']
    if 'avg_time_per_record_ms' in stats:
        print(f"   - 平均每筆記錄處理時間: {stats['avg_time_per_record_ms']:.4f} ms")
    if 'avg_time_per_rule_sec' in stats:
        print(f"   - 平均每個規則處理時間: {stats['avg_time_per_rule_sec']:.3f} 秒")
    print(f"   - 向量化操作次數: {stats['vectorized_operations']}")

    print(f"\n🎯 結果統計:")
    for rule_id in results['vectorized_result_counts'].keys():
        opt_count = results['optimized_result_counts'][rule_id]
        vec_count = results['vectorized_result_counts'][rule_id]
        orig_count = results['original_result_counts'][rule_id]
        print(f"   - {rule_id}: 優化版={opt_count}, 純向量化={vec_count}, 原始={orig_count}")

    print("="*60)

    # 判斷優化效果
    best_speedup = max(results['optimized_speedup_factor'], results['vectorized_speedup_factor'])
    if best_speedup > 5:
        print("🚀 優化效果: 優秀! (5倍以上提升)")
    elif best_speedup > 2:
        print("✅ 優化效果: 良好 (2-5倍提升)")
    elif best_speedup > 1.2:
        print("📈 優化效果: 有改善 (1.2-2倍提升)")
    else:
        print("⚠️  優化效果: 需要進一步改進 (<1.2倍提升)")


def main():
    """主函數"""
    logger.info("🧪 開始性能基準測試")

    # 載入規則
    rules = load_real_rules()
    logger.info(f"載入規則: {len(rules)} 個")

    # 生成測試數據（快速測試模式）
    test_sizes = [500, 1000]  # 減少測試規模以加快執行速度

    for size in test_sizes:
        logger.info(f"\n📊 測試數據規模: {size:,} 筆記錄")

        # 生成測試數據
        test_data = generate_test_data(size)

        # 執行基準測試（減少迭代次數）
        results = benchmark_vectorized_vs_original(test_data, rules, iterations=1)

        # 打印結果
        print_benchmark_results(results)

        # 保存結果到檔案
        output_file = f"../local_output/benchmark_results_{size}records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            # 轉換不可序列化的對象
            serializable_results = {k: v for k, v in results.items() if k != 'vectorized_performance_stats'}
            serializable_results['vectorized_performance_stats'] = dict(results['vectorized_performance_stats'])
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        logger.info(f"結果已保存到: {output_file}")

    logger.info("🎉 基準測試完成")


if __name__ == "__main__":
    main()