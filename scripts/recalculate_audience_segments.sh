#!/bin/bash
# 重新執行特定 EC ID 的統計及受眾分群計算
# 此腳本用於修復受眾分群計算問題，重新處理 2025/05/22 的資料

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd $SCRIPT_DIR/..

# 要重新計算的 EC ID 列表
EC_IDS=(2980 3819 3820)
# 要處理的日期 (基準日的前一天)
BASE_DATE="2025-05-21"
BASE_DATE_COMPACT="20250521"
# 要產生的日期 (基準日)
TARGET_DATE="2025-05-22"
TARGET_DATE_COMPACT="20250522"

echo "準備重新計算 EC ID ${EC_IDS[@]} 的使用者統計資料"
echo "基準日期: $BASE_DATE, 目標日期: $TARGET_DATE"

# 執行重新計算前的檢查
echo "檢查新的 audience_rules.json 是否已上傳到 GCS..."
gsutil ls gs://tagtoo-ml-workflow/LTA/user_stats_configs/audience_rules.json
if [ $? -ne 0 ]; then
    echo "錯誤: audience_rules.json 在 GCS 中不存在！"
    exit 1
fi

# 檢查本地資料夾是否存在
mkdir -p ./local_data

# 為每個 EC ID 重新執行計算
for EC_ID in "${EC_IDS[@]}"; do
    echo "============================================="
    echo "開始處理 EC ID: $EC_ID"
    echo "============================================="

    echo "1. 獲取 $BASE_DATE 的基礎資料..."
    # 從 GCS 下載基準日的 parquet 檔案 (使用正確的路徑格式)
    gsutil cp gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_${EC_ID}/${BASE_DATE_COMPACT}.parquet ./local_data/${EC_ID}_${BASE_DATE_COMPACT}.parquet

    echo "2. 執行 update_fields_for_ec_id.py 計算 user_stats..."
    # 使用 update_fields 更新到目標日期
    # 只使用有效的欄位 (last_view_item_days 和 last_add_to_cart_days 將在 dynamic_audience_rules.py 中計算)
    python -m scripts.update_fields_for_ec_id \
        --ec-id=${EC_ID} \
        --fields=first_interaction_time,last_interaction_time,first_purchase_time,last_purchase_time,first_add_to_cart_time,last_add_to_cart_time,first_view_item_time,last_view_item_time \
        --start-date=${BASE_DATE} \
        --end-date=${TARGET_DATE} \
        --update-gcs \
        --use-bigquery-compute \
        --log-level=INFO

    echo "3. 驗證輸出..."
    # 檢查計算結果是否已經正確寫入 GCS (update_fields_for_ec_id.py 上傳到 user_stats_snapshots)
    GCS_PARQUET="gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_${EC_ID}/${TARGET_DATE_COMPACT}.parquet"

    if gsutil -q stat "$GCS_PARQUET"; then
        echo "找到新產生的 GCS 檔案: $GCS_PARQUET"
        LOCAL_PARQUET="./local_data/${EC_ID}_${TARGET_DATE_COMPACT}_new.parquet"
        gsutil cp "$GCS_PARQUET" "$LOCAL_PARQUET"
        echo "已下載檢查用 parquet 檔案: $LOCAL_PARQUET"
    else
        echo "警告: GCS 上找不到 $GCS_PARQUET，跳過本地驗證。"
        continue
    fi

    echo "4. 驗證 parquet 必要欄位與動態 xxx_days 欄位..."
    if [ ! -f "$LOCAL_PARQUET" ]; then
        echo "錯誤: 本地 parquet 檔案 $LOCAL_PARQUET 不存在，跳過驗證。"
        continue
    fi
    python3 -c "
import pandas as pd
import logging
from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(message)s')

# 必須存在於 parquet 的欄位
required_columns = [
    'first_interaction_time', 'last_interaction_time',
    'first_purchase_time', 'last_purchase_time',
    'first_add_to_cart_time', 'last_add_to_cart_time',
    'first_view_item_time', 'last_view_item_time'
]
# 動態產生的欄位
optional_dynamic_columns = [
    'last_view_item_days', 'last_add_to_cart_days', 'last_purchase_days', 'last_interaction_days'
]

try:
    df = pd.read_parquet('$LOCAL_PARQUET')
    missing = [col for col in required_columns if col not in df.columns]
    if missing:
        logging.error(f'缺少關鍵欄位: {missing}')
        raise Exception(f'Parquet 檔案缺少必要欄位: {missing}')
    else:
        logging.info('所有必要欄位皆存在於 parquet 檔案中')

    # 動態產生欄位驗證
    # calculate_audience_segments_dynamic 返回一個字典，而不是 DataFrame
    result_dict = calculate_audience_segments_dynamic(df, $EC_ID)

    # 檢查動態產生的欄位是否存在於原始 DataFrame 中
    # 這些欄位是在 calculate_audience_segments_dynamic 函數內部計算並添加到 df 中的
    missing_dynamic = [col for col in optional_dynamic_columns if col not in df.columns]
    if missing_dynamic:
        logging.debug(f'動態產生欄位不存在（預期行為）: {missing_dynamic}')

    # 查看這些欄位的統計信息
    for col in optional_dynamic_columns:
        if col in df.columns:
            logging.debug(f'{col} describe:')
            logging.debug(df[col].describe())
            logging.debug(f'{col} null count: {df[col].isnull().sum()}')

    # 添加對受眾分群結果的驗證
    if result_dict:
        logging.info(f'受眾分群結果: 共找到 {len(result_dict)} 個規則')

        # 統計每個規則匹配的用戶數量
        for rule_id, users in result_dict.items():
            logging.info(f'規則 {rule_id}: 匹配 {len(users)} 個用戶')

        # 檢查是否存在 c_003 規則
        expected_rules = [f'tm:c_9999_{$EC_ID}_c_00{i}' for i in range(1, 6)]
        missing_rules = [rule for rule in expected_rules if rule not in result_dict]
        if missing_rules:
            logging.warning(f'警告: 缺少預期的規則: {missing_rules}')

        # 如果所有規則匹配的用戶數量都相同且接近總用戶數量，可能存在問題
        user_counts = [len(users) for users in result_dict.values()]
        if all(count == user_counts[0] for count in user_counts) and user_counts[0] == len(df):
            logging.warning(f'警告: 所有規則都匹配了相同數量的用戶 ({user_counts[0]})，接近總用戶數量 ({len(df)})，這可能表示規則邏輯有問題')
    else:
        logging.warning('警告: 未找到任何符合條件的受眾分群')
except Exception as e:
    logging.error(f'驗證失敗: {e}')
    # 不 exit，繼續處理下一個 EC ID
"

    echo "EC ID $EC_ID 的計算已完成"
    echo ""
done

echo "所有 EC ID 的重新計算已完成！"
echo "請驗證新的分群結果是否正確。"
echo "重新計算的 parquet 檔案已上傳到 GCS: gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_<ec_id>/${TARGET_DATE_COMPACT}.parquet"
