#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查 parquet 檔案的時間範圍
分析 EC ID 2980, 3819, 3820 的資料時間分佈
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加項目根目錄到 Python 路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from src.utils import logging_setup
from google.cloud import storage

logger = logging_setup.configure_logging()

def check_parquet_time_range(ec_id):
    """檢查特定 EC ID 的 parquet 檔案時間範圍"""
    try:
        # 初始化 GCS client
        storage_client = storage.Client()
        bucket_name = 'tagtoo-ml-workflow'
        bucket = storage_client.bucket(bucket_name)

        # 構建 parquet 檔案路徑
        parquet_path = f'LTA/user_stats_parquet/ec_{ec_id}_user_stats.parquet'
        logger.info(f"檢查檔案: gs://{bucket_name}/{parquet_path}")

        blob = bucket.blob(parquet_path)


        # 構建 parquet 檔案路徑
        parquet_path = f'LTA/user_stats_parquet/ec_{ec_id}_user_stats.parquet'
        logger.info(f"檢查檔案: gs://{bucket_name}/{parquet_path}")

        blob = bucket.blob(parquet_path)

        # 檢查檔案是否存在
        if not blob.exists():
            logger.error(f"檔案不存在: {parquet_path}")
            return None


        # 下載檔案到臨時位置
        temp_file = f'/tmp/ec_{ec_id}_user_stats.parquet'
        blob.download_to_filename(temp_file)
        logger.info(f"檔案下載完成: {temp_file}")


        # 讀取 parquet 檔案
        df = pd.read_parquet(temp_file)
        logger.info(f"EC ID {ec_id} 資料筆數: {len(df)}")
        logger.info(f"EC ID {ec_id} 欄位: {df.columns.tolist()}")

        # 分析時間相關欄位
        time_columns = [
            'last_interaction_days', 'last_view_item_days',
            'last_add_to_cart_days', 'last_purchase_days',
            'first_interaction_days', 'first_view_item_days',
            'first_add_to_cart_days', 'first_purchase_days'
        ]

        time_analysis = {}

        for col in time_columns:
            if col in df.columns:
                # 過濾 -1 值（表示沒有該事件）
                valid_data = df[col][df[col] != -1]

                if len(valid_data) > 0:
                    analysis = {
                        'min_days': int(valid_data.min()),
                        'max_days': int(valid_data.max()),
                        'mean_days': round(valid_data.mean(), 2),
                        'median_days': round(valid_data.median(), 2),
                        'valid_count': len(valid_data),
                        'total_count': len(df),
                        'coverage_pct': round(len(valid_data) / len(df) * 100, 2)
                    }

                    # 計算分佈
                    distribution = {}
                    ranges = [(0, 30), (31, 60), (61, 90), (91, 180), (181, 365), (366, float('inf'))]
                    for start, end in ranges:
                        if end == float('inf'):
                            count = ((valid_data >= start)).sum()
                            range_name = f"{start}+ 天"
                        else:
                            count = ((valid_data >= start) & (valid_data <= end)).sum()
                            range_name = f"{start}-{end} 天"


                        if count > 0:
                            distribution[range_name] = {
                                'count': int(count),
                                'percentage': round(count / len(valid_data) * 100, 2)
                            }


                    analysis['distribution'] = distribution
                    time_analysis[col] = analysis
                else:
                    time_analysis[col] = {
                        'status': 'no_valid_data',
                        'total_count': len(df)
                    }
            else:
                time_analysis[col] = {'status': 'column_not_found'}


        # 檢查購買相關統計
        purchase_analysis = {}
        if 'purchase_count' in df.columns:
            purchase_stats = {
                'total_users': len(df),
                'users_with_purchases': (df['purchase_count'] > 0).sum(),
                'users_without_purchases': (df['purchase_count'] == 0).sum(),
                'max_purchases': int(df['purchase_count'].max()),
                'mean_purchases': round(df['purchase_count'].mean(), 2)
            }
            purchase_analysis['purchase_count'] = purchase_stats


        # 檢查互動相關統計
        interaction_analysis = {}
        if 'last_interaction_time' in df.columns:
            has_interaction = df['last_interaction_time'].notna().sum()
            interaction_analysis['interaction_coverage'] = {
                'users_with_interaction': int(has_interaction),
                'users_without_interaction': int(len(df) - has_interaction),
                'coverage_pct': round(has_interaction / len(df) * 100, 2)
            }


        result = {
            'ec_id': ec_id,
            'file_path': parquet_path,
            'total_records': len(df),
            'columns': df.columns.tolist(),
            'time_analysis': time_analysis,
            'purchase_analysis': purchase_analysis,
            'interaction_analysis': interaction_analysis
        }

        # 清理臨時檔案
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return result


        # 清理臨時檔案
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return result

    except Exception as e:
        logger.error(f"檢查 EC ID {ec_id} 時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return None

def print_analysis_report(analysis):
    """印出分析報告"""
    if not analysis:
        print("無法產生分析報告")
        return


    print(f"\n{'='*60}")
    print(f"EC ID {analysis['ec_id']} 資料分析報告")
    print(f"{'='*60}")
    print(f"檔案路徑: {analysis['file_path']}")
    print(f"總記錄數: {analysis['total_records']:,}")
    print(f"欄位數量: {len(analysis['columns'])}")


    # 時間分析報告
    print(f"\n時間欄位分析:")
    print("-" * 40)
    for col, data in analysis['time_analysis'].items():
        if isinstance(data, dict) and 'status' in data:
            if data['status'] == 'no_valid_data':
                print(f"{col}: 無有效資料 (總筆數: {data['total_count']})")
            elif data['status'] == 'column_not_found':
                print(f"{col}: 欄位不存在")
        else:
            print(f"\n{col}:")
            print(f"  範圍: {data['min_days']} - {data['max_days']} 天")
            print(f"  平均: {data['mean_days']} 天")
            print(f"  中位數: {data['median_days']} 天")
            print(f"  覆蓋率: {data['valid_count']:,}/{data['total_count']:,} ({data['coverage_pct']}%)")


            if 'distribution' in data and data['distribution']:
                print(f"  分佈:")
                for range_name, dist_data in data['distribution'].items():
                    print(f"    {range_name}: {dist_data['count']:,} 筆 ({dist_data['percentage']}%)")


    # 購買分析報告
    if analysis['purchase_analysis']:
        print(f"\n購買行為分析:")
        print("-" * 40)
        for metric, data in analysis['purchase_analysis'].items():
            if metric == 'purchase_count':
                print(f"總用戶數: {data['total_users']:,}")
                print(f"有購買的用戶: {data['users_with_purchases']:,}")
                print(f"無購買的用戶: {data['users_without_purchases']:,}")
                print(f"最高購買次數: {data['max_purchases']}")
                print(f"平均購買次數: {data['mean_purchases']}")


    # 互動分析報告
    if analysis['interaction_analysis']:
        print(f"\n互動行為分析:")
        print("-" * 40)
        for metric, data in analysis['interaction_analysis'].items():
            if metric == 'interaction_coverage':
                print(f"有互動的用戶: {data['users_with_interaction']:,}")
                print(f"無互動的用戶: {data['users_without_interaction']:,}")
                print(f"互動覆蓋率: {data['coverage_pct']}%")

def check_rule_compatibility(analysis, ec_id):
    """檢查資料與規則的相容性"""
    print(f"\n規則相容性檢查 (EC ID {ec_id}):")
    print("-" * 40)


    # 根據不同 EC ID 檢查不同的規則
    if ec_id == 2980:
        rules_to_check = [
            {'name': '規則 003 (Viewcontent_舊訪客)', 'requires': 'last_interaction_days > 60'},
            {'name': '規則 006 (PC_舊客)', 'requires': 'last_purchase_days > 60'}
        ]
    elif ec_id == 3819:
        rules_to_check = [
            {'name': '規則 001 (認知受眾)', 'requires': 'last_interaction_days <= 90'},
            {'name': '規則 002 (興趣/意象受眾)', 'requires': 'last_add_to_cart_days <= 365'}
        ]
    elif ec_id == 3820:
        rules_to_check = [
            {'name': '規則 001 (認知受眾)', 'requires': 'last_interaction_days <= 90'},
            {'name': '規則 002 (興趣/意象受眾)', 'requires': 'last_add_to_cart_days <= 365'}
        ]
    else:
        print(f"未定義 EC ID {ec_id} 的規則檢查")
        return

    for rule in rules_to_check:
        print(f"\n{rule['name']}:")
        print(f"  需求: {rule['requires']}")


    for rule in rules_to_check:
        print(f"\n{rule['name']}:")
        print(f"  需求: {rule['requires']}")

        # 根據需求檢查資料
        if 'last_interaction_days > 60' in rule['requires']:
            col = 'last_interaction_days'
            if col in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][col]:
                max_days = analysis['time_analysis'][col]['max_days']
                if max_days > 60:
                    print(f"  ✅ 資料滿足需求 (最大: {max_days} 天)")
                else:
                    print(f"  ❌ 資料不足 (最大: {max_days} 天，需要 > 60 天)")
            else:
                print(f"  ❌ 欄位 {col} 無有效資料")


        elif 'last_purchase_days > 60' in rule['requires']:
            col = 'last_purchase_days'
            if col in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][col]:
                max_days = analysis['time_analysis'][col]['max_days']
                if max_days > 60:
                    print(f"  ✅ 資料滿足需求 (最大: {max_days} 天)")
                else:
                    print(f"  ❌ 資料不足 (最大: {max_days} 天，需要 > 60 天)")
            else:
                print(f"  ❌ 欄位 {col} 無有效資料")


        elif 'last_interaction_days <= 90' in rule['requires']:
            col = 'last_interaction_days'
            if col in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][col]:
                max_days = analysis['time_analysis'][col]['max_days']
                print(f"  ✅ 資料範圍 (0-{max_days} 天)")
            else:
                print(f"  ❌ 欄位 {col} 無有效資料")


        elif 'last_add_to_cart_days <= 365' in rule['requires']:
            col = 'last_add_to_cart_days'
            if col in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][col]:
                max_days = analysis['time_analysis'][col]['max_days']
                print(f"  ✅ 資料範圍 (0-{max_days} 天)")
            else:
                print(f"  ❌ 欄位 {col} 無有效資料")

def main():
    """主函數"""
    target_ec_ids = [2980, 3819, 3820]

    print("正在檢查 parquet 檔案的時間範圍...")
    print("目標 EC ID:", target_ec_ids)

    all_analysis = {}

    for ec_id in target_ec_ids:
        print(f"\n正在分析 EC ID {ec_id}...")
        analysis = check_parquet_time_range(ec_id)


    print("正在檢查 parquet 檔案的時間範圍...")
    print("目標 EC ID:", target_ec_ids)

    all_analysis = {}

    for ec_id in target_ec_ids:
        print(f"\n正在分析 EC ID {ec_id}...")
        analysis = check_parquet_time_range(ec_id)

        if analysis:
            all_analysis[ec_id] = analysis
            print_analysis_report(analysis)
            check_rule_compatibility(analysis, ec_id)
        else:
            print(f"❌ EC ID {ec_id} 分析失敗")


    # 生成總結報告
    print(f"\n{'='*60}")
    print("總結報告")
    print(f"{'='*60}")

    for ec_id, analysis in all_analysis.items():
        print(f"\nEC ID {ec_id}:")

        # 檢查關鍵時間欄位的範圍
        key_fields = ['last_interaction_days', 'last_purchase_days', 'last_add_to_cart_days']
        max_days_found = 0


    for ec_id, analysis in all_analysis.items():
        print(f"\nEC ID {ec_id}:")

        # 檢查關鍵時間欄位的範圍
        key_fields = ['last_interaction_days', 'last_purchase_days', 'last_add_to_cart_days']
        max_days_found = 0

        for field in key_fields:
            if field in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][field]:
                max_days = analysis['time_analysis'][field]['max_days']
                max_days_found = max(max_days_found, max_days)

        print(f"  最大時間範圍: {max_days_found} 天")


        print(f"  最大時間範圍: {max_days_found} 天")

        if max_days_found < 60:
            print(f"  ⚠️  資料時間範圍不足，可能需要重新初始化")
        elif max_days_found < 365:
            print(f"  ⚠️  資料時間範圍部分不足，某些長期規則可能無效")
        else:
            print(f"  ✅ 資料時間範圍充足")


    # 建議
    print(f"\n建議:")
    insufficient_ecs = []
    for ec_id, analysis in all_analysis.items():
        key_fields = ['last_interaction_days', 'last_purchase_days']
        max_days_found = 0


        for field in key_fields:
            if field in analysis['time_analysis'] and 'max_days' in analysis['time_analysis'][field]:
                max_days = analysis['time_analysis'][field]['max_days']
                max_days_found = max(max_days_found, max_days)

        if max_days_found < 60:
            insufficient_ecs.append(ec_id)


        if max_days_found < 60:
            insufficient_ecs.append(ec_id)

    if insufficient_ecs:
        print(f"- 需要重新初始化的 EC ID: {insufficient_ecs}")
        print(f"- 建議重新產生至少 90-365 天的歷史資料")
    else:
        print(f"- 所有 EC ID 的資料範圍都足夠")

if __name__ == "__main__":
    main()
