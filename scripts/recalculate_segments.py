#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重新計算特定 EC ID 的受眾分群結果

此腳本用於修復 EC ID 2980, 3819, 3820 的分群結果，讀取最新的 audience_rules.json，
重新計算 last_view_item_days 和 last_add_to_cart_days 欄位，並更新受眾分群。

用法:
    python -m scripts.recalculate_segments --ec-id 2980 --base-date 20250521 --target-date 20250522
"""

import argparse
import logging
import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.cloud_integration import StorageManager
from src.core.data_processing import standardize_datetime_columns
from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic
from src.utils.logging_setup import configure_logging
from src.utils.time_utils import get_taiwan_date_str

logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='重新計算特定 EC ID 的受眾分群')
    parser.add_argument('--ec-id', type=int, required=True, help='電商 ID')
    parser.add_argument('--base-date', type=str, required=True, help='基準日期 (YYYYMMDD)')
    parser.add_argument('--target-date', type=str, required=True, help='目標日期 (YYYYMMDD)')
    parser.add_argument('--dry-run', action='store_true', help='僅測試不上傳')
    parser.add_argument('--log-level', type=str, default='INFO', help='日誌級別')
    return parser.parse_args()

def main():
    """主函數"""
    args = parse_args()

    # 設置日誌級別
    configure_logging(level=args.log_level)

    logger.info(f"開始重新計算 EC ID {args.ec_id} 的受眾分群")
    logger.info(f"基準日期: {args.base_date}, 目標日期: {args.target_date}")

    # 初始化 Storage Manager
    storage_manager = StorageManager()

    try:
        # 步驟 1: 從 GCS 下載基準日的 parquet 檔案
        base_parquet_path = f"LTA/user_stats_snapshots/ec_{args.ec_id}/{args.base_date}.parquet"
        gcs_base_path = f"gs://tagtoo-ml-workflow/{base_parquet_path}"
        logger.info(f"從 GCS 下載基準日 parquet: {gcs_base_path}")

        local_base_path = f"./local_data/{args.ec_id}_{args.base_date}.parquet"
        os.makedirs(os.path.dirname(local_base_path), exist_ok=True)

        # 使用 gsutil 下載檔案而非 storage_manager.download_file
        import subprocess
        result = subprocess.run(['gsutil', 'cp', gcs_base_path, local_base_path], capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"下載失敗: {result.stderr}")
            raise Exception(f"無法從 GCS 下載檔案: {gcs_base_path}")

        logger.info(f"成功下載基準日 parquet 到: {local_base_path}")
        # 步驟 2: 讀取基準數據並計算新欄位
        logger.info("讀取基準 parquet 檔案...")
        df = pd.read_parquet(local_base_path)
        logger.info(f"基準數據行數: {len(df)}")

        # 步驟 3: 獲取並處理所有必要的事件數據
        # 這裡應該有一個更詳細的過程來獲取最新的事件數據，但為簡化腳本，我們假設這已經由 update_fields_for_ec_id.py 處理

        # 步驟 4: 標準化時間欄位
        logger.info("標準化時間欄位...")
        standard_df = standardize_datetime_columns(df)

        # 步驟 5: 設置當前時間以計算天數欄位
        current_time = datetime.now()
        taiwan_date = get_taiwan_date_str(current_time)
        logger.info(f"使用當前時間: {current_time}, 台灣日期: {taiwan_date}")

        # 步驟 6: 重新計算受眾分群
        logger.info(f"重新計算 EC ID {args.ec_id} 的受眾分群...")
        audience_segments = calculate_audience_segments_dynamic(standard_df, args.ec_id)
        logger.info(f"受眾分群計算完成, 結果規則數: {len(audience_segments)}")

        # 記錄分群結果詳情
        for rule_id, users in audience_segments.items():
            logger.info(f"規則 {rule_id}: 匹配 {len(users)} 個用戶")

        # 步驟 7: 添加分群標記並保存結果
        # 保存原始數據，不是分群結果（分群結果是字典，not DataFrame）
        target_parquet_path = f"./local_data/{args.ec_id}_{args.target_date}.parquet"
        logger.info(f"保存處理後數據到本地: {target_parquet_path}")
        standard_df.to_parquet(target_parquet_path, index=False)

        # 步驟 8: 上傳到 GCS
        if not args.dry_run:
            gcs_target_path = f"LTA/user_stats_snapshots/ec_{args.ec_id}/{args.target_date}.parquet"
            gcs_full_path = f"gs://tagtoo-ml-workflow/{gcs_target_path}"
            logger.info(f"上傳結果到 GCS: {gcs_full_path}")

            # 使用 gsutil 上傳檔案而非 storage_manager.upload_file
            import subprocess
            result = subprocess.run(['gsutil', 'cp', target_parquet_path, gcs_full_path], capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"上傳失敗: {result.stderr}")
                raise Exception(f"無法上傳檔案到 GCS: {gcs_full_path}")

            logger.info("上傳完成")
        else:
            logger.info("乾運行模式，跳過上傳到 GCS")

        logger.info(f"EC ID {args.ec_id} 的受眾分群已成功重新計算")
        return True

    except Exception as e:
        logger.exception(f"處理 EC ID {args.ec_id} 時發生錯誤: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
