import pandas as pd
import sys
from datetime import datetime

def check_parquet_file(file_path='./local_data/test_fixed.parquet'):
    print(f"檢查 Parquet 檔案: {file_path}")
    try:
        # 讀取 Parquet 檔案
        df = pd.read_parquet(file_path)

        # 基本信息
        print(f"\n基本信息:")
        print(f"總行數: {len(df)}")
        print(f"列數: {len(df.columns)}")
        print(f"列名: {', '.join(df.columns)}")

        # 檢查時間欄位
        print(f"\n時間欄位統計:")
        time_cols = [col for col in df.columns if 'time' in col]
        for col in time_cols:
            valid_count = df[col].notna().sum()
            valid_percent = valid_count/len(df)*100 if len(df) > 0 else 0
            print(f"{col}: {valid_count} 筆有效數據 ({valid_percent:.2f}%)")

            # 如果有有效數據，顯示範圍
            if valid_count > 0:
                print(f"  - 最早時間: {df[col].min()}")
                print(f"  - 最晚時間: {df[col].max()}")
                # 顯示前5筆有效數據的樣本
                sample = df[df[col].notna()][col].head(5).tolist()
                print(f"  - 樣本值: {sample}")

        # 檢查購買相關欄位
        print(f"\n購買相關欄位:")
        if 'purchase_count' in df.columns:
            purchaser_count = (df['purchase_count'] > 0).sum()
            purchase_rate = purchaser_count/len(df)*100 if len(df) > 0 else 0
            print(f"購買用戶數: {purchaser_count} ({purchase_rate:.2f}%)")
            print(f"購買記錄分布: \n{df['purchase_count'].value_counts().sort_index()}")

            if 'total_purchase_amount' in df.columns:
                total_amount = df['total_purchase_amount'].sum()
                print(f"總購買金額: {total_amount:.2f}")
                if purchaser_count > 0:
                    avg_amount = total_amount / purchaser_count
                    print(f"平均每購買用戶金額: {avg_amount:.2f}")

        # 檢查互動次數欄位
        print(f"\n互動次數欄位:")
        if 'total_interaction_count' in df.columns:
            print(f"互動總數: {df['total_interaction_count'].sum()}")
            print(f"平均每用戶互動數: {df['total_interaction_count'].mean():.2f}")
            print(f"互動數分布: \n{df['total_interaction_count'].value_counts().sort_index().head(10)}")

        # 檢查會話數欄位
        print(f"\n會話數欄位:")
        if 'total_sessions' in df.columns:
            print(f"會話總數: {df['total_sessions'].sum()}")
            print(f"平均每用戶會話數: {df['total_sessions'].mean():.2f}")
            print(f"會話數分布: \n{df['total_sessions'].value_counts().sort_index().head(10)}")

        # 檢查原始數據
        print(f"\n前5筆記錄的樣本:")
        print(df.head(5))

        # 檢查 event.name 欄位
        if 'event.name' in df.columns:
            print(f"\nevent.name 欄位分布:")
            print(df['event.name'].value_counts())

        # 檢查關鍵統計指標之間的關係
        print(f"\n關鍵統計指標之間的關係:")
        if 'total_interaction_count' in df.columns and 'total_sessions' in df.columns:
            print(f"互動次數與會話數的比例: {df['total_interaction_count'].sum()/df['total_sessions'].sum() if df['total_sessions'].sum() > 0 else 0:.2f}")

        return True
    except Exception as e:
        print(f"檢查 Parquet 檔案時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 如果提供了文件路徑參數，則使用參數指定的文件
    file_path = sys.argv[1] if len(sys.argv) > 1 else './local_data/test_fixed.parquet'
    check_parquet_file(file_path)