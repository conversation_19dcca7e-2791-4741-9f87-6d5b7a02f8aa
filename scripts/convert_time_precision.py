#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
時間精度轉換腳本

此腳本用於將 parquet 檔案中的時間欄位從微秒精度 (datetime64[us, UTC]) 轉換為奈秒精度 (datetime64[ns, UTC])，
以解決不同 pandas 版本產生的時間精度不一致問題。

使用方式:
    python scripts/convert_time_precision.py --input-file <input_parquet_file> --output-file <output_parquet_file>

參數說明:
    --input-file: 輸入的 parquet 檔案路徑
    --output-file: 輸出的 parquet 檔案路徑，若不提供則覆蓋輸入檔案
    --columns: 要轉換的時間欄位，逗號分隔，預設為 "first_interaction_time,last_interaction_time,first_purchase_time,last_purchase_time,registration_time"
    --backup: 是否在覆蓋前備份原檔案，預設為 True
    --verbose: 顯示詳細資訊
"""

import os
import sys
import argparse
import logging
import pandas as pd
from pathlib import Path
import shutil
import time

# 設定 logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(description='將 parquet 檔案中的時間欄位從微秒精度轉換為奈秒精度')
    parser.add_argument('--input-file', type=str, required=True, help='輸入的 parquet 檔案路徑')
    parser.add_argument('--output-file', type=str, help='輸出的 parquet 檔案路徑，若不提供則覆蓋輸入檔案')
    parser.add_argument('--columns', type=str, default="first_interaction_time,last_interaction_time,first_purchase_time,last_purchase_time,registration_time",
                        help='要轉換的時間欄位，逗號分隔')
    parser.add_argument('--backup', action='store_true', default=True, help='是否在覆蓋前備份原檔案')
    parser.add_argument('--verbose', action='store_true', help='顯示詳細資訊')
    return parser.parse_args()

def convert_time_precision(df, columns):
    """將時間欄位從微秒精度轉換為奈秒精度

    Args:
        df: 輸入 DataFrame
        columns: 要轉換的欄位名稱列表

    Returns:
        pd.DataFrame: 轉換後的 DataFrame
    """
    # 複製 DataFrame 以避免修改原始資料
    df = df.copy()

    for col in columns:
        if col in df.columns:
            # 檢查欄位型別
            dtype_str = str(df[col].dtype)

            # 如果欄位是 datetime64[us] 或 datetime64[us, UTC]，則轉換為奈秒精度
            if dtype_str.startswith('datetime64[us'):
                logger.info(f"轉換欄位 {col}，當前精度: {dtype_str}")
                # 轉換為字串後再轉回日期時間，以確保精度變更
                df[col] = pd.to_datetime(df[col].astype(str))
                logger.info(f"轉換後精度: {df[col].dtype}")
            else:
                logger.info(f"欄位 {col} 不需要轉換，當前精度: {dtype_str}")

    return df

def main():
    """主函數"""
    # 解析命令列參數
    args = parse_args()

    # 設定日誌級別
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    input_file = args.input_file
    output_file = args.output_file or input_file
    column_list = args.columns.split(',')

    # 檢查輸入檔案是否存在
    if not os.path.exists(input_file):
        logger.error(f"輸入檔案不存在: {input_file}")
        sys.exit(1)

    # 讀取 parquet 檔案
    start_time = time.time()
    logger.info(f"開始讀取 parquet 檔案: {input_file}")
    df = pd.read_parquet(input_file)
    logger.info(f"成功讀取 parquet 檔案，資料筆數: {len(df)}")
    logger.info(f"讀取檔案耗時: {time.time() - start_time:.2f} 秒")

    # 檢查並顯示時間欄位資訊
    time_columns = [col for col in column_list if col in df.columns]
    logger.info(f"找到 {len(time_columns)} 個時間欄位: {', '.join(time_columns)}")
    for col in time_columns:
        logger.info(f"欄位 {col} 的型別: {df[col].dtype}")

    # 轉換時間精度
    start_time = time.time()
    logger.info("開始轉換時間精度...")
    df_converted = convert_time_precision(df, time_columns)
    logger.info(f"轉換時間精度完成，耗時: {time.time() - start_time:.2f} 秒")

    # 備份原檔案（如果需要）
    if args.backup and output_file == input_file:
        backup_file = f"{input_file}.bak"
        logger.info(f"備份原檔案到: {backup_file}")
        shutil.copy2(input_file, backup_file)

    # 儲存轉換後的檔案
    start_time = time.time()
    logger.info(f"開始儲存轉換後的檔案到: {output_file}")
    df_converted.to_parquet(output_file, index=False)
    logger.info(f"儲存檔案完成，耗時: {time.time() - start_time:.2f} 秒")

    logger.info("轉換完成！")

if __name__ == "__main__":
    main()