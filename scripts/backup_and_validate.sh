#!/bin/bash
# 下載並備份現有的 parquet 檔案，用於比較和驗證
# 此腳本會從 GCS 下載當前的 ${TARGET_DATE}.parquet 檔案，進行備份並檢查其內容

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd $SCRIPT_DIR/..

# 要備份的 EC ID 列表
EC_IDS=(2980 3819 3820)
TARGET_DATE="20250525"

# 確保備份目錄存在
BACKUP_DIR="./local_data/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

echo "==========================================================="
echo "開始備份並驗證現有的 $TARGET_DATE.parquet 檔案"
echo "備份目錄: $BACKUP_DIR"
echo "==========================================================="

# 為每個 EC ID 下載並驗證文件
for EC_ID in "${EC_IDS[@]}"; do
    echo "============================================="
    echo "處理 EC ID: $EC_ID"
    echo "============================================="

    # 目標文件在 GCS 的路徑
    GCS_PATH="gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_${EC_ID}/${TARGET_DATE}.parquet"
    BACKUP_FILE="${BACKUP_DIR}/${EC_ID}_${TARGET_DATE}_原始檔.parquet"

    echo "1. 檢查 GCS 上是否存在 $TARGET_DATE 的 parquet 檔案..."
    if gsutil -q stat $GCS_PATH; then
        echo "找到檔案: $GCS_PATH"

        # 下載並備份文件
        echo "2. 下載並備份檔案到: $BACKUP_FILE"
        gsutil cp $GCS_PATH $BACKUP_FILE

        # 使用 pandas 檢查文件內容
        echo "3. 分析 parquet 檔案內容..."
        python -c "
import pandas as pd
import sys

try:
    # 讀取 parquet 檔案
    df = pd.read_parquet('$BACKUP_FILE')

    # 輸出基本統計資訊
    print(f'檔案行數: {len(df)}')
    print(f'檔案欄位: {df.columns.tolist()}')

    # 檢查是否有關鍵欄位 (只檢查 _time 欄位，_days 欄位是動態計算的，不應存在於原始檔)
    key_columns = ['first_interaction_time', 'last_interaction_time',
                   'first_purchase_time', 'last_purchase_time',
                   'first_add_to_cart_time', 'last_add_to_cart_time',
                   'first_view_item_time', 'last_view_item_time']
    missing_columns = [col for col in key_columns if col not in df.columns]

    if missing_columns:
        print(f'警告: 缺少關鍵欄位: {missing_columns}')
    else:
        print('所有關鍵欄位均存在')

        # 檢查可能的錯誤值
        nulls = df[key_columns].isnull().sum().to_dict()
        print(f'欄位空值統計: {nulls}')

        # 檢查天數欄位的範圍
        for col in ['last_view_item_days', 'last_add_to_cart_days']:
            if col in df.columns:
                non_null = df[col].dropna()
                if len(non_null) > 0:
                    print(f'{col} 範圍: {non_null.min()} 到 {non_null.max()}')
                    print(f'{col} 前 5 個值: {non_null.head(5).tolist()}')

except Exception as e:
    print(f'分析檔案時發生錯誤: {str(e)}')
    sys.exit(1)
"
        # 4. 驗證分群規則
        echo "4. 驗證分群規則數量..."
        python -c "
import sys
sys.path.insert(0, '.')

try:
    import pandas as pd
    from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

    # 讀取 parquet 檔案
    df = pd.read_parquet('$BACKUP_FILE')

    # 執行分群計算
    print('執行分群計算...')
    segmentation_results = calculate_audience_segments_dynamic(df, $EC_ID)

    if not segmentation_results:
        print('⚠️  沒有分群結果')
    else:
        print(f'✓ 分群規則數量: {len(segmentation_results)}')

        total_users_in_segments = 0
        print('各規則用戶數量:')
        for rule_id, users in segmentation_results.items():
            user_count = len(users)
            total_users_in_segments += user_count
            # 簡化規則 ID 顯示
            simple_rule_id = rule_id.split('_')[-1] if '_' in rule_id else rule_id
            print(f'  {simple_rule_id}: {user_count} 個用戶')

        print(f'總用戶數: {len(df)}')
        print(f'分群用戶總數: {total_users_in_segments}')
        if len(df) > 0:
            coverage = total_users_in_segments/len(df)*100
            print(f'分群覆蓋率: {coverage:.1f}%')

except ImportError as e:
    print(f'⚠️  無法導入分群模組: {e}')
except Exception as e:
    print(f'❌ 分群驗證失敗: {e}')
"
    else
        echo "檔案不存在: $GCS_PATH"
    fi

    echo ""
done

echo "==========================================================="
echo "備份及驗證完成"
echo "所有檔案已備份到: $BACKUP_DIR"
echo "==========================================================="
