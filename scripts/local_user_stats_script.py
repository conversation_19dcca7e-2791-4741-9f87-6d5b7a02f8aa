#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地執行用戶統計處理腳本

=====================================================
功能概述
=====================================================
此腳本提供與 API 功能完全一致的本地執行版本，專門用於解決處理大量數據時的記憶體不足（OOM）問題。
它完整複製了用戶統計計算流程，包括快照載入、增量處理、分群計算、LTA 寫入等功能。

主要特色：
1. 支援 BigQuery 計算模式，避免本地記憶體限制
2. 快照式增量處理，提高效率
3. 完整的數據驗證和錯誤處理
4. 本地 parquet 檔案輸出
5. 詳細的處理進度和統計資訊

=====================================================
使用方式
=====================================================

基本用法（推薦使用 BigQuery 計算模式）：
    python local_user_stats_script.py \
        --ec-ids 107 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --use-bigquery-compute

處理多個 EC ID：
    python local_user_stats_script.py \
        --ec-ids 107,108,109 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --use-bigquery-compute

傳統模式（使用快照增量處理）：
    python local_user_stats_script.py \
        --ec-ids 107 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --local-output /path/to/output

跳過快照，從頭處理：
    python local_user_stats_script.py \
        --ec-ids 107 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --skip-snapshot

自訂批次大小和工作進程：
    python local_user_stats_script.py \
        --ec-ids 107 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --batch-size 100000 \
        --workers 8

詳細輸出模式：
    python local_user_stats_script.py \
        --ec-ids 107 \
        --start-time "2025-05-27T16:00:00Z" \
        --end-time "2025-06-02T16:00:00Z" \
        --use-bigquery-compute \
        --verbose

=====================================================
參數說明
=====================================================

必要參數：
    --ec-ids                    電商 ID 列表 (可以指定多個，用逗號分隔)
                                範例：107 或 107,108,109

    --start-time                開始時間 (ISO 格式)
                                範例："2025-05-27T16:00:00Z"

    --end-time                  結束時間 (ISO 格式)
                                範例："2025-06-02T16:00:00Z"

可選參數：
    --timezone                  時區設定 (預設: UTC)
                                範例："Asia/Taipei", "UTC"

    --use-bigquery-compute      使用 BigQuery 直接計算模式（強烈推薦）
                                此模式避免本地記憶體限制，適合處理大量數據

    --skip-snapshot            跳過快照載入，強制從頭開始處理
                                適用於需要重新計算所有數據的情況

    --skip-validation          跳過數據驗證步驟
                                可以加快處理速度，但可能影響數據品質

    --local-output             本地輸出目錄 (預設: ./local_output)
                                所有 parquet 檔案和摘要報告都會存放在此目錄

    --batch-size               分批處理大小 (預設: 50000)
                                較大的批次可能更快，但需要更多記憶體

    --max-repair-days          最大修復天數 (預設: 30)
                                限制增量處理的最大天數，避免處理時間過長

    --workers                  並行處理工作進程數 (預設: 4)
                                適當增加可以提高處理速度

    --verbose                  詳細輸出模式
                                顯示更多除錯資訊

    --quiet                    安靜模式
                                只顯示錯誤訊息

=====================================================
處理模式說明
=====================================================

1. BigQuery 計算模式 (推薦)
   - 使用 --use-bigquery-compute 啟用
   - 在 BigQuery 中執行所有計算，避免本地記憶體問題
   - 適合處理大量數據（如超過 100 萬筆記錄）
   - 會自動更新 user_stats 表格、儲存快照、寫入 LTA

2. 傳統模式（快照 + 增量）
   - 預設模式，會先尋找最近的快照
   - 從快照日期開始進行增量處理
   - 逐日更新用戶統計資料
   - 適合小到中等規模的數據處理

3. 從頭處理模式
   - 使用 --skip-snapshot 啟用
   - 忽略所有快照，從指定時間範圍的開始重新計算
   - 適用於需要重新計算或驗證數據的情況

=====================================================
輸出說明
=====================================================

本地輸出檔案：
- ec_{ec_id}_user_stats_{mode}_{timestamp}.parquet
  - 包含完整的用戶統計資料
  - mode 可能是 'bigquery_computed', 'incremental', 或 'full'

- ec_{ec_id}_summary_{mode}_{timestamp}.txt
  - 包含數據摘要和統計資訊
  - 數據品質檢查結果
  - 處理過程中的關鍵指標

日誌檔案：
- local_user_stats_{timestamp}.log
  - 完整的處理日誌
  - 包含所有除錯資訊和錯誤訊息

=====================================================
注意事項
=====================================================

1. 記憶體管理：
   - 對於大量數據（>200萬筆），強烈建議使用 --use-bigquery-compute
   - 如果使用傳統模式，可以調整 --batch-size 和 --workers 參數

2. 網路和權限：
   - 確保有 BigQuery 和 Cloud Storage 的適當權限
   - 確保網路連線穩定，大量數據處理可能需要較長時間

3. 時間格式：
   - 所有時間都應使用 ISO 8601 格式
   - 建議明確指定時區資訊

4. 錯誤處理：
   - 腳本會自動重試失敗的操作
   - 使用 --verbose 可以獲得更詳細的錯誤資訊

5. 輸出管理：
   - 定期清理 local_output 目錄中的舊檔案
   - parquet 檔案可能佔用較大空間

=====================================================
常見問題解答
=====================================================

Q: 出現記憶體不足錯誤怎麼辦？
A: 使用 --use-bigquery-compute 參數，或減少 --batch-size 的值

Q: 處理時間很長是正常的嗎？
A: 是的，大量數據處理需要時間。使用 --verbose 可以監控進度

Q: 如何確認處理結果是否正確？
A: 檢查輸出的摘要檔案，比較處理前後的統計數據

Q: 可以中斷處理後繼續嗎？
A: 目前不支援斷點續傳，建議使用較小的時間範圍分批處理

Q: BigQuery 計算模式和傳統模式的差別？
A: BigQuery 模式在雲端計算，避免本地記憶體限制；傳統模式在本地計算，可能有記憶體問題但處理過程更可控

=====================================================
範例使用情境
=====================================================

1. 解決 OOM 問題：
   python local_user_stats_script.py --ec-ids 107 --start-time "2025-05-27T16:00:00Z" --end-time "2025-06-02T16:00:00Z" --use-bigquery-compute

2. 小規模數據處理：
   python local_user_stats_script.py --ec-ids 108 --start-time "2025-06-01T16:00:00Z" --end-time "2025-06-02T16:00:00Z"

3. 重新計算特定時間範圍：
   python local_user_stats_script.py --ec-ids 107 --start-time "2025-05-27T16:00:00Z" --end-time "2025-06-02T16:00:00Z" --skip-snapshot

4. 批量處理多個 EC ID：
   python local_user_stats_script.py --ec-ids 107,108,109,110 --start-time "2025-05-27T16:00:00Z" --end-time "2025-06-02T16:00:00Z" --use-bigquery-compute

5. 除錯模式：
   python local_user_stats_script.py --ec-ids 107 --start-time "2025-06-01T16:00:00Z" --end-time "2025-06-02T16:00:00Z" --verbose --skip-validation

"""

import argparse
import json
import os
import sys
import time
import logging
import tempfile
import traceback
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
import pytz
from collections import defaultdict
from google.cloud import bigquery

# 將專案根目錄加入 sys.path
ROOT_DIR = Path(__file__).parent.parent.absolute()  # 向上兩層到專案根目錄
sys.path.append(str(ROOT_DIR))

# 導入必要的模組
from src.utils import logging_setup, time_utils
from src.core import data_processing, cloud_integration
from src.core.cloud_integration import BigQueryClient, StorageManager
from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic
from src.optimization.memory_optimizer import ArrowDataProcessor
from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery

# 設定 logger（將在 main() 中重新配置）
logger = logging.getLogger(__name__)

class LocalUserStatsProcessor:
    """
    本地用戶統計處理器

    這個類別是整個本地處理邏輯的核心，提供完整的用戶統計處理功能。
    它封裝了兩種主要的處理模式：

    1. BigQuery 計算模式：
       - 在 BigQuery 中執行所有計算邏輯
       - 避免本地記憶體限制，適合大量數據處理
       - 自動執行快照存儲、LTA 寫入、user_stats 表格更新

    2. 傳統模式（快照 + 增量處理）：
       - 載入最近的快照作為基礎
       - 逐日處理增量數據
       - 適合中小規模數據或需要細粒度控制的情況

    主要功能包括：
    - 智能快照管理（查找、載入、驗證）
    - 增量數據處理（逐日更新）
    - 完整數據處理（從頭開始）
    - 動態分群計算
    - 本地輸出管理（parquet 檔案、統計摘要）
    - 詳細的日誌記錄和錯誤處理
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化本地用戶統計處理器

        Args:
            config (Dict[str, Any]): 配置字典，包含以下鍵值：
                - ec_ids (List[int]): 要處理的電商 ID 列表
                - start_time (str): 開始時間（ISO 格式）
                - end_time (str): 結束時間（ISO 格式）
                - timezone (str): 時區設定，預設 'UTC'
                - use_bigquery_compute (bool): 是否使用 BigQuery 計算模式
                - skip_snapshot (bool): 是否跳過快照載入
                - skip_validation (bool): 是否跳過數據驗證
                - local_output (str): 本地輸出目錄路徑
                - batch_size (int): 分批處理大小
                - max_repair_days (int): 最大修復天數
                - workers (int): 並行處理工作進程數

        初始化過程會：
        1. 設定所有配置參數
        2. 創建本地輸出目錄
        3. 初始化結果字典結構
        4. 記錄關鍵配置資訊到日誌
        """
        self.config = config
        self.ec_ids = config.get('ec_ids', [])
        self.start_time = config.get('start_time')
        self.end_time = config.get('end_time')
        self.timezone_str = config.get('timezone', 'UTC')
        self.use_bigquery_compute = config.get('use_bigquery_compute', False)
        self.skip_snapshot = config.get('skip_snapshot', False)
        self.skip_validation = config.get('skip_validation', False)
        self.local_output = config.get('local_output', './local_output')
        self.batch_size = config.get('batch_size', 50000)
        self.max_repair_days = config.get('max_repair_days', 30)
        self.workers = config.get('workers', 4)

        # 建立輸出目錄
        os.makedirs(self.local_output, exist_ok=True)

        # 初始化結果
        self.results = {
            'calculate_stats': False,
            'save_snapshot': False,
            'write_lta': False,
            'update_user_stats': False,
            'total_cost_usd': 0,
            'total_cost_twd': 0,
            'total_bytes_processed': 0,
            'snapshot_used': False,
            'snapshot_type': None,
            'segment_stats': {},
            'repaired_dates': [],
            'days_repaired': 0,
            'daily_segment_stats': {},
            'users': 0,
            'message': ''
        }

        logger.info(f"初始化本地用戶統計處理器")
        logger.info(f"EC IDs: {self.ec_ids}")
        logger.info(f"時間範圍: {self.start_time} 到 {self.end_time}")
        logger.info(f"時區: {self.timezone_str}")
        logger.info(f"使用 BigQuery 計算模式: {self.use_bigquery_compute}")
        logger.info(f"本地輸出目錄: {self.local_output}")

    def parse_time(self, time_str: str) -> datetime:
        """解析時間字串為 datetime 物件"""
        try:
            if isinstance(time_str, datetime):
                dt = time_str
            else:
                if time_str is None or time_str == "":
                    raise ValueError("時間字串不能為空")

                # 使用 dateutil.parser 解析時間字串
                from dateutil import parser
                dt = parser.parse(str(time_str))

            # 確保時區信息
            if dt.tzinfo is None:
                tz = pytz.timezone(self.timezone_str)
                dt = tz.localize(dt)
            return dt
        except Exception as e:
            logger.error(f"解析時間字串 '{time_str}' 時出錯: {str(e)}")
            raise ValueError(f"無法解析時間字串: {time_str}") from e

    def fix_days_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        修復 _days 欄位的 None 值問題

        確保所有以 '_days' 結尾的欄位都不會是 None，而是 -1 或有效的整數值。
        這可以避免在規則評估時出現 "TypeError: '>' not supported between instances of 'NoneType' and 'int'" 錯誤。

        Args:
            df: 輸入的 DataFrame

        Returns:
            修復後的 DataFrame
        """
        logger.info("開始修復 _days 欄位的 None 值問題")

        # 找出所有 _days 欄位
        days_columns = [col for col in df.columns if col.endswith('_days')]

        if not days_columns:
            logger.info("沒有找到 _days 欄位，無需修復")
            return df

        logger.info(f"找到 {len(days_columns)} 個 _days 欄位: {days_columns}")

        # 修復每個 _days 欄位
        for col in days_columns:
            # 檢查 None 值的數量
            none_count = df[col].isnull().sum()

            if none_count > 0:
                logger.info(f"欄位 '{col}' 有 {none_count} 個 None 值，將設為 -1")
                df[col] = df[col].fillna(-1)

            # 確保資料類型是整數
            try:
                df[col] = df[col].astype(int)
            except Exception as e:
                logger.warning(f"無法將欄位 '{col}' 轉換為整數: {str(e)}")
                # 強制轉換：先轉為 float，再轉為 int
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(-1).astype(int)
                    logger.info(f"使用強制轉換成功處理欄位 '{col}'")
                except Exception as e2:
                    logger.error(f"強制轉換欄位 '{col}' 也失敗: {str(e2)}")
                    # 最後手段：直接設為 -1
                    df[col] = -1

            # 檢查是否還有異常值
            invalid_count = 0
            if col in df.columns:
                # 檢查是否有 inf 或 NaN 值
                invalid_mask = ~np.isfinite(df[col])
                invalid_count = invalid_mask.sum()

                if invalid_count > 0:
                    logger.warning(f"欄位 '{col}' 有 {invalid_count} 個無效值（inf 或 NaN），將設為 -1")
                    df.loc[invalid_mask, col] = -1

            logger.debug(f"欄位 '{col}' 修復完成，資料類型: {df[col].dtype}，範圍: {df[col].min()} 到 {df[col].max()}")

        logger.info("_days 欄位修復完成")
        return df

    def load_local_audience_rules(self, ec_id: int) -> Dict[str, Any]:
        """
        從本地 audience_rules.json 檔案載入規則作為後備方案

        當無法從 Cloud Storage 載入規則時，使用這個函數從本地檔案讀取規則。

        Args:
            ec_id: 電商 ID

        Returns:
            規則字典，格式與 fetch_audience_mapping_with_rules 相同
        """
        logger.info(f"嘗試從本地檔案載入 EC ID {ec_id} 的受眾規則")

        # 尋找 audience_rules.json 檔案的可能位置
        possible_paths = [
            os.path.join(ROOT_DIR, 'scripts', 'audience_rules.json'),
            os.path.join(ROOT_DIR, 'audience_rules.json'),
            './audience_rules.json',
            '../audience_rules.json',
            'scripts/audience_rules.json',
            # CI 環境中可能的路徑
            'lta-user-stats/scripts/audience_rules.json',
            os.path.join(os.getcwd(), 'lta-user-stats', 'scripts', 'audience_rules.json'),
            # 絕對路徑變體
            os.path.abspath(os.path.join(os.path.dirname(__file__), 'audience_rules.json')),
            os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'scripts', 'audience_rules.json'))
        ]

        rules_file_path = None
        logger.info(f"當前工作目錄: {os.getcwd()}")
        logger.info(f"ROOT_DIR: {ROOT_DIR}")
        logger.info(f"__file__: {__file__}")

        for path in possible_paths:
            logger.debug(f"檢查路徑: {path}")
            if os.path.exists(path):
                rules_file_path = path
                logger.info(f"找到規則檔案: {rules_file_path}")
                break

        if not rules_file_path:
            logger.error(f"無法找到 audience_rules.json 檔案，已檢查路徑: {possible_paths}")
            return {}

        try:
            # 讀取規則檔案
            with open(rules_file_path, 'r', encoding='utf-8') as f:
                all_rules = json.loads(f.read())

            logger.info(f"成功讀取規則檔案，包含 {len(all_rules)} 個 EC")

            # 檢查 EC ID 是否存在
            ec_id_str = str(ec_id)
            if ec_id_str not in all_rules:
                logger.warning(f"EC ID {ec_id} 不存在於本地規則檔案中")
                logger.info(f"可用的 EC ID: {list(all_rules.keys())}")
                return {}

            ec_rules = all_rules.get(ec_id_str, {}).get('rules', {})
            logger.info(f"獲取到 EC {ec_id} 的本地規則，共 {len(ec_rules)} 條")

            # 處理 DXP 前綴邏輯（與原函數保持一致）
            prefix = f"tm:c_9999_{ec_id}_c_"

            # 檢查是否有符合前綴的規則
            matching_rules = [rule_id for rule_id in ec_rules.keys() if rule_id.startswith(prefix)]
            if not matching_rules:
                logger.warning(f"沒有找到符合前綴 {prefix} 的規則")
                logger.info(f"規則 ID 範例: {list(ec_rules.keys())[:5]}{'...' if len(ec_rules) > 5 else ''}")
                return {}

            result = {
                rule_id: rule_data
                for rule_id, rule_data in ec_rules.items()
                if rule_id.startswith(prefix)
            }

            logger.info(f"成功載入本地規則，處理後的規則數量: {len(result)}")

            # 檢查規則格式
            if result:
                sample_rule_id = next(iter(result))
                sample_rule = result[sample_rule_id]
                logger.info(f"本地規則範例 {sample_rule_id}: {sample_rule.keys()}")

                # 檢查規則是否包含必要的欄位
                if 'rule' not in sample_rule:
                    logger.warning(f"本地規則 {sample_rule_id} 缺少 'rule' 欄位")
                if 'data' not in sample_rule:
                    logger.warning(f"本地規則 {sample_rule_id} 缺少 'data' 欄位")

            return result

        except Exception as e:
            logger.error(f"讀取本地規則檔案時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            return {}

    def get_audience_rules_with_fallback(self, ec_id: int) -> Dict[str, Any]:
        """
        獲取受眾規則，先嘗試從 Cloud Storage，失敗時使用本地檔案

        Args:
            ec_id: 電商 ID

        Returns:
            規則字典
        """
        logger.info(f"開始獲取 EC ID {ec_id} 的受眾規則")

        try:
            # 首先嘗試從 Cloud Storage 載入
            from src.rules.dynamic_audience_rules import fetch_audience_mapping_with_rules

            logger.info("嘗試從 Cloud Storage 載入受眾規則...")
            rules = fetch_audience_mapping_with_rules(ec_id, add_to_dxp=True)

            if rules:
                logger.info(f"成功從 Cloud Storage 載入 {len(rules)} 條規則")
                return rules
            else:
                logger.warning("從 Cloud Storage 載入的規則為空，嘗試使用本地檔案")

        except Exception as e:
            logger.warning(f"從 Cloud Storage 載入規則失敗: {str(e)}")
            logger.info("嘗試使用本地檔案作為後備方案")

        # 使用本地檔案作為後備
        return self.load_local_audience_rules(ec_id)

    def calculate_audience_segments_with_fallback(self, user_stats_df: pd.DataFrame, ec_id: int) -> Dict[str, Any]:
        """
        使用已優化的向量化分群計算 - 直接調用 /src 中的優化版本

        這個函數現在直接使用 /src/rules/dynamic_audience_rules.py 中已經整合了
        optimized_vectorized_evaluate 的 calculate_audience_segments_dynamic 函數，
        可以獲得 600+ 倍的性能提升。

        Args:
            user_stats_df: 用戶統計資料
            ec_id: 電商 ID

        Returns:
            分群結果字典
        """
        logger.info(f"使用已優化的向量化分群計算，EC ID {ec_id}，用戶數: {len(user_stats_df)}")

        try:
            # 預先修復資料中的 None 值問題
            user_stats_df = self.fix_days_columns(user_stats_df)

            # 直接使用 /src 中已經優化過的分群計算函數
            # 這個函數內部已經整合了 optimized_vectorized_evaluate，可以獲得巨大的性能提升
            logger.info("調用已優化的 calculate_audience_segments_dynamic")
            return calculate_audience_segments_dynamic(user_stats_df, ec_id, add_to_dxp=True)

        except Exception as e:
            logger.error(f"優化版分群計算時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")

            # 如果仍然失敗，返回空結果
            logger.error("分群計算完全失敗，返回空結果")
            return {}

    # 注意：以下自定義分群函數已移除，因為我們現在直接使用 /src 中已優化的版本
    # 這些函數包括：
    # - _preprocess_for_segmentation: 已由 fix_days_columns 替代
    # - _parallel_rule_evaluation: 已由 /src 中的 optimized_vectorized_evaluate 替代
    # - _sequential_rule_evaluation: 已由 /src 中的 optimized_vectorized_evaluate 替代
    # - _evaluate_rule_optimized: 已由向量化評估器替代
    # - _try_pandas_query: 已由向量化評估器替代
    # - _evaluate_batch: 已由向量化評估器替代
    #
         # 移除這些函數可以：
     # 1. 避免重複實現和維護
     # 2. 確保使用最新的優化版本
     # 3. 獲得 600+ 倍的性能提升

    def find_latest_snapshot(self, target_date: datetime, ec_id: int, max_days_back: int = 30):
        """尋找最近的可用快照"""
        if self.skip_snapshot:
            logger.info("跳過快照搜尋（依用戶要求）")
            return None, None, None

        logger.info(f"尋找 EC ID {ec_id} 在 {target_date.date()} 的最近快照...")
        return data_processing.find_latest_snapshot(target_date, ec_id, max_days_back)

    def load_snapshot(self, snapshot_path: str, snapshot_type: str, ec_id: int) -> Optional[pd.DataFrame]:
        """載入快照數據"""
        try:
            if snapshot_type == "local":
                logger.info(f"從本地載入快照: {snapshot_path}")
                base_stats_df = pd.read_parquet(snapshot_path)
            else:  # "gcs"
                logger.info(f"從 GCS 載入快照: {snapshot_path}")
                storage_manager = StorageManager()

                # 使用與儲存快照時相同的檔名格式
                taiwan_date_str = time_utils.get_taiwan_date_str(self.start_time)
                local_temp = os.path.join(tempfile.gettempdir(), f"user_stats_{ec_id}_{taiwan_date_str}.parquet")

                storage_manager.bucket.blob(snapshot_path).download_to_filename(local_temp)
                base_stats_df = pd.read_parquet(local_temp)

                # 清理臨時檔案
                if os.path.exists(local_temp):
                    os.remove(local_temp)

            # 過濾出當前 ec_id 的資料
            if not base_stats_df.empty:
                base_stats_df = base_stats_df[base_stats_df['ec_id'] == ec_id]
                if base_stats_df.empty:
                    logger.warning(f"快照中沒有電商 ID {ec_id} 的資料")
                    return None
                else:
                    logger.info(f"成功從快照載入 {len(base_stats_df)} 筆電商 ID {ec_id} 的資料")
                    # 修復 _days 欄位的 None 值問題
                    base_stats_df = self.fix_days_columns(base_stats_df)
                    return base_stats_df
            else:
                logger.warning("載入的快照為空")
                return None

        except Exception as e:
            logger.error(f"載入快照時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            return None

    def validate_snapshot(self, df: pd.DataFrame) -> bool:
        """驗證快照數據"""
        if self.skip_validation:
            logger.info("跳過數據驗證（依用戶要求）")
            return True

        logger.info("開始驗證快照數據...")
        return data_processing.validate_snapshot(df)

    def calculate_user_stats_bigquery_mode(self) -> Dict[str, Any]:
        """
        使用 BigQuery 計算模式處理用戶統計

        改進版本：結合快照和 BigQuery 計算的優點
        1. 先檢查是否有可用快照
        2. 如果有快照：將快照上傳到 BigQuery 臨時表，進行增量合併
        3. 如果沒有快照：使用完整時間範圍計算
        4. 所有計算邏輯都在 BigQuery 中執行，避免本地記憶體限制

        優點：
        - 保留歷史時間資料（解決時間不夠問題）
        - 在 BigQuery 執行增量合併（速度快）
        - 避免本地記憶體限制
        - 自動執行所有必要的後續處理

        Returns:
            Dict[str, Any]: 處理結果
        """
        logger.info("使用 BigQuery 增量計算模式處理用戶統計資料")

        total_cost_usd = 0
        total_cost_twd = 0
        total_bytes_processed = 0
        total_users_processed = 0
        all_results = {}

        try:
            from google.cloud import bigquery

            for ec_id in self.ec_ids:
                logger.info(f"開始處理 EC ID: {ec_id}")

                # 首先嘗試找到最近可用的快照
                snapshot_date, snapshot_path, snapshot_type = self.find_latest_snapshot(
                    self.start_time, ec_id, self.max_repair_days
                )

                user_stats_df = None

                if snapshot_date is not None:
                    logger.info(f"找到{(self.end_time - snapshot_date).days}天前的快照: {snapshot_path} (來源: {snapshot_type})")

                    # 使用 BigQuery 增量模式
                    user_stats_df = self._bigquery_incremental_mode(
                        ec_id=ec_id,
                        snapshot_date=snapshot_date,
                        snapshot_path=snapshot_path,
                        snapshot_type=snapshot_type
                    )
                else:
                    logger.warning(f"EC ID {ec_id} 找不到快照，使用完整時間範圍計算")

                    # 使用完整 BigQuery 計算模式
                    user_stats_df = self._bigquery_full_mode(ec_id=ec_id)

                if user_stats_df is None or user_stats_df.empty:
                    logger.warning(f"EC ID {ec_id} 沒有數據")
                    continue

                logger.info(f"EC ID {ec_id} 獲得 {len(user_stats_df)} 筆用戶統計資料")
                total_users_processed += len(user_stats_df)

                # 修復 _days 欄位的 None 值問題
                user_stats_df = self.fix_days_columns(user_stats_df)

                # 保存本地輸出
                self.save_local_output(user_stats_df, ec_id, "bigquery_incremental")

                # 更新 user_stats 表格
                try:
                    logger.info(f"更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                    bq_client = BigQueryClient()
                    bq_client.update_user_stats_table(
                        user_stats_df=user_stats_df,
                        ec_id=ec_id,
                        current_date=self.end_time,
                        skip_time_standardization=True,
                        only_required_columns=True
                    )
                    logger.info(f"✓ 成功更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                except Exception as e:
                    logger.error(f"✗ 更新 BigQuery user_stats 表格失敗，EC ID: {ec_id}，錯誤: {str(e)}")

                # 儲存快照
                try:
                    logger.info(f"儲存快照，EC ID: {ec_id}")
                    snapshot_result = self.save_user_stats_snapshot(user_stats_df, ec_id)
                    logger.info(f"✓ 成功儲存快照，EC ID: {ec_id}")
                except Exception as e:
                    logger.error(f"✗ 儲存快照失敗，EC ID: {ec_id}，錯誤: {str(e)}")

                # 寫入 LTA
                try:
                    logger.info(f"寫入 LTA，EC ID: {ec_id}")
                    lta_result = self.write_to_special_lta(user_stats_df, ec_id)
                    logger.info(f"✓ 成功寫入 LTA，EC ID: {ec_id}")

                    # 收集分群統計資料
                    if isinstance(lta_result, dict) and 'segment_stats' in lta_result:
                        all_results[str(ec_id)] = lta_result['segment_stats']

                except Exception as e:
                    logger.error(f"✗ 寫入 LTA 失敗，EC ID: {ec_id}，錯誤: {str(e)}")

                logger.info(f"完成處理 EC ID: {ec_id}")

            # 構建回應
            response = {
                'calculate_stats': True,
                'total_cost_usd': total_cost_usd,
                'total_cost_twd': total_cost_twd,
                'total_bytes_processed': total_bytes_processed,
                'users': total_users_processed,
                'use_bigquery_compute': True,
                'message': f'成功使用 BigQuery 增量計算模式處理 {len(self.ec_ids)} 個 EC ID，總計 {total_users_processed} 位用戶'
            }

            # 如果有分群統計資料
            if all_results:
                response['segment_stats'] = all_results

            logger.info(f"BigQuery 增量計算模式完成: {response}")
            return response

        except Exception as e:
            logger.error(f"BigQuery 增量計算模式發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            return {
                'error': f'BigQuery 增量計算模式失敗: {str(e)}',
                'use_bigquery_compute': True
            }

    def _bigquery_incremental_mode(self, ec_id: int, snapshot_date: datetime,
                                 snapshot_path: str, snapshot_type: str) -> pd.DataFrame:
        """
        BigQuery 增量模式：將快照上傳至 BigQuery 並進行增量合併

        Args:
            ec_id: 電商 ID
            snapshot_date: 快照日期
            snapshot_path: 快照路徑
            snapshot_type: 快照類型 ("local" 或 "gcs")

        Returns:
            合併後的用戶統計資料 DataFrame
        """
        logger.info(f"使用 BigQuery 增量模式處理 EC ID {ec_id}")

        from google.cloud import bigquery
        client = bigquery.Client()

        # 1. 載入快照資料到 DataFrame
        base_stats_df = self.load_snapshot(snapshot_path, snapshot_type, ec_id)
        if base_stats_df is None or base_stats_df.empty:
            logger.warning("快照載入失敗，改用完整模式")
            return self._bigquery_full_mode(ec_id)

        logger.info(f"成功載入快照，包含 {len(base_stats_df)} 筆資料")

        # 2. 創建臨時表格 ID
        temp_table_id = f"temp_snapshot_{ec_id}_{int(time.time())}"

        # 根據專案選擇適當的 temp dataset
        current_project = client.project
        dataset_id = "temp" if current_project == "tagtoo-tracking" else "temp_tables"
        temp_table_ref = client.dataset(dataset_id).table(temp_table_id)

        try:
            # 3. 上傳快照 DataFrame 到 BigQuery 臨時表（使用動態 Schema）
            self._upload_snapshot_to_temp_table(base_stats_df, temp_table_ref.table_id, client)

            # 4. 構建增量合併 SQL
            incremental_query = self._build_incremental_merge_query(
                ec_id=ec_id,
                temp_table_id=temp_table_id,
                snapshot_date=snapshot_date,
                end_date=self.end_time,
                client=client
            )

            # 5. 執行增量合併查詢
            logger.info("開始執行 BigQuery 增量合併查詢")
            query_job = client.query(incremental_query)
            result = query_job.result()

            # 6. 轉換結果為 DataFrame
            user_stats_df = result.to_dataframe()
            logger.info(f"BigQuery 增量合併完成，結果包含 {len(user_stats_df)} 筆資料")

            return user_stats_df

        finally:
            # 7. 清理臨時表格
            try:
                client.delete_table(temp_table_ref, not_found_ok=True)
                logger.info(f"已清理臨時表格: {temp_table_id}")
            except Exception as e:
                logger.warning(f"清理臨時表格失敗: {str(e)}")

    def _bigquery_full_mode(self, ec_id: int) -> pd.DataFrame:
        """
        BigQuery 完整模式：使用完整時間範圍計算
        """
        logger.info(f"使用 BigQuery 完整模式處理 EC ID {ec_id}")

        from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery
        from google.cloud import bigquery

        # 創建查詢配置
        query_job_config = bigquery.QueryJobConfig()
        query_job_config.use_query_cache = True
        query_job_config.priority = "INTERACTIVE"
        query_job_config.use_legacy_sql = False
        query_job_config.maximum_bytes_billed = 100_000_000_000  # 100GB

        # 調用 BigQuery 計算函數
        user_stats_df = calculate_user_stats_via_bigquery(
            ec_id=ec_id,
            time_range=[self.start_time, self.end_time],
            query_job_config=query_job_config,
            force_query=True
        )

        return user_stats_df

    def _build_incremental_merge_query(self, ec_id: int, temp_table_id: str,
                                     snapshot_date: datetime, end_date: datetime, client) -> str:
        """
        構建增量合併的 SQL 查詢

        這個查詢會：
        1. 從快照獲取基礎資料
        2. 查詢增量期間的新資料
        3. 合併更新統計欄位
        """

        # 獲取當前 BigQuery client 使用的專案 ID 和對應的 temp dataset
        current_project = client.project
        if current_project == "tagtoo-tracking":
            temp_dataset = "temp"
        else:
            temp_dataset = "temp_tables"

        query = f"""
        WITH snapshot_data AS (
            SELECT * FROM `{current_project}.{temp_dataset}.{temp_table_id}`
        ),

        incremental_data AS (
            SELECT
                {ec_id} as ec_id,
                permanent,
                COUNT(DISTINCT session.id) as new_sessions,
                COUNT(DISTINCT DATE(event_time)) as new_active_days,
                COUNT(IF(event.name = 'purchase', 1, NULL)) as new_purchase_count,
                MIN(IF(event.name = 'register', event_time, NULL)) as new_registration_time,
                MIN(event_time) as new_first_interaction_time,
                MAX(event_time) as new_last_interaction_time,
                MIN(IF(event.name = 'purchase', event_time, NULL)) as new_first_purchase_time,
                MAX(IF(event.name = 'purchase', event_time, NULL)) as new_last_purchase_time,
                MIN(IF(event.name = 'add_to_cart', event_time, NULL)) as new_first_add_to_cart_time,
                MAX(IF(event.name = 'add_to_cart', event_time, NULL)) as new_last_add_to_cart_time,
                MIN(IF(event.name = 'view_item', event_time, NULL)) as new_first_view_item_time,
                MAX(IF(event.name = 'view_item', event_time, NULL)) as new_last_view_item_time,
                SUM(IF(event.name = 'purchase', event.value, 0)) as new_purchase_amount
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ec_id = {ec_id}
            AND permanent IS NOT NULL
            AND event_time > TIMESTAMP('{snapshot_date.strftime("%Y-%m-%d %H:%M:%S")}')
            AND event_time <= TIMESTAMP('{end_date.strftime("%Y-%m-%d %H:%M:%S")}')
            GROUP BY permanent
        ),

        merged_data AS (
            SELECT
                COALESCE(s.ec_id, i.ec_id) as ec_id,
                COALESCE(s.permanent, i.permanent) as permanent,

                -- 合併 sessions
                COALESCE(s.total_sessions, 0) + COALESCE(i.new_sessions, 0) as total_sessions,

                -- 合併 active_days（如果快照中為 -1，則只使用新的 active_days；否則累加）
                CASE
                    WHEN COALESCE(s.active_days, -1) = -1 THEN COALESCE(i.new_active_days, 0)
                    ELSE COALESCE(s.active_days, 0) + COALESCE(i.new_active_days, 0)
                END as active_days,

                -- 合併 purchase_count
                COALESCE(s.purchase_count, 0) + COALESCE(i.new_purchase_count, 0) as purchase_count,

                -- 合併 registration_time (取最早的，直接比較 TIMESTAMP)
                CASE
                    WHEN s.registration_time IS NOT NULL AND i.new_registration_time IS NOT NULL
                        THEN LEAST(s.registration_time, i.new_registration_time)
                    WHEN s.registration_time IS NOT NULL THEN s.registration_time
                    WHEN i.new_registration_time IS NOT NULL THEN i.new_registration_time
                    ELSE NULL
                END as registration_time,

                -- 合併 first_interaction_time (取最早的，直接比較 TIMESTAMP)
                CASE
                    WHEN s.first_interaction_time IS NOT NULL AND i.new_first_interaction_time IS NOT NULL
                        THEN LEAST(s.first_interaction_time, i.new_first_interaction_time)
                    WHEN s.first_interaction_time IS NOT NULL THEN s.first_interaction_time
                    WHEN i.new_first_interaction_time IS NOT NULL THEN i.new_first_interaction_time
                    ELSE NULL
                END as first_interaction_time,

                -- 合併 last_interaction_time (取最新的，直接比較 TIMESTAMP)
                CASE
                    WHEN s.last_interaction_time IS NOT NULL AND i.new_last_interaction_time IS NOT NULL
                        THEN GREATEST(s.last_interaction_time, i.new_last_interaction_time)
                    WHEN s.last_interaction_time IS NOT NULL THEN s.last_interaction_time
                    WHEN i.new_last_interaction_time IS NOT NULL THEN i.new_last_interaction_time
                    ELSE NULL
                END as last_interaction_time,

                -- 合併 first_purchase_time (取最早的，直接比較 TIMESTAMP)
                CASE
                    WHEN s.first_purchase_time IS NOT NULL AND i.new_first_purchase_time IS NOT NULL
                        THEN LEAST(s.first_purchase_time, i.new_first_purchase_time)
                    WHEN s.first_purchase_time IS NOT NULL THEN s.first_purchase_time
                    WHEN i.new_first_purchase_time IS NOT NULL THEN i.new_first_purchase_time
                    ELSE NULL
                END as first_purchase_time,

                -- 合併 last_purchase_time (取最新的，直接比較 TIMESTAMP)
                CASE
                    WHEN s.last_purchase_time IS NOT NULL AND i.new_last_purchase_time IS NOT NULL
                        THEN GREATEST(s.last_purchase_time, i.new_last_purchase_time)
                    WHEN s.last_purchase_time IS NOT NULL THEN s.last_purchase_time
                    WHEN i.new_last_purchase_time IS NOT NULL THEN i.new_last_purchase_time
                    ELSE NULL
                END as last_purchase_time,

                -- 其他時間欄位類似處理（直接比較 TIMESTAMP）...
                CASE
                    WHEN s.first_add_to_cart_time IS NOT NULL AND i.new_first_add_to_cart_time IS NOT NULL
                        THEN LEAST(s.first_add_to_cart_time, i.new_first_add_to_cart_time)
                    WHEN s.first_add_to_cart_time IS NOT NULL THEN s.first_add_to_cart_time
                    WHEN i.new_first_add_to_cart_time IS NOT NULL THEN i.new_first_add_to_cart_time
                    ELSE NULL
                END as first_add_to_cart_time,

                CASE
                    WHEN s.last_add_to_cart_time IS NOT NULL AND i.new_last_add_to_cart_time IS NOT NULL
                        THEN GREATEST(s.last_add_to_cart_time, i.new_last_add_to_cart_time)
                    WHEN s.last_add_to_cart_time IS NOT NULL THEN s.last_add_to_cart_time
                    WHEN i.new_last_add_to_cart_time IS NOT NULL THEN i.new_last_add_to_cart_time
                    ELSE NULL
                END as last_add_to_cart_time,

                CASE
                    WHEN s.first_view_item_time IS NOT NULL AND i.new_first_view_item_time IS NOT NULL
                        THEN LEAST(s.first_view_item_time, i.new_first_view_item_time)
                    WHEN s.first_view_item_time IS NOT NULL THEN s.first_view_item_time
                    WHEN i.new_first_view_item_time IS NOT NULL THEN i.new_first_view_item_time
                    ELSE NULL
                END as first_view_item_time,

                CASE
                    WHEN s.last_view_item_time IS NOT NULL AND i.new_last_view_item_time IS NOT NULL
                        THEN GREATEST(s.last_view_item_time, i.new_last_view_item_time)
                    WHEN s.last_view_item_time IS NOT NULL THEN s.last_view_item_time
                    WHEN i.new_last_view_item_time IS NOT NULL THEN i.new_last_view_item_time
                    ELSE NULL
                END as last_view_item_time,

                -- 合併 total_purchase_amount
                COALESCE(s.total_purchase_amount, 0) + COALESCE(i.new_purchase_amount, 0) as total_purchase_amount,

                -- 時間戳
                CURRENT_TIMESTAMP() as created_at,
                CURRENT_TIMESTAMP() as updated_at

            FROM snapshot_data s
            FULL OUTER JOIN incremental_data i ON s.permanent = i.permanent
        )

        SELECT * FROM merged_data
        WHERE permanent IS NOT NULL
        ORDER BY permanent
        """

        return query

    def calculate_user_stats_traditional_mode(self) -> Dict[str, Any]:
        """
        使用傳統模式（快照+增量）處理用戶統計

        這是預設的處理模式，使用快照式增量處理來提高效率。適合中小規模數據或
        需要更細粒度控制的情況。

        處理邏輯：
        1. 嘗試尋找最近可用的快照
        2. 如果找到快照：
           - 載入並驗證快照數據
           - 從快照日期開始進行增量處理
           - 逐日更新用戶統計資料
        3. 如果沒有快照：
           - 從頭開始處理完整時間範圍的數據
        4. 儲存處理結果和統計摘要

        特點：
        - 利用快照減少重複計算
        - 逐日增量處理，避免一次性載入過多數據
        - 支援修復缺失的日期資料
        - 提供詳細的處理進度和統計資訊

        Returns:
            Dict[str, Any]: 處理結果，包含快照使用情況、修復日期、分群統計等資訊
        """
        logger.info("使用傳統模式處理用戶統計資料")

        result = {
            'calculate_stats': True,
            'save_snapshot': False,
            'write_lta': False,
            'update_user_stats': False,
            'total_cost_usd': 0,
            'total_cost_twd': 0,
            'total_bytes_processed': 0,
            'snapshot_used': False,
            'snapshot_type': None,
            'users': 0,
            'segment_stats': {},
            'repaired_dates': [],
            'days_repaired': 0,
            'daily_segment_stats': {}
        }

        try:
            # 初始化 BigQuery 客戶端
            from google.cloud import bigquery
            client = bigquery.Client()
            total_bytes_processed = 0

            # 對每個電商 ID 進行處理
            for current_ec_id in self.ec_ids:
                logger.info(f"開始處理電商 ID: {current_ec_id}")

                # 首先嘗試找到最近可用的快照
                snapshot_date, snapshot_path, snapshot_type = self.find_latest_snapshot(
                    self.start_time, current_ec_id, self.max_repair_days
                )

                base_stats_df = None

                if snapshot_date is not None:
                    logger.info(f"找到{(self.end_time - snapshot_date).days}天前的快照: {snapshot_path} (來源: {snapshot_type})")

                    # 載入快照
                    base_stats_df = self.load_snapshot(snapshot_path, snapshot_type, current_ec_id)

                    if base_stats_df is not None and not base_stats_df.empty:
                        # 驗證快照
                        if self.validate_snapshot(base_stats_df):
                            result['snapshot_used'] = True
                            result['snapshot_type'] = snapshot_type

                            # 執行增量處理
                            incremental_result = self.process_incremental_data(
                                base_stats_df, current_ec_id, snapshot_date, client
                            )

                            # 合併結果
                            for key, value in incremental_result.items():
                                if key in ['total_cost_usd', 'total_cost_twd', 'total_bytes_processed', 'users']:
                                    result[key] += value
                                elif key in ['repaired_dates']:
                                    result[key].extend(value)
                                elif key in ['days_repaired']:
                                    result[key] += value
                                elif key in ['segment_stats', 'daily_segment_stats']:
                                    result[key].update(value)
                                else:
                                    result[key] = value
                        else:
                            logger.error(f"快照驗證失敗，將從頭開始處理")
                            base_stats_df = None

                if base_stats_df is None:
                    # 沒有快照或快照無效，從頭開始處理
                    logger.info(f"沒有可用快照，從頭開始處理 EC ID {current_ec_id}")
                    full_result = self.process_full_data(current_ec_id, client)

                    # 合併結果
                    for key, value in full_result.items():
                        if key in ['total_cost_usd', 'total_cost_twd', 'total_bytes_processed', 'users']:
                            result[key] += value
                        elif key in ['segment_stats', 'daily_segment_stats']:
                            result[key].update(value)
                        else:
                            result[key] = value

            # 設置成功標記
            result['calculate_stats'] = True
            result['message'] = f'成功處理 {len(self.ec_ids)} 個 EC ID，總計 {result["users"]} 位用戶'

            logger.info(f"傳統模式處理完成: {result}")
            return result

        except Exception as e:
            logger.error(f"傳統模式處理發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            result['error'] = str(e)
            return result

    def process_incremental_data(self, base_stats_df: pd.DataFrame, ec_id: int,
                                snapshot_date: datetime, client) -> Dict[str, Any]:
        """處理增量數據（從快照日期到結束日期）"""
        logger.info(f"開始處理增量數據，從 {snapshot_date.date()} 到 {self.end_time.date()}")

        result = {
            'total_cost_usd': 0,
            'total_cost_twd': 0,
            'total_bytes_processed': 0,
            'users': len(base_stats_df),
            'segment_stats': {},
            'daily_segment_stats': {},
            'repaired_dates': [],
            'days_repaired': 0
        }

        # 計算需要修復的天數
        days_to_repair = (self.end_time - snapshot_date).days

        # 檢查 end_time 是否少於當天的 16:00 UTC (台灣時間 00:00)
        if self.end_time.hour <= 16 and self.end_time.minute == 0 and self.end_time.second == 0:
            logger.info(f"end_time 是 {self.end_time.date()} 的 16:00 UTC (台灣時間 00:00)，需要調整 days_to_repair")
            days_to_repair -= 1

        # 確保 days_to_repair 至少為 0
        days_to_repair = max(0, days_to_repair)

        if days_to_repair > self.max_repair_days:
            logger.warning(f"需要修復的天數({days_to_repair})超過最大限制({self.max_repair_days})，將只修復最近的 {self.max_repair_days} 天")
            days_to_repair = self.max_repair_days
            snapshot_date = self.end_time - timedelta(days=self.max_repair_days)

        if days_to_repair > 0:
            current_stats_df = base_stats_df.copy()
            current_date = snapshot_date

            # 逐日處理資料
            for day_offset in range(1, days_to_repair + 1):
                day_start = current_date + timedelta(days=1)
                day_end = day_start + timedelta(days=1)
                logger.info(f"處理第 {day_offset} 天 ({day_start.date()} -> {day_end.date()}) 的資料")

                # 記錄修復日期
                taiwan_dt = day_start.astimezone(pytz.timezone('Asia/Taipei'))
                taiwan_date_str = taiwan_dt.strftime('%Y-%m-%d')
                result['repaired_dates'].append(taiwan_date_str)

                # 查詢該天的資料
                try:
                    from src.core import queries
                    daily_query = queries.daily_user_stats_query(ec_id, day_start, day_end)
                    logger.debug(f"每日查詢 SQL:\n{daily_query}")

                    daily_job = client.query(daily_query)
                    daily_results = daily_job.result()
                    daily_stats_df = daily_results.to_dataframe()
                    result['total_bytes_processed'] += daily_job.total_bytes_processed

                    if daily_stats_df.empty:
                        logger.warning(f"電商 ID {ec_id} 在 {day_start.date()} 沒有新資料")
                    else:
                        logger.info(f"成功查詢到 {len(daily_stats_df)} 筆電商 ID {ec_id} 在 {day_start.date()} 的資料")

                        # 更新統計資料
                        current_stats_df = data_processing.update_user_stats_with_daily_data(
                            current_stats_df, daily_stats_df
                        )

                        # 儲存快照
                        snapshot_result = self.save_user_stats_snapshot(current_stats_df, ec_id, day_end)

                        # 寫入 LTA 並計算分群
                        lta_result = self.write_to_special_lta_with_segments(
                            current_stats_df, ec_id, day_start
                        )

                        # 收集分群統計
                        if isinstance(lta_result, dict) and 'segment_stats' in lta_result:
                            result['daily_segment_stats'][taiwan_date_str] = lta_result['segment_stats']
                            result['segment_stats'].update(lta_result['segment_stats'])

                except Exception as e:
                    logger.error(f"處理電商 ID {ec_id} 日期 {day_start.date()} 時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    continue

                # 更新當前日期
                current_date = day_start

            # 設置修復天數
            result['days_repaired'] = days_to_repair
            result['users'] = len(current_stats_df)
            logger.info(f"已修復 {days_to_repair} 天的資料")

            # 保存最終結果
            self.save_local_output(current_stats_df, ec_id, "incremental")

        return result

    def process_full_data(self, ec_id: int, client) -> Dict[str, Any]:
        """從頭開始處理完整數據"""
        logger.info(f"從頭開始處理電商 ID {ec_id} 的完整數據")

        result = {
            'total_cost_usd': 0,
            'total_cost_twd': 0,
            'total_bytes_processed': 0,
            'users': 0,
            'segment_stats': {},
            'daily_segment_stats': {}
        }

        try:
            # 使用現有的腳本功能來獲取完整數據
            from scripts.init_user_stats_for_ec_id import get_user_data_from_bigquery_simple, process_raw_data_with_progress

            # 從 BigQuery 獲取原始數據
            raw_data = get_user_data_from_bigquery_simple(
                ec_id=ec_id,
                batch_size=self.batch_size,
                max_rows=None,
                time_range=[self.start_time, self.end_time]
            )

            if raw_data is None or len(raw_data) == 0:
                logger.warning(f"電商 ID {ec_id} 沒有數據")
                return result

            logger.info(f"獲取到 {len(raw_data)} 筆原始數據")

            # 處理原始數據
            user_stats_df = process_raw_data_with_progress(raw_data, ec_id)

            if user_stats_df.empty:
                logger.warning(f"處理後的用戶統計為空")
                return result

            result['users'] = len(user_stats_df)
            logger.info(f"處理得到 {len(user_stats_df)} 筆用戶統計")

            # 修復 _days 欄位的 None 值問題
            user_stats_df = self.fix_days_columns(user_stats_df)

            # 保存本地輸出
            self.save_local_output(user_stats_df, ec_id, "full")

            # 寫入 LTA 並計算分群
            lta_result = self.write_to_special_lta_with_segments(user_stats_df, ec_id, self.end_time)

            # 收集分群統計
            if isinstance(lta_result, dict) and 'segment_stats' in lta_result:
                result['segment_stats'] = lta_result['segment_stats']

            # 儲存快照
            snapshot_result = self.save_user_stats_snapshot(user_stats_df, ec_id)

        except Exception as e:
            logger.error(f"處理完整數據時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            raise

        return result

    def save_user_stats_snapshot(self, user_stats_df: pd.DataFrame, ec_id: int,
                                current_date: Optional[datetime] = None) -> Dict[str, Any]:
        """儲存用戶統計快照"""
        if current_date is None:
            current_date = self.end_time

        logger.info(f"儲存快照，EC ID: {ec_id}，日期: {current_date.date()}")

        try:
            result = cloud_integration.save_user_stats_snapshot(
                user_stats_df,
                ec_id,
                current_date,
                skip_time_standardization=True,
                only_required_columns=True
            )
            return result
        except Exception as e:
            logger.error(f"儲存快照失敗: {str(e)}")
            return {"success": False, "error": str(e)}

    def write_to_special_lta(self, user_stats_df: pd.DataFrame, ec_id: int) -> Dict[str, Any]:
        """寫入特殊 LTA 資料表"""
        try:
            return cloud_integration.write_to_special_lta(
                user_stats_df,
                ec_id,
                self.end_time,
                add_to_dxp=True,
                skip_time_standardization=True,
                only_required_columns=True,
                from_script=True
            )
        except Exception as e:
            logger.error(f"寫入 LTA 時發生錯誤: {str(e)}")
            return {"success": False, "error": str(e)}

    def write_to_special_lta_with_segments(self, user_stats_df: pd.DataFrame,
                                         ec_id: int, current_date: datetime) -> Dict[str, Any]:
        """寫入 LTA 並計算分群統計"""
        try:
            # 修復 user_stats_df 中的 _days 欄位
            user_stats_df = self.fix_days_columns(user_stats_df)

            # 計算動態分群（使用改進版本）
            segments = self.calculate_audience_segments_with_fallback(user_stats_df, ec_id)

            # 計算分群統計資訊
            segment_counts = {segment_id: len(permanents) for segment_id, permanents in segments.items()}
            segments_per_user = defaultdict(int)
            user_segment_counts = defaultdict(int)

            # 計算每個用戶屬於幾個分群
            for segment_id, permanents in segments.items():
                for permanent in permanents:
                    user_segment_counts[permanent] += 1

            # 統計每種分群數量的用戶數
            for user, count in user_segment_counts.items():
                segments_per_user[f"{count}_個分群"] += 1

            # 構建分群統計資料
            segment_stats = {
                'segment_counts': segment_counts,
                'segments_per_user': dict(segments_per_user),
                'total_users': len(user_segment_counts)
            }

            # 寫入 LTA
            lta_result = cloud_integration.write_to_special_lta(
                user_stats_df,
                ec_id,
                current_date,
                add_to_dxp=True,
                skip_time_standardization=True,
                only_required_columns=True,
                from_script=True
            )

            # 添加分群統計到結果中
            if isinstance(lta_result, dict):
                lta_result['segment_stats'] = segment_stats

            return lta_result

        except Exception as e:
            logger.error(f"寫入 LTA 並計算分群時發生錯誤: {str(e)}")
            return {"success": False, "error": str(e)}

    def save_local_output(self, user_stats_df: pd.DataFrame, ec_id: int, mode: str):
        """保存本地輸出"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ec_{ec_id}_user_stats_{mode}_{timestamp}.parquet"
            filepath = os.path.join(self.local_output, filename)

            # 標準化時間欄位格式
            user_stats_df = data_processing.standardize_datetime_columns(
                user_stats_df,
                skip_validation=False,
                only_required_columns=True
            )

            # 保存為 parquet 檔案
            user_stats_df.to_parquet(filepath, index=False)

            logger.info(f"✓ 本地輸出已保存: {filepath}")
            logger.info(f"  - 數據筆數: {len(user_stats_df)}")
            logger.info(f"  - 檔案大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")

            # 保存統計摘要
            self.save_data_summary(user_stats_df, ec_id, mode)

        except Exception as e:
            logger.error(f"保存本地輸出時發生錯誤: {str(e)}")

    def save_data_summary(self, user_stats_df: pd.DataFrame, ec_id: int, mode: str):
        """保存數據摘要"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_filename = f"ec_{ec_id}_summary_{mode}_{timestamp}.txt"
            summary_filepath = os.path.join(self.local_output, summary_filename)

            with open(summary_filepath, 'w', encoding='utf-8') as f:
                f.write(f"用戶統計數據摘要 - EC ID: {ec_id} ({mode})\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成時間: {datetime.now().isoformat()}\n")
                f.write(f"時間範圍: {self.start_time} 到 {self.end_time}\n")
                f.write(f"總用戶數: {len(user_stats_df)}\n")

                if 'total_sessions' in user_stats_df.columns:
                    total_sessions = user_stats_df['total_sessions'].sum()
                    avg_sessions = user_stats_df['total_sessions'].mean()
                    f.write(f"總會話數: {total_sessions}\n")
                    f.write(f"平均每用戶會話數: {avg_sessions:.2f}\n")

                if 'purchase_count' in user_stats_df.columns:
                    purchasers = user_stats_df[user_stats_df['purchase_count'] > 0]
                    purchase_rate = len(purchasers) / len(user_stats_df) * 100
                    f.write(f"購買用戶數: {len(purchasers)} ({purchase_rate:.2f}%)\n")
                    f.write(f"總購買次數: {user_stats_df['purchase_count'].sum()}\n")

                if 'total_purchase_amount' in user_stats_df.columns:
                    total_amount = user_stats_df['total_purchase_amount'].sum()
                    f.write(f"總購買金額: {total_amount:.2f}\n")

                # 時間分佈
                if 'first_interaction_time' in user_stats_df.columns:
                    valid_times = user_stats_df['first_interaction_time'].dropna()
                    if not valid_times.empty:
                        f.write(f"最早互動時間: {valid_times.min()}\n")
                        f.write(f"最晚互動時間: {user_stats_df['last_interaction_time'].dropna().max()}\n")

                # 數據品質檢查
                f.write("\n數據品質檢查:\n")
                for col in user_stats_df.columns:
                    null_count = user_stats_df[col].isnull().sum()
                    null_rate = null_count / len(user_stats_df) * 100
                    f.write(f"  {col}: {null_count} 個空值 ({null_rate:.2f}%)\n")

            logger.info(f"✓ 數據摘要已保存: {summary_filepath}")

        except Exception as e:
            logger.error(f"保存數據摘要時發生錯誤: {str(e)}")

    def run(self) -> Dict[str, Any]:
        """執行用戶統計處理"""
        start_time = time.time()
        logger.info("開始執行本地用戶統計處理")

        try:
            # 解析時間
            self.start_time = self.parse_time(self.start_time)
            self.end_time = self.parse_time(self.end_time)

            logger.info(f"解析後的時間範圍: {self.start_time} 到 {self.end_time}")

            # 選擇處理模式
            if self.use_bigquery_compute:
                results = self.calculate_user_stats_bigquery_mode()
            else:
                results = self.calculate_user_stats_traditional_mode()

            # 計算總處理時間
            total_time = time.time() - start_time
            results['total_processing_time'] = total_time

            logger.info(f"處理完成，總耗時: {total_time:.2f} 秒")
            logger.info(f"處理結果: {results}")

            return results

        except Exception as e:
            logger.error(f"執行過程中發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            return {
                'error': str(e),
                'total_processing_time': time.time() - start_time
            }

    def _get_product_segments_from_gcs(self, ec_id: int) -> Dict:
        """
        從 GCS 讀取最新的商品分群檔案
        """
        # ... (existing content of the function) ...
        return json.loads(blob.download_as_string())

    def _upload_snapshot_to_temp_table(self, df: pd.DataFrame, table_id: str, client) -> None:
        """
        將 DataFrame 上傳到 BigQuery 的臨時表，並強制使用正確的 Schema
        """
        project = client.project
        dataset_id = "temp" if project == "tagtoo-tracking" else "temp_tables"
        table_ref = client.dataset(dataset_id).table(table_id)

        # 1. 預處理：統一時間欄位格式
        df_copy = df.copy()

        # 找出所有時間相關欄位
        time_columns = [col for col in df_copy.columns
                       if 'time' in col or 'created_at' in col or 'updated_at' in col]

        logger.info(f"發現時間欄位: {time_columns}")

        # 統一轉換為 pandas datetime
        for col in time_columns:
            logger.info(f"處理時間欄位 {col}，原始型別: {df_copy[col].dtype}")

            if df_copy[col].dtype == 'object':
                # 字串格式 → pandas datetime
                logger.info(f"將字串欄位 {col} 轉換為 datetime")
                df_copy[col] = pd.to_datetime(df_copy[col], utc=True, errors='coerce')
            elif 'datetime64' in str(df_copy[col].dtype):
                # 已經是 datetime，確保時區正確
                if df_copy[col].dt.tz is None:
                    logger.info(f"為 {col} 設定 UTC 時區")
                    df_copy[col] = df_copy[col].dt.tz_localize('UTC')
                else:
                    logger.info(f"將 {col} 轉換為 UTC 時區")
                    df_copy[col] = df_copy[col].dt.tz_convert('UTC')

            logger.info(f"處理後 {col} 的型別: {df_copy[col].dtype}")

        # 2. 動態生成 BigQuery schema，根據實際處理後的資料型別
        schema = []
        dtype_mapping = {
            'int64': 'INTEGER',
            'float64': 'FLOAT',
            'bool': 'BOOLEAN',
            'object': 'STRING',
        }

        for col, dtype in df_copy.dtypes.items():
            if 'datetime64' in str(dtype):
                bq_type = 'TIMESTAMP'
            else:
                # 根據 pandas dtype 映射，未知類型預設為 STRING
                bq_type = dtype_mapping.get(str(dtype).lower(), 'STRING')
            schema.append(bigquery.SchemaField(col, bq_type))

        logger.info(f"為臨時表 {table_id} 生成的動態 Schema: {schema}")

        # 3. 上傳到 BigQuery
        job_config = bigquery.LoadJobConfig(
            schema=schema,
            write_disposition="WRITE_APPEND",
        )
        job = client.load_table_from_dataframe(
            df_copy, table_ref, job_config=job_config
        )
        job.result()
        logger.info(f"成功上傳 {len(df_copy)} 筆資料到臨時表: {table_id}")


def parse_args():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='本地執行用戶統計處理腳本')

    # 必要參數
    parser.add_argument('--ec-ids', type=str, required=True,
                        help='電商 ID 列表 (用逗號分隔，例如: 107,108,109)')
    parser.add_argument('--start-time', type=str, required=True,
                        help='開始時間 (ISO 格式，例如: 2025-05-27T16:00:00Z)')
    parser.add_argument('--end-time', type=str, required=True,
                        help='結束時間 (ISO 格式，例如: 2025-06-02T16:00:00Z)')

    # 可選參數
    parser.add_argument('--timezone', type=str, default='UTC',
                        help='時區 (預設: UTC)')
    parser.add_argument('--use-bigquery-compute', action='store_true',
                        help='使用 BigQuery 直接計算以避免記憶體問題')
    parser.add_argument('--skip-snapshot', action='store_true',
                        help='跳過快照載入')
    parser.add_argument('--skip-validation', action='store_true',
                        help='跳過數據驗證')
    parser.add_argument('--local-output', type=str, default='./local_output',
                        help='本地輸出目錄 (預設: ./local_output)')
    parser.add_argument('--batch-size', type=int, default=50000,
                        help='分批處理大小 (預設: 50000)')
    parser.add_argument('--max-repair-days', type=int, default=30,
                        help='最大修復天數 (預設: 30)')
    parser.add_argument('--workers', type=int, default=4,
                        help='並行處理工作進程數 (預設: 4)')
    parser.add_argument('--verbose', action='store_true',
                        help='詳細輸出模式')
    parser.add_argument('--quiet', action='store_true',
                        help='安靜模式')

    return parser.parse_args()


def main():
    """
    主函數 - 本地用戶統計處理腳本的入口點

    執行流程：
    1. 解析命令行參數
    2. 設定日誌級別和輸出格式
    3. 驗證和解析 EC ID 列表
    4. 創建處理器配置
    5. 實例化 LocalUserStatsProcessor
    6. 執行用戶統計處理
    7. 輸出處理結果和統計資訊

    錯誤處理：
    - 捕獲 KeyboardInterrupt（Ctrl+C）進行優雅退出
    - 捕獲所有其他異常並記錄詳細錯誤資訊
    - 返回適當的退出碼（0=成功，1=失敗）

    Returns:
        int: 程序退出碼（0 表示成功，1 表示失敗）
    """
    try:
                # 解析命令行參數
        args = parse_args()

        # 設置日誌級別（根據 --verbose 或 --quiet 參數）
        log_level = logging.INFO
        if args.verbose:
            log_level = logging.DEBUG
        elif args.quiet:
            log_level = logging.WARNING

        # 重新配置日誌 - 確保正確寫入檔案
        log_filename = f'local_user_stats_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

        # 清除現有的 handlers
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # 重新配置日誌
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_filename, mode='w', encoding='utf-8')
            ],
            force=True  # 強制重新配置
        )

        # 重新獲取 logger 實例以確保使用新配置
        global logger
        logger = logging.getLogger(__name__)

        logger.info(f"日誌檔案: {log_filename}")
        logger.info(f"日誌級別: {logging.getLevelName(log_level)}")

        # 解析 EC IDs
        try:
            ec_ids = [int(x.strip()) for x in args.ec_ids.split(',')]
        except ValueError as e:
            logger.error(f"無法解析 EC IDs: {args.ec_ids}")
            return 1

        logger.info(f"開始處理 EC IDs: {ec_ids}")
        logger.info(f"時間範圍: {args.start_time} 到 {args.end_time}")
        logger.info(f"使用 BigQuery 計算模式: {args.use_bigquery_compute}")

        # 創建配置
        config = {
            'ec_ids': ec_ids,
            'start_time': args.start_time,
            'end_time': args.end_time,
            'timezone': args.timezone,
            'use_bigquery_compute': args.use_bigquery_compute,
            'skip_snapshot': args.skip_snapshot,
            'skip_validation': args.skip_validation,
            'local_output': args.local_output,
            'batch_size': args.batch_size,
            'max_repair_days': args.max_repair_days,
            'workers': args.workers
        }

        # 創建處理器並執行（這是核心處理邏輯）
        processor = LocalUserStatsProcessor(config)
        results = processor.run()

        # 輸出結果
        if 'error' in results:
            logger.error(f"處理失敗: {results['error']}")
            return 1
        else:
            logger.info("處理成功完成!")
            logger.info(f"處理了 {results.get('users', 0)} 位用戶")
            logger.info(f"總處理時間: {results.get('total_processing_time', 0):.2f} 秒")

            if 'segment_stats' in results:
                logger.info("分群統計資料:")
                for key, value in results['segment_stats'].items():
                    logger.info(f"  {key}: {value}")

            return 0

    except KeyboardInterrupt:
        logger.info("接收到中斷信號，程序停止")
        return 1
    except Exception as e:
        logger.error(f"程序執行時發生未預期錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return 1


if __name__ == "__main__":
    sys.exit(main())