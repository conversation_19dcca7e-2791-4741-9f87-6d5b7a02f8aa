#!/usr/bin/env python3
"""
監控 LTA 用戶統計快照檔案的健康狀態
用於預防暫存檔案異常導致的分群計算問題
"""

import sys
import os
sys.path.append('.')

import pandas as pd
from google.cloud import storage
import logging
from datetime import datetime, timedelta
import tempfile

def check_snapshot_health(ec_id: int, days_to_check: int = 7) -> dict:
    """
    檢查指定 EC ID 的快照檔案健康狀態

    Args:
        ec_id: EC ID
        days_to_check: 檢查最近幾天的檔案

    Returns:
        健康狀態報告
    """
    logger = logging.getLogger(__name__)
    client = storage.Client()
    bucket = client.bucket('tagtoo-ml-workflow')

    health_report = {
        'ec_id': ec_id,
        'total_files_checked': 0,
        'healthy_files': 0,
        'unhealthy_files': [],
        'missing_files': [],
        'warnings': [],
        'errors': []
    }

    # 檢查最近幾天的檔案
    for i in range(days_to_check):
        date = datetime.now() - timedelta(days=i)
        date_str = date.strftime('%Y%m%d')
        blob_path = f'LTA/user_stats_snapshots/ec_{ec_id}/{date_str}.parquet'
        blob = bucket.blob(blob_path)

        health_report['total_files_checked'] += 1

        try:
            if not blob.exists():
                health_report['missing_files'].append(date_str)
                logger.warning(f"缺少檔案: {date_str}")
                continue

            # 檢查檔案大小
            blob.reload()
            file_size = blob.size

            # 下載並檢查內容
            with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
                blob.download_to_filename(temp_file.name)
                df = pd.read_parquet(temp_file.name)

                # 健康檢查標準 (根據 EC ID 動態調整)
                is_healthy = True
                issues = []

                # 根據 EC ID 設定合理的最小值
                if ec_id == 107:
                    min_file_size = 50 * 1024 * 1024  # 50MB
                    min_records = 1000000  # 100萬筆
                elif ec_id == 2980:
                    min_file_size = 10 * 1024 * 1024  # 10MB
                    min_records = 200000  # 20萬筆
                elif ec_id in [3819, 3820]:
                    min_file_size = 500 * 1024  # 500KB
                    min_records = 5000  # 5千筆
                else:
                    # 預設值
                    min_file_size = 1 * 1024 * 1024  # 1MB
                    min_records = 1000  # 1千筆

                # 1. 檔案大小檢查
                if file_size < min_file_size:
                    is_healthy = False
                    issues.append(f"檔案過小: {file_size:,} bytes (最小: {min_file_size:,})")

                # 2. 記錄數量檢查
                if len(df) < min_records:
                    is_healthy = False
                    issues.append(f"記錄數過少: {len(df):,} (最小: {min_records:,})")

                # 3. 必要欄位檢查
                required_fields = ['permanent', 'ec_id', 'last_interaction_time']
                missing_fields = [field for field in required_fields if field not in df.columns]
                if missing_fields:
                    is_healthy = False
                    issues.append(f"缺少必要欄位: {missing_fields}")

                # 4. EC ID 一致性檢查
                if 'ec_id' in df.columns:
                    unique_ec_ids = df['ec_id'].unique()
                    if len(unique_ec_ids) != 1 or unique_ec_ids[0] != ec_id:
                        is_healthy = False
                        issues.append(f"EC ID 不一致: {unique_ec_ids}")

                # 5. 數據新鮮度檢查 (updated_at 應該是最近的)
                if 'updated_at' in df.columns:
                    latest_update = pd.to_datetime(df['updated_at']).max()
                    days_old = (datetime.now() - latest_update.to_pydatetime().replace(tzinfo=None)).days
                    if days_old > 2:
                        health_report['warnings'].append(f"{date_str}: 數據過舊 ({days_old} 天)")

                if is_healthy:
                    health_report['healthy_files'] += 1
                    logger.info(f"✓ {date_str}: 健康 ({len(df):,} 記錄, {file_size:,} bytes)")
                else:
                    health_report['unhealthy_files'].append({
                        'date': date_str,
                        'issues': issues,
                        'size': file_size,
                        'records': len(df)
                    })
                    logger.error(f"✗ {date_str}: 不健康 - {'; '.join(issues)}")

                # 清理臨時檔案
                os.unlink(temp_file.name)

        except Exception as e:
            health_report['errors'].append(f"{date_str}: {str(e)}")
            logger.error(f"檢查檔案 {date_str} 時發生錯誤: {str(e)}")

    return health_report

def generate_health_summary(health_report: dict) -> str:
    """生成健康狀態摘要"""
    summary = []
    summary.append(f"=== EC ID {health_report['ec_id']} 快照健康檢查報告 ===")
    summary.append(f"檢查檔案數: {health_report['total_files_checked']}")
    summary.append(f"健康檔案數: {health_report['healthy_files']}")
    summary.append(f"不健康檔案數: {len(health_report['unhealthy_files'])}")
    summary.append(f"缺少檔案數: {len(health_report['missing_files'])}")

    if health_report['unhealthy_files']:
        summary.append("\n不健康檔案詳情:")
        for file_info in health_report['unhealthy_files']:
            summary.append(f"  {file_info['date']}: {'; '.join(file_info['issues'])}")

    if health_report['missing_files']:
        summary.append(f"\n缺少檔案: {', '.join(health_report['missing_files'])}")

    if health_report['warnings']:
        summary.append(f"\n警告:")
        for warning in health_report['warnings']:
            summary.append(f"  {warning}")

    if health_report['errors']:
        summary.append(f"\n錯誤:")
        for error in health_report['errors']:
            summary.append(f"  {error}")

    # 健康度評分
    total_expected = health_report['total_files_checked'] - len(health_report['missing_files'])
    if total_expected > 0:
        health_score = health_report['healthy_files'] / total_expected * 100
        summary.append(f"\n健康度評分: {health_score:.1f}%")

        if health_score < 80:
            summary.append("⚠️  健康度低於 80%，建議立即檢查！")
        elif health_score < 95:
            summary.append("⚠️  健康度低於 95%，建議關注。")
        else:
            summary.append("✅ 健康狀態良好。")

    return '\n'.join(summary)

def main():
    """主函數"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # 檢查所有活躍的 EC ID
    ec_ids = [107, 2980, 3819, 3820]

    for ec_id in ec_ids:
        logger.info(f"開始檢查 EC ID {ec_id} 的快照健康狀態...")

        try:
            health_report = check_snapshot_health(ec_id, days_to_check=7)
            summary = generate_health_summary(health_report)
            print(summary)
            print("\n" + "="*60 + "\n")

        except Exception as e:
            logger.error(f"檢查 EC ID {ec_id} 時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")

if __name__ == "__main__":
    main()
