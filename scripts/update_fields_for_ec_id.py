#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新特定 EC ID 的特定欄位資料

此腳本用於更新現有 EC ID 的特定欄位資料，例如新增的 first_add_to_cart_time, last_add_to_cart_time,
first_view_item_time, last_view_item_time 等欄位。

用法:
    python -m scripts.update_fields_for_ec_id --ec-id=1234 --fields=first_add_to_cart_time,last_add_to_cart_time
"""

import argparse
import logging
import sys
import time
from datetime import datetime, timedelta
from typing import List, Optional

import pandas as pd
from google.cloud import bigquery

# 添加專案根目錄到 Python 路徑以確保模組可用性
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.cloud_integration import BigQueryClient, save_user_stats_snapshot
from src.core.data_processing import standardize_datetime_columns
from src.utils.logging_setup import configure_logging

# 設置日誌
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='更新特定 EC ID 的特定欄位資料')
    parser.add_argument('--ec-id', type=int, required=True, help='電商 ID')
    parser.add_argument('--fields', type=str, required=True, help='要更新的欄位，以逗號分隔')
    parser.add_argument('--start-date', type=str, required=True, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, required=True, help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--dry-run', action='store_true', help='僅執行查詢估算，不實際更新資料')
    parser.add_argument('--max-rows', type=int, help='最大處理資料列數')
    parser.add_argument('--use-bigquery-compute', action='store_true', help='使用 BigQuery 直接計算統計資料')
    parser.add_argument('--update-gcs', action='store_true', help='更新 GCS 快照')
    parser.add_argument('--log-level', type=str, default='INFO', help='日誌級別 (DEBUG, INFO, WARNING, ERROR)')
    return parser.parse_args()

def validate_fields(fields: List[str]) -> bool:
    """驗證欄位名稱是否有效"""
    valid_fields = [
        'first_interaction_time', 'last_interaction_time',
        'first_purchase_time', 'last_purchase_time',
        'first_add_to_cart_time', 'last_add_to_cart_time',
        'first_view_item_time', 'last_view_item_time'
    ]
    for field in fields:
        if field not in valid_fields:
            logger.error(f"無效的欄位名稱: {field}")
            return False
    return True

def build_update_query(ec_id: int, fields: List[str], start_date: str, end_date: str, dry_run: bool = False) -> str:
    """構建更新欄位的 BigQuery 查詢

    Args:
        ec_id: 電商 ID
        fields: 要更新的欄位列表
        start_date: 開始日期
        end_date: 結束日期
        dry_run: 是否為 dry run 模式，如果是則返回 SELECT 查詢而不是 UPDATE 查詢

    Returns:
        str: BigQuery 查詢字符串
    """
    # 構建 CASE WHEN 子句
    case_clauses = []
    for field in fields:
        if field == 'first_add_to_cart_time':
            case_clauses.append("""
            CASE
                WHEN stats.first_add_to_cart_time IS NULL THEN
                    (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                     WHERE ec_id = {ec_id} AND permanent = stats.permanent
                     AND event.name = 'add_to_cart'
                     AND event_time BETWEEN '{start_date}' AND '{end_date}')
                ELSE stats.first_add_to_cart_time
            END AS first_add_to_cart_time
            """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
        elif field == 'last_add_to_cart_time':
            case_clauses.append("""
            CASE
                WHEN TRUE THEN
                    GREATEST(stats.last_add_to_cart_time,
                            (SELECT MAX(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                             WHERE ec_id = {ec_id} AND permanent = stats.permanent
                             AND event.name = 'add_to_cart'
                             AND event_time BETWEEN '{start_date}' AND '{end_date}'))
                ELSE stats.last_add_to_cart_time
            END AS last_add_to_cart_time
            """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
        elif field == 'first_view_item_time':
            case_clauses.append("""
            CASE
                WHEN stats.first_view_item_time IS NULL THEN
                    (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                     WHERE ec_id = {ec_id} AND permanent = stats.permanent
                     AND event.name = 'view_item'
                     AND event_time BETWEEN '{start_date}' AND '{end_date}')
                ELSE stats.first_view_item_time
            END AS first_view_item_time
            """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
        elif field == 'last_view_item_time':
            case_clauses.append("""
            CASE
                WHEN TRUE THEN
                    GREATEST(stats.last_view_item_time,
                            (SELECT MAX(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                             WHERE ec_id = {ec_id} AND permanent = stats.permanent
                             AND event.name = 'view_item'
                             AND event_time BETWEEN '{start_date}' AND '{end_date}'))
                ELSE stats.last_view_item_time
            END AS last_view_item_time
            """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))

    # 如果是 dry run 模式，返回 SELECT 查詢而不是 UPDATE 查詢
    if dry_run:
        # 構建 SELECT 查詢，用於估算成本
        # 為每個欄位創建單獨的 SELECT 子句
        select_clauses = []
        for field in fields:
            if field == 'first_add_to_cart_time':
                select_clauses.append("""
                CASE
                    WHEN stats.first_add_to_cart_time IS NULL THEN
                        (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                         WHERE ec_id = {ec_id} AND permanent = stats.permanent
                         AND event.name = 'add_to_cart'
                         AND event_time BETWEEN '{start_date}' AND '{end_date}')
                    ELSE stats.first_add_to_cart_time
                END AS first_add_to_cart_time
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'last_add_to_cart_time':
                select_clauses.append("""
                CASE
                    WHEN stats.last_add_to_cart_time IS NULL THEN
                        (
                            SELECT
                                CASE
                                    WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_add_to_cart_time 相同的時間
                                    ELSE MAX(event_time)
                                END
                            FROM `tagtoo-tracking.event_prod.tagtoo_event`
                            WHERE ec_id = {ec_id} AND permanent = stats.permanent
                            AND event.name = 'add_to_cart'
                            AND event_time BETWEEN '{start_date}' AND '{end_date}'
                        )
                    ELSE
                        GREATEST(stats.last_add_to_cart_time,
                                IFNULL((
                                    SELECT
                                        CASE
                                            WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_add_to_cart_time 相同的時間
                                            ELSE MAX(event_time)
                                        END
                                    FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                    WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                    AND event.name = 'add_to_cart'
                                    AND event_time BETWEEN '{start_date}' AND '{end_date}'
                                ), stats.last_add_to_cart_time))
                END AS last_add_to_cart_time
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'first_view_item_time':
                select_clauses.append("""
                CASE
                    WHEN stats.first_view_item_time IS NULL THEN
                        (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                         WHERE ec_id = {ec_id} AND permanent = stats.permanent
                         AND event.name = 'view_item'
                         AND event_time BETWEEN '{start_date}' AND '{end_date}')
                    ELSE stats.first_view_item_time
                END AS first_view_item_time
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'last_view_item_time':
                select_clauses.append("""
                CASE
                    WHEN stats.last_view_item_time IS NULL THEN
                        (
                            SELECT
                                CASE
                                    WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_view_item_time 相同的時間
                                    ELSE MAX(event_time)
                                END
                            FROM `tagtoo-tracking.event_prod.tagtoo_event`
                            WHERE ec_id = {ec_id} AND permanent = stats.permanent
                            AND event.name = 'view_item'
                            AND event_time BETWEEN '{start_date}' AND '{end_date}'
                        )
                    ELSE
                        GREATEST(stats.last_view_item_time,
                                IFNULL((
                                    SELECT
                                        CASE
                                            WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_view_item_time 相同的時間
                                            ELSE MAX(event_time)
                                        END
                                    FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                    WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                    AND event.name = 'view_item'
                                    AND event_time BETWEEN '{start_date}' AND '{end_date}'
                                ), stats.last_view_item_time))
                END AS last_view_item_time
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))

        # 構建完整查詢
        query = """
        SELECT
            stats.ec_id,
            stats.permanent,
            {field_selects}
        FROM `tagtoo-tracking.event_prod.user_stats` stats
        WHERE
            stats.ec_id = {ec_id}
        LIMIT 1000
        """.format(
            field_selects=",\n            ".join(select_clauses),
            ec_id=ec_id
        )
    else:
        # 構建 UPDATE 查詢，用於實際更新
        # 為每個欄位創建單獨的 SET 語句，而不是使用 CASE 表達式作為欄位名稱
        set_clauses = []
        for field in fields:
            if field == 'first_add_to_cart_time':
                set_clauses.append("""
                first_add_to_cart_time = (
                    CASE
                        WHEN stats.first_add_to_cart_time IS NULL THEN
                            (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                             WHERE ec_id = {ec_id} AND permanent = stats.permanent
                             AND event.name = 'add_to_cart'
                             AND event_time BETWEEN '{start_date}' AND '{end_date}')
                        ELSE stats.first_add_to_cart_time
                    END
                )
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'last_add_to_cart_time':
                set_clauses.append("""
                last_add_to_cart_time = (
                    CASE
                        WHEN stats.last_add_to_cart_time IS NULL THEN
                            (
                                SELECT
                                    CASE
                                        WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_add_to_cart_time 相同的時間
                                        ELSE MAX(event_time)
                                    END
                                FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                AND event.name = 'add_to_cart'
                                AND event_time BETWEEN '{start_date}' AND '{end_date}'
                            )
                        ELSE
                            GREATEST(stats.last_add_to_cart_time,
                                    IFNULL((
                                        SELECT
                                            CASE
                                                WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_add_to_cart_time 相同的時間
                                                ELSE MAX(event_time)
                                            END
                                        FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                        WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                        AND event.name = 'add_to_cart'
                                        AND event_time BETWEEN '{start_date}' AND '{end_date}'
                                    ), stats.last_add_to_cart_time))
                    END
                )
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'first_view_item_time':
                set_clauses.append("""
                first_view_item_time = (
                    CASE
                        WHEN stats.first_view_item_time IS NULL THEN
                            (SELECT MIN(event_time) FROM `tagtoo-tracking.event_prod.tagtoo_event`
                             WHERE ec_id = {ec_id} AND permanent = stats.permanent
                             AND event.name = 'view_item'
                             AND event_time BETWEEN '{start_date}' AND '{end_date}')
                        ELSE stats.first_view_item_time
                    END
                )
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))
            elif field == 'last_view_item_time':
                set_clauses.append("""
                last_view_item_time = (
                    CASE
                        WHEN stats.last_view_item_time IS NULL THEN
                            (
                                SELECT
                                    CASE
                                        WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_view_item_time 相同的時間
                                        ELSE MAX(event_time)
                                    END
                                FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                AND event.name = 'view_item'
                                AND event_time BETWEEN '{start_date}' AND '{end_date}'
                            )
                        ELSE
                            GREATEST(stats.last_view_item_time,
                                    IFNULL((
                                        SELECT
                                            CASE
                                                WHEN COUNT(*) = 1 THEN MIN(event_time) -- 如果只有一筆記錄，使用與 first_view_item_time 相同的時間
                                                ELSE MAX(event_time)
                                            END
                                        FROM `tagtoo-tracking.event_prod.tagtoo_event`
                                        WHERE ec_id = {ec_id} AND permanent = stats.permanent
                                        AND event.name = 'view_item'
                                        AND event_time BETWEEN '{start_date}' AND '{end_date}'
                                    ), stats.last_view_item_time))
                    END
                )
                """.format(ec_id=ec_id, start_date=start_date, end_date=end_date))

        # 添加 updated_at 欄位
        set_clauses.append("updated_at = CURRENT_TIMESTAMP()")

        # 構建完整查詢
        query = """
        UPDATE `tagtoo-tracking.event_prod.user_stats` stats
        SET
            {field_updates}
        WHERE
            ec_id = {ec_id}
        """.format(
            field_updates=",\n            ".join(set_clauses),
            ec_id=ec_id
        )

    return query

def main():
    """主函數"""
    args = parse_args()

    # 設置日誌級別
    configure_logging(args.log_level)

    # 解析欄位列表
    fields = args.fields.split(',')

    # 驗證欄位
    if not validate_fields(fields):
        sys.exit(1)

    # 解析日期
    try:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        # 將結束日期設為當天的 23:59:59
        end_date = end_date.replace(hour=23, minute=59, second=59)
    except ValueError as e:
        logger.error(f"日期格式錯誤: {e}")
        sys.exit(1)

    # 初始化 BigQuery 處理器
    bq_handler = BigQueryClient()

    # 構建更新查詢
    update_query = build_update_query(
        args.ec_id,
        fields,
        start_date.strftime('%Y-%m-%d'),
        end_date.strftime('%Y-%m-%d %H:%M:%S'),
        args.dry_run
    )

    # 顯示查詢
    logger.info("更新查詢:")
    logger.info(update_query)

    # 估算查詢成本
    cost_info = bq_handler.estimate_query_cost(update_query)

    # 計算新台幣成本 (1 USD 約等於 31 TWD)
    usd_cost = cost_info.get('estimated_cost_usd', 0)
    twd_cost = usd_cost * 31
    cost_info['estimated_cost_twd'] = twd_cost

    # 顯示更詳細的成本資訊
    logger.info(f"估算查詢成本: {cost_info['bytes_processed']} bytes ({cost_info['tb_processed']:.6f} TB)")
    logger.info(f"估算費用: ${usd_cost:.6f} USD (約 NT${twd_cost:.2f})")

    # 如果是 dry run 模式，則不執行實際更新
    if args.dry_run:
        logger.info("Dry run 模式，跳過實際更新")
        return

    # 執行更新
    start_time = time.time()
    logger.info(f"開始更新 EC ID {args.ec_id} 的欄位: {', '.join(fields)}")

    try:
        # 執行查詢
        query_job = bq_handler.client.query(update_query)
        query_job.result()  # 等待查詢完成

        # 獲取更新的行數
        total_rows = query_job.num_dml_affected_rows
        logger.info(f"成功更新 {total_rows} 行資料")

        # 記錄執行時間
        execution_time = time.time() - start_time
        logger.info(f"更新完成，耗時: {execution_time:.2f} 秒")

        # 如果需要更新 GCS 快照
        if args.update_gcs:
            logger.info(f"開始更新 EC ID {args.ec_id} 的 GCS 快照...")

            # 從 BigQuery 讀取最新資料
            query = f"""
            SELECT * FROM `tagtoo-tracking.event_prod.user_stats`
            WHERE ec_id = {args.ec_id}
            """
            logger.info(f"從 BigQuery 讀取最新資料: {query}")

            try:
                # 執行查詢
                df = bq_handler.client.query(query).result().to_dataframe()
                logger.info(f"成功讀取 {len(df)} 筆資料")

                # 使用結束日期作為快照日期
                end_date_obj = datetime.strptime(args.end_date, '%Y-%m-%d')
                end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)

                # 上傳到 GCS
                snapshot_path = save_user_stats_snapshot(df, args.ec_id, end_date_obj)
                logger.info(f"成功更新 GCS 快照: {snapshot_path}")
            except Exception as e:
                logger.error(f"更新 GCS 快照失敗: {e}")

    except Exception as e:
        logger.error(f"更新失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
