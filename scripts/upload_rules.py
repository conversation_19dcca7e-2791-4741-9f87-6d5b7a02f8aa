import os
import requests
from datetime import datetime, timezone

from google.cloud import storage
from src.utils import logging_setup

logger = logging_setup.configure_logging()

def upload_audience_rules():
    """安全上傳 audience_rules.json 到 GCS"""
    try:
        client = storage.Client()
        bucket_name = os.getenv('GCS_BUCKET', 'tagtoo-ml-workflow')
        bucket = client.bucket(bucket_name)

        # 本地檔案檢查
        local_path = os.path.join(os.path.dirname(__file__), 'audience_rules.json')
        if not os.path.exists(local_path):
            raise FileNotFoundError(f"本地檔案不存在: {local_path}")

        # 建立 blob 並上傳
        rules_path = 'LTA/user_stats_configs/audience_rules.json'
        blob = bucket.blob(rules_path)
        blob.metadata = {
            'deploy_time': datetime.now(timezone.utc).isoformat(),
            'source': 'automated-uploader'
        }
        blob.upload_from_filename(local_path)

        logger.info(f"檔案已上傳至 gs://{bucket.name}/{blob.name}")

        # 刷新 CDN 快取
        if 'CDN_PURGE_URL' in os.environ:
            requests.post(os.environ['CDN_PURGE_URL'], timeout=10)

    except Exception as e:
        logger.error(f"上傳失敗: {str(e)}")
        raise

def verify_upload():
    client = storage.Client()
    bucket_name = 'tagtoo-ml-workflow'
    bucket = client.get_bucket(bucket_name)
    rules_path = 'LTA/user_stats_configs/audience_rules.json'
    blob = bucket.blob(rules_path)

    if not blob.exists():
        raise FileNotFoundError("檔案不存在於 GCS")

    blob.reload()

    print(f"最後更新時間: {blob.updated.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"檔案大小: {blob.size / 1024:.2f} KB" if blob.size else "檔案大小未知")
    print(f"MD5 校驗碼: {blob.md5_hash}")

if __name__ == '__main__':
    upload_audience_rules()
    verify_upload()
