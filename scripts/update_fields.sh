#!/bin/bash
# 更新特定 EC ID 的特定欄位資料
# 用法: ./update_fields.sh --ec-ids=1234,5678 --fields=first_add_to_cart_time,last_add_to_cart_time --start-date=2023-01-01 --end-date=2023-12-31

set -e

# 預設值
EC_IDS=""
FIELDS=""
START_DATE=""
END_DATE=""
DRY_RUN=false
MAX_ROWS=""
USE_BQ_COMPUTE=true  # 預設使用 BigQuery 計算
UPDATE_GCS=false     # 預設不更新 GCS 快照
LOG_LEVEL="INFO"

# 解析命令行參數
for i in "$@"; do
  case $i in
    --ec-ids=*)
      EC_IDS="${i#*=}"
      shift
      ;;
    --fields=*)
      FIELDS="${i#*=}"
      shift
      ;;
    --start-date=*)
      START_DATE="${i#*=}"
      shift
      ;;
    --end-date=*)
      END_DATE="${i#*=}"
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --max-rows=*)
      MAX_ROWS="${i#*=}"
      shift
      ;;
    --use-bigquery-compute)
      USE_BQ_COMPUTE=true
      shift
      ;;
    --no-use-bigquery-compute)
      USE_BQ_COMPUTE=false
      shift
      ;;
    --update-gcs)
      UPDATE_GCS=true
      shift
      ;;
    --log-level=*)
      LOG_LEVEL="${i#*=}"
      shift
      ;;
    *)
      # 未知選項
      ;;
  esac
done

# 檢查必要參數
if [ -z "$EC_IDS" ]; then
  echo "錯誤: 必須指定 EC ID (--ec-ids=1234,5678)"
  exit 1
fi

if [ -z "$FIELDS" ]; then
  echo "錯誤: 必須指定要更新的欄位 (--fields=first_add_to_cart_time,last_add_to_cart_time)"
  exit 1
fi

# 如果未指定日期範圍，使用過去一年的資料
if [ -z "$START_DATE" ]; then
  START_DATE=$(date -v-2y +%Y-%m-%d)
  echo "未指定開始日期，使用兩年前: $START_DATE"
fi

if [ -z "$END_DATE" ]; then
  END_DATE=$(date +%Y-%m-%d)
  echo "未指定結束日期，使用今天: $END_DATE"
fi

# 顯示執行參數
echo "更新欄位設定:"
echo "EC IDs: $EC_IDS"
echo "欄位: $FIELDS"
echo "日期範圍: $START_DATE 至 $END_DATE"
echo "Dry Run: $DRY_RUN"
if [ -n "$MAX_ROWS" ]; then
  echo "最大資料列數: $MAX_ROWS"
fi
echo "使用 BigQuery 計算: $USE_BQ_COMPUTE"
echo "更新 GCS 快照: $UPDATE_GCS"
echo "日誌級別: $LOG_LEVEL"

# 確認執行
if [ "$DRY_RUN" = false ]; then
  read -p "確定要執行更新操作嗎? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
  fi
fi

# 轉換 EC IDs 為陣列
IFS=',' read -ra EC_ID_ARRAY <<< "$EC_IDS"

# 為每個 EC ID 執行更新
for EC_ID in "${EC_ID_ARRAY[@]}"; do
  echo "正在處理 EC ID: $EC_ID"

  # 構建 Python 命令
  PYTHON_CMD="python -m scripts.update_fields_for_ec_id \
    --ec-id=$EC_ID \
    --fields=$FIELDS \
    --start-date=$START_DATE \
    --end-date=$END_DATE"

  if [ "$DRY_RUN" = true ]; then
    PYTHON_CMD="$PYTHON_CMD --dry-run"
  fi

  if [ -n "$MAX_ROWS" ]; then
    PYTHON_CMD="$PYTHON_CMD --max-rows=$MAX_ROWS"
  fi

  if [ "$USE_BQ_COMPUTE" = true ]; then
    PYTHON_CMD="$PYTHON_CMD --use-bigquery-compute"
  fi

  if [ "$UPDATE_GCS" = true ]; then
    PYTHON_CMD="$PYTHON_CMD --update-gcs"
  fi

  PYTHON_CMD="$PYTHON_CMD --log-level=$LOG_LEVEL"

  # 執行命令
  echo "執行命令: $PYTHON_CMD"
  eval $PYTHON_CMD

  # 檢查執行結果
  if [ $? -ne 0 ]; then
    echo "錯誤: EC ID $EC_ID 的欄位更新失敗"
    exit 1
  fi
done

echo "本次腳本執行完畢"
