#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
初始化新加入的 EC ID 的用戶統計腳本

此腳本用於初始化特定 EC ID 的用戶統計，會從 BigQuery 中擷取該 EC ID 從 2022-01-01 開始
到查詢當下的前一天00:00:00為止的所有資料，計算出用戶統計資料並寫入 BigQuery 和 GCS。

使用方式:
    python scripts/init_user_stats_for_ec_id.py --ec-id 123

選項:
    --ec-id [EC_ID]: 必填，電商 ID
    --start-date [DATE]: 選填，開始日期，默認為2022-01-01，格式為YYYY-MM-DD
    --end-date [DATE]: 選填，結束日期，預設為查詢當下的前一天，格式為YYYY-MM-DD
    --skip-bigquery: 選填，跳過寫入 BigQuery
    --skip-gcs: 選填，跳過寫入 GCS
    --debug: 選填，啟用 debug 模式，相當於同時指定 --skip-bigquery 和 --skip-gcs
    --local-parquet: 選填，產生本地 parquet 檔案
    --local-path: 選填，本地 parquet 檔案的儲存路徑，默認為 ./local_data
    --output-filename: 選填，指定輸出的 parquet 檔案名稱
    --use-bigquery-compute: 選填，直接使用 BigQuery 計算用戶統計數據，而不是在本地處理原始數據
"""

import argparse
import os
import sys
import time
import logging
import multiprocessing as mp
import threading
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from pathlib import Path
import pytz
import concurrent.futures
# 保留 BigQuery 相關導入
from google.cloud import bigquery, storage
import pandas_gbq
import random
from tqdm import tqdm

# 將專案根目錄加入 sys.path
ROOT_DIR = Path(__file__).parent.parent.absolute()
sys.path.append(str(ROOT_DIR))

from src.utils import logging_setup
from src.utils.time_utils import get_taiwan_date_str
from src.core.cloud_integration import BigQueryClient, StorageManager
from src.core.data_processing import standardize_datetime_columns, validate_snapshot

# 設定 logger
logger = logging_setup.configure_logging()

# 設置 pandas 顯示選項，以便在控制台中顯示更多信息
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
pd.set_option('display.width', 1000)

def parse_args():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='生成特定電商的用戶統計數據')

    # 必要參數
    parser.add_argument('--ec-id', type=int, help='電商 ID')
    parser.add_argument('--parquet-path', type=str, help='輸入的 parquet 文件路徑')

    # 處理方式相關
    parser.add_argument('--disable-parallel', action='store_true', help='禁用並行處理')
    parser.add_argument('--use-bigquery-compute', action='store_true',
                        help='直接使用 BigQuery 計算用戶統計數據而非導出數據到本地處理')
    parser.add_argument('--force-query', action='store_true',
                        help='自動確認執行高成本查詢，跳過費用確認提示')

    # 輸出控制
    parser.add_argument('--output-path', type=str, help='輸出文件路徑')
    parser.add_argument('--output-filename', type=str, help='輸出文件名')
    parser.add_argument('--local-parquet', action='store_true', help='是否輸出本地 parquet 檔案 (legacy 格式)')
    parser.add_argument('--local-path', type=str, help='本地 parquet 檔案路徑')

    # 上傳控制
    parser.add_argument('--skip-bigquery', action='store_true', default=True,
                        help='跳過上傳到 BigQuery (默認：True)')
    parser.add_argument('--skip-gcs', action='store_true', default=True,
                        help='跳過上傳到 GCS (默認：True)')
    parser.add_argument('--skip-local', action='store_true', help='跳過輸出本地文件')

    # 上傳啟用（必須明確指定）
    parser.add_argument('--enable-bigquery-upload', action='store_false', dest='skip_bigquery',
                        help='啟用上傳到 BigQuery (默認：不啟用)')
    parser.add_argument('--enable-gcs-upload', action='store_false', dest='skip_gcs',
                        help='啟用上傳到 GCS (默認：不啟用)')

    # 處理相關配置
    parser.add_argument('--workers', type=int, default=8, help='並行處理的工作進程數')
    parser.add_argument('--batch-size', type=int, default=50000, help='處理批次大小')
    parser.add_argument('--max-rows', type=int, help='限制處理的最大行數')

    # 時間範圍
    parser.add_argument('--start-date', type=str, help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='結束日期 (YYYY-MM-DD)')

    # 日誌控制
    parser.add_argument('--verbose', action='store_true', help='詳細模式')
    parser.add_argument('--quiet', action='store_true', help='安靜模式')
    parser.add_argument('--debug', action='store_true', help='調試模式')

    # 解析參數
    args = parser.parse_args()

    return args

def get_custom_time_range(start_date_str, end_date_str=None):
    """根據指定的開始日期和結束日期獲取時間範圍"""
    # 台灣時區
    taipei_tz = pytz.timezone('Asia/Taipei')

    # 解析自定義開始日期 (台灣時間)
    try:
        start_date_parts = start_date_str.split('-')
        year, month, day = map(int, start_date_parts)
        start_date_tw = taipei_tz.localize(datetime(year, month, day, 0, 0, 0))
    except (ValueError, IndexError) as e:
        logger.error(f"無法解析開始日期: {start_date_str}，應為 YYYY-MM-DD 格式: {str(e)}")
        sys.exit(1)

    # 解析結束日期
    if end_date_str:
        try:
            end_date_parts = end_date_str.split('-')
            year, month, day = map(int, end_date_parts)
            end_date_tw = taipei_tz.localize(datetime(year, month, day, 0, 0, 0))
        except (ValueError, IndexError) as e:
            logger.error(f"無法解析結束日期: {end_date_str}，應為 YYYY-MM-DD 格式: {str(e)}")
            sys.exit(1)
    else:
        # 當前台灣時間
        now_tw = datetime.now(taipei_tz)
        # 前一天 00:00:00（台灣時間）
        end_date_tw = now_tw.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)

    # 轉換為 UTC 時間
    start_date_utc = start_date_tw.astimezone(timezone.utc)
    end_date_utc = end_date_tw.astimezone(timezone.utc)

    logger.info(f"時間範圍：從 {start_date_tw.strftime('%Y-%m-%d %H:%M:%S %Z')} [台灣時間]")
    logger.info(f"      到 {end_date_tw.strftime('%Y-%m-%d %H:%M:%S %Z')} [台灣時間]")
    logger.info(f"查詢將使用 UTC 時間：從 {start_date_utc.isoformat()} 到 {end_date_utc.isoformat()}")

    return start_date_utc, end_date_utc

def save_to_bigquery(user_stats_df, ec_id, skip_bigquery=False):
    """將用戶統計資料寫入 BigQuery"""
    logger.info(f"開始將 {len(user_stats_df)} 筆用戶統計資料寫入 BigQuery...")

    if skip_bigquery:
        logger.info("[跳過] 不寫入 BigQuery")
        return True

    bq_client = BigQueryClient()

    # 獲取目前時間（UTC）
    current_date = datetime.now(timezone.utc)

    try:
        logger.info("準備寫入 BigQuery user_stats 表...")
        # 更新 BigQuery 用戶統計表
        bq_client.update_user_stats_table(
            user_stats_df=user_stats_df,
            ec_id=ec_id,
            current_date=current_date,
            skip_time_standardization=True,  # 已經在前面標準化過
            only_required_columns=True
        )
        logger.info("✓ 成功寫入 BigQuery user_stats 表")
        return True
    except Exception as e:
        logger.error(f"✗ 寫入 BigQuery 失敗: {str(e)}")
        return False

def save_to_gcs(user_stats_df, ec_id, skip_gcs=False, end_date=None):
    """將用戶統計資料保存為 parquet 檔案並上傳到 GCS

    Args:
        user_stats_df: 用戶統計資料
        ec_id: 電商 ID
        skip_gcs: 是否跳過上傳到 GCS
        end_date: 查詢的結束日期，用於檔名（如果為 None，則使用當前時間）
    """
    logger.info(f"開始將 {len(user_stats_df)} 筆用戶統計資料保存為 parquet 檔案並上傳到 GCS...")

    if skip_gcs:
        logger.info("[跳過] 不上傳到 GCS")
        return True

    # 獲取目前時間（UTC）或使用查詢的結束日期
    if end_date is not None and isinstance(end_date, datetime):
        current_date = end_date
        logger.info(f"使用查詢結束日期作為檔名: {current_date.strftime('%Y-%m-%d')}")
    else:
        current_date = datetime.now(timezone.utc)
        logger.info(f"使用當前時間作為檔名: {current_date.strftime('%Y-%m-%d')}")

    # 在保存前進行詳細的資料檢查
    try:
        logger.info("進行資料檢查...")
        # 檢查必要欄位是否存在
        required_columns = ['ec_id', 'permanent', 'first_interaction_time', 'last_interaction_time',
                          'total_sessions', 'purchase_count', 'total_purchase_amount']
        missing_columns = [col for col in required_columns if col not in user_stats_df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要欄位: {', '.join(missing_columns)}")
        logger.info("  • 必要欄位檢查通過")

        # 檢查時間欄位的型別和時區
        logger.info("檢查時間欄位...")
        time_columns = ['first_interaction_time', 'last_interaction_time', 'first_purchase_time',
                       'last_purchase_time', 'registration_time']
        for col in time_columns:
            if col in user_stats_df.columns and not user_stats_df[col].empty:
                if not pd.api.types.is_datetime64_ns_dtype(user_stats_df[col]):
                    logger.info(f"  • 轉換 {col} 為 datetime64[ns]")
                    user_stats_df[col] = pd.to_datetime(user_stats_df[col])
                if not all(ts.tzinfo == timezone.utc for ts in user_stats_df[col].dropna()):
                    logger.info(f"  • 轉換 {col} 時區為 UTC")
                    user_stats_df[col] = user_stats_df[col].dt.tz_convert('UTC')
        logger.info("  • 時間欄位檢查通過")

        # 檢查數值欄位的型別
        logger.info("檢查數值欄位...")
        user_stats_df['ec_id'] = user_stats_df['ec_id'].astype('int64')
        user_stats_df['total_sessions'] = user_stats_df['total_sessions'].fillna(0).astype('int64')
        user_stats_df['purchase_count'] = user_stats_df['purchase_count'].fillna(0).astype('int64')
        user_stats_df['total_purchase_amount'] = user_stats_df['total_purchase_amount'].fillna(0).astype('float64')
        user_stats_df['permanent'] = user_stats_df['permanent'].astype(str)
        logger.info("  • 數值欄位型別轉換完成")

        # 檢查數值是否合理
        logger.info("檢查數值合理性...")
        if (user_stats_df['total_sessions'] < 0).any():
            raise ValueError("total_sessions 包含負值")
        if (user_stats_df['purchase_count'] < 0).any():
            raise ValueError("purchase_count 包含負值")
        if (user_stats_df['total_purchase_amount'] < 0).any():
            raise ValueError("total_purchase_amount 包含負值")
        logger.info("  • 數值合理性檢查通過")

        # 檢查時間順序是否合理
        logger.info("檢查時間順序...")
        mask = user_stats_df['first_interaction_time'] > user_stats_df['last_interaction_time']
        if mask.any():
            problematic_rows = user_stats_df[mask]
            raise ValueError(f"發現 {len(problematic_rows)} 筆資料的 first_interaction_time 晚於 last_interaction_time")
        logger.info("  • 時間順序檢查通過")

        # 驗證數據格式是否正確
        logger.info("執行最終資料驗證...")
        if not validate_snapshot(user_stats_df):
            raise ValueError("用戶統計資料驗證失敗")
        logger.info("  • 最終資料驗證通過")

        # 使用 StorageManager 保存快照
        logger.info("開始上傳到 GCS...")
        storage_manager = StorageManager()
        result = storage_manager.save_snapshot(
            data_df=user_stats_df,
            date=current_date,
            ec_id=ec_id
        )

        if result.get("success", False):
            logger.info(f"✓ 成功保存快照到 GCS: {result.get('path', '')}")
            return True
        else:
            raise Exception(result.get('error', '未知錯誤'))

    except Exception as e:
        logger.error(f"✗ 保存快照到 GCS 時發生錯誤: {str(e)}")
        return False

def save_to_parquet(user_stats_df, output_path, ec_id=None):
    """將用戶統計資料保存為 parquet 檔案"""
    logger.info(f"開始將 {len(user_stats_df)} 筆用戶統計資料保存為 parquet 檔案...")

    try:
        # 確保輸出目錄存在
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 檢查並轉換時間欄位為奈秒精度
        datetime_columns = [
            'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'registration_time'
        ]

        for col in datetime_columns:
            if col in user_stats_df.columns and not user_stats_df[col].empty:
                # 檢查是否為微秒精度
                dtype_str = str(user_stats_df[col].dtype)
                if dtype_str.startswith('datetime64[us'):
                    logger.info(f"將欄位 {col} 從 {dtype_str} 轉換為奈秒精度")
                    # 轉為字串再轉回 datetime，強制使用奈秒精度
                    user_stats_df[col] = pd.to_datetime(user_stats_df[col].astype(str))
                    logger.info(f"轉換後欄位 {col} 型別: {user_stats_df[col].dtype}")

        # 保存為 parquet 檔案
        logger.info(f"寫入 parquet 檔案: {output_path}")
        user_stats_df.to_parquet(output_path, index=False)

        logger.info(f"✓ 成功保存 parquet 檔案到: {output_path}")
        return True
    except Exception as e:
        logger.error(f"✗ 保存 parquet 檔案時發生錯誤: {str(e)}")
        return False

def save_local_parquet(user_stats_df, ec_id, output_path='./local_data', skip_local=False, output_filename=None):
    """將用戶統計資料保存為本地 parquet 檔案

    Args:
        user_stats_df: 用戶統計資料
        ec_id: 電商平台 ID
        output_path: 輸出目錄路徑
        skip_local: 是否跳過本地保存
        output_filename: 指定輸出檔案名稱

    Returns:
        bool: 是否成功保存
    """
    if skip_local:
        logger.info("[跳過] 不保存本地 parquet 檔案")
        return True

    logger.info(f"開始將 {len(user_stats_df)} 筆用戶統計資料保存為本地 parquet 檔案...")

    try:
        # 確保輸出目錄存在
        logger.info(f"檢查輸出目錄: {output_path}")
        os.makedirs(output_path, exist_ok=True)

        # 使用指定的檔案名稱或生成預設檔案名稱
        if output_filename:
            # 不添加.parquet後綴如果已經有了
            if not output_filename.endswith('.parquet'):
                filename = f"{output_filename}.parquet"
            else:
                filename = output_filename
            logger.info(f"使用指定的檔案名稱: {filename}")
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"user_stats_ec{ec_id}_{timestamp}.parquet"
            logger.info(f"使用自動生成的檔案名稱: {filename}")

        file_path = os.path.join(output_path, filename)

        # 檢查並轉換時間欄位為奈秒精度
        datetime_columns = [
            'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'registration_time'
        ]

        for col in datetime_columns:
            if col in user_stats_df.columns and not user_stats_df[col].empty:
                # 檢查是否為微秒精度
                dtype_str = str(user_stats_df[col].dtype)
                if dtype_str.startswith('datetime64[us'):
                    logger.info(f"將欄位 {col} 從 {dtype_str} 轉換為奈秒精度")
                    # 轉為字串再轉回 datetime，強制使用奈秒精度
                    user_stats_df[col] = pd.to_datetime(user_stats_df[col].astype(str))
                    logger.info(f"轉換後欄位 {col} 型別: {user_stats_df[col].dtype}")

        # 保存為 parquet 檔案
        logger.info(f"寫入 parquet 檔案到: {file_path}")
        user_stats_df.to_parquet(file_path, index=False)

        logger.info(f"✓ 成功保存本地 parquet 檔案到: {file_path}")
        return True
    except Exception as e:
        logger.error(f"✗ 保存本地 parquet 檔案時發生錯誤: {str(e)}")
        return False

def get_user_data_from_bigquery_simple(ec_id, batch_size=10000, max_rows=None, time_range=None):
    """
    直接從 BigQuery 獲取用戶數據，使用 pandas-gbq 進行高效查詢處理
    包含智能時間切片和平行查詢優化

    Args:
        ec_id: 電商平台 ID
        batch_size: 一次獲取的數據批次大小
        max_rows: 限制獲取的最大數據行數
        time_range: 時間範圍元組 (start_date, end_date)

    Returns:
        用戶數據 DataFrame
    """
    logger.info(f"從 BigQuery 直接獲取 ec_id={ec_id} 的用戶數據 (使用 pandas-gbq)")

    # 添加時間範圍條件（如果有指定）
    time_condition = ""
    if time_range:
        start_date, end_date = time_range
        time_condition = f"""
        AND event_time >= TIMESTAMP('{start_date.isoformat()}')
        AND event_time < TIMESTAMP('{end_date.isoformat()}')
        """
        logger.info(f"使用時間範圍: {start_date.isoformat()} 到 {end_date.isoformat()}")

    # 構建查詢，根據是否有限制來調整
    limit_clause = f"LIMIT {max_rows}" if max_rows else ""

    try:
        # 如果設置了明確的數據量限制，直接執行簡單查詢
        if max_rows is not None and max_rows <= 1000000:
            logger.info(f"使用限制 {max_rows} 筆數據的簡單查詢...")
            # 構建標準查詢
            query = f"""
            SELECT
                {ec_id} AS ec_id,
                permanent,
                event_time AS interaction_time,
                CASE WHEN event.name = 'purchase' THEN TRUE ELSE FALSE END AS purchase_confirmation,
                IFNULL(event.value, 0) AS event_value,
                event.name AS event_name,
                session.id AS session_id
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ec_id = {ec_id}
            {time_condition}
            ORDER BY event_time
            {limit_clause}
            """
            df = execute_single_query(query)
            if df is not None and len(df) > 0:
                # 直接返回原始數據，不進行處理
                return df
            return pd.DataFrame()

        # 首先進行數據量估算
        logger.info("嘗試估算查詢結果數據量...")
        try:
            estimated_rows, estimated_time = estimate_query_size(ec_id, time_range)
            logger.info(f"估計查詢將返回約 {estimated_rows:,} 筆數據")

            # 如果估計數據量超過100萬行，提示用戶考慮使用時間切片
            if estimated_rows > 1_000_000:
                logger.warning(f"⚠️ 估計數據量較大 ({estimated_rows:,} 筆)，處理可能需要較長時間")
                logger.warning("考慮使用較小的時間範圍或設置 --limit 參數來限制數據量")
        except Exception as e:
            logger.warning(f"估算數據量時發生錯誤: {str(e)}，繼續執行...")
            estimated_rows = 10_000_000  # 假設一個默認值

        # 對於小數據量，直接執行完整查詢
        if estimated_rows <= 1_000_000 or max_rows is not None:
            logger.info("數據量適中，直接執行完整查詢...")
            # 構建標準查詢
            query = f"""
            SELECT
                {ec_id} AS ec_id,
                permanent,
                event_time AS interaction_time,
                CASE WHEN event.name = 'purchase' THEN TRUE ELSE FALSE END AS purchase_confirmation,
                IFNULL(event.value, 0) AS event_value,
                event.name AS event_name,
                session.id AS session_id
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ec_id = {ec_id}
            {time_condition}
            ORDER BY event_time
            {limit_clause}
            """
            df = execute_single_query(query)
            if df is not None and len(df) > 0:
                # 直接返回原始數據，不進行處理
                return df
            return pd.DataFrame()

        # 【優化1】智能時間採樣：在執行詳細時間切片前，先檢測數據分布
        if time_range:
            logger.info("進行智能時間採樣，檢測數據分布...")
            active_periods = detect_data_distribution(ec_id, time_range)

            if not active_periods:
                logger.warning(f"在指定時間範圍內未檢測到 EC ID {ec_id} 有數據，返回空結果")
                return pd.DataFrame()

            logger.info(f"檢測到 {len(active_periods)} 個時間段有數據，將集中查詢這些時間段")

            # 創建時間片段
            time_chunks = []
            for period_start, period_end in active_periods:
                # 【優化2】自適應調整每個活躍期間的切片粒度
                current_start = period_start

                # 估算這個時間段的數據量
                period_rows = estimate_period_rows(ec_id, period_start, period_end)

                # 根據時間段數據量確定切片大小
                if period_rows > 10_000_000:
                    days_per_chunk = 7  # 每片7天
                elif period_rows > 5_000_000:
                    days_per_chunk = 14  # 每片14天
                elif period_rows > 1_000_000:
                    days_per_chunk = 30  # 每片30天
                else:
                    days_per_chunk = 60  # 每片60天，或者直接查詢整個期間

                logger.info(f"活躍時間段 {period_start.isoformat()} 到 {period_end.isoformat()} 估計有 {period_rows:,} 筆數據")
                logger.info(f"使用每片 {days_per_chunk} 天的切片粒度")

                # 切分這個活躍時間段
                while current_start < period_end:
                    current_end = min(current_start + timedelta(days=days_per_chunk), period_end)
                    time_chunks.append((current_start, current_end))
                    current_start = current_end

            logger.info(f"生成了 {len(time_chunks)} 個時間片段進行查詢")
        else:
            # 如果沒有指定時間範圍，使用默認策略
            logger.info("未指定時間範圍，將根據數據量估算採用適當的時間切片策略...")

            # 【優化3】根據總數據量選擇更大的時間片段
            if estimated_rows > 50_000_000:
                days_per_chunk = 30  # 每片30天
            elif estimated_rows > 20_000_000:
                days_per_chunk = 60  # 每片60天
            elif estimated_rows > 10_000_000:
                days_per_chunk = 90  # 每片90天
            else:
                days_per_chunk = 180  # 每片180天

            logger.info(f"將使用每片 {days_per_chunk} 天的時間切片")

            # 獲取預設時間範圍
            default_start, default_end = get_custom_time_range('2022-01-01')

            # 計算時間片段
            time_chunks = []
            current_start = default_start
            while current_start < default_end:
                current_end = min(current_start + timedelta(days=days_per_chunk), default_end)
                time_chunks.append((current_start, current_end))
                current_start = current_end

            logger.info(f"生成了 {len(time_chunks)} 個時間片段進行查詢")

        # 【優化4】平行處理多個時間片段
        logger.info("啟動平行處理多個時間片段的查詢...")
        all_user_data = []

        # 定義單個時間片段查詢函數
        def query_time_chunk(i, chunk_start, chunk_end):
            chunk_start_time = time.time()
            logger.info(f"處理第 {i+1}/{len(time_chunks)} 個時間片段: "
                      f"{chunk_start.isoformat()} 到 {chunk_end.isoformat()}")

            # 構建時間片段查詢
            chunk_query = f"""
            SELECT
                {ec_id} AS ec_id,
                permanent,
                event_time AS interaction_time,
                CASE WHEN event.name = 'purchase' THEN TRUE ELSE FALSE END AS purchase_confirmation,
                IFNULL(event.value, 0) AS event_value,
                event.name AS event_name,
                session.id AS session_id
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ec_id = {ec_id}
            AND event_time >= TIMESTAMP('{chunk_start.isoformat()}')
            AND event_time < TIMESTAMP('{chunk_end.isoformat()}')
            """

            try:
                logger.info(f"執行時間片段 {i+1} 的查詢...")

                # 配置 pandas-gbq 查詢選項
                configuration = {
                    'query': {
                        'useQueryCache': True,
                        'maximumBytesBilled': 100_000_000_000,  # 100GB 的最大計費量
                        'priority': 'INTERACTIVE',
                        'useLegacySql': False
                    }
                }

                # 設定超時和重試機制
                chunk_df = pandas_gbq.read_gbq(
                    chunk_query,
                    project_id=None,  # 使用預設的專案 ID
                    configuration=configuration,
                    dialect='standard',
                    progress_bar_type=None,
                    max_results=None,  # 不限制結果數量
                    use_bqstorage_api=True,  # 啟用 BigQuery Storage API
                )

                chunk_duration = time.time() - chunk_start_time
                logger.info(f"時間片段 {i+1} 完成: 獲取了 {len(chunk_df):,} 行，"
                          f"耗時 {chunk_duration:.2f} 秒")

                return chunk_df if not chunk_df.empty else None

            except Exception as e:
                logger.error(f"處理時間片段 {i+1} 時出錯: {str(e)}")
                logger.error("嘗試使用備用方法...")

                try:
                    # 備用方法：使用標準 bigquery 客戶端
                    logger.info("使用 bigquery 客戶端作為備用方法...")
                    client = bigquery.Client()
                    chunk_job = client.query(chunk_query)
                    chunk_df = chunk_job.to_dataframe(create_bqstorage_client=True)

                    chunk_duration = time.time() - chunk_start_time
                    logger.info(f"備用方法成功: 獲取了 {len(chunk_df):,} 行，"
                              f"耗時 {chunk_duration:.2f} 秒")

                    return chunk_df if not chunk_df.empty else None

                except Exception as e2:
                    logger.error(f"備用方法也失敗: {str(e2)}")
                    return None

        # 【優化5】使用平行處理執行查詢，最多同時執行4個查詢
        max_workers = min(4, len(time_chunks))
        logger.info(f"使用 {max_workers} 個工作線程平行處理查詢")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_chunk = {
                executor.submit(query_time_chunk, i, chunk[0], chunk[1]): (i, chunk)
                for i, chunk in enumerate(time_chunks)
            }

            completed = 0
            for future in concurrent.futures.as_completed(future_to_chunk):
                i, chunk = future_to_chunk[future]
                try:
                    chunk_df = future.result()
                    if chunk_df is not None and not chunk_df.empty:
                        all_user_data.append(chunk_df)
                except Exception as e:
                    logger.error(f"時間片段 {i+1} 處理失敗: {str(e)}")

                completed += 1
                logger.info(f"已完成 {completed}/{len(time_chunks)} 個時間片段查詢 ({completed/len(time_chunks)*100:.1f}%)")

        # 處理并行查詢結果
        if all_user_data:
            # 合併所有時間片段的數據
            if len(all_user_data) > 1:
                logger.info(f"合併 {len(all_user_data)} 個時間片段的數據...")
                combined_df = pd.concat(all_user_data, ignore_index=True)
                logger.info(f"合併完成，總共 {len(combined_df)} 行數據")
                return combined_df
            else:
                # 如果只有一個時間片段，直接返回該片段的數據
                logger.info(f"僅有 1 個時間片段的數據，共 {len(all_user_data[0])} 行")
                return all_user_data[0]
        return pd.DataFrame()

    except Exception as e:
        logger.error(f"從 BigQuery 獲取數據時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        raise

def execute_single_query(query, query_job_config=None):
    """執行單一 BigQuery 查詢，並返回結果"""
    client = bigquery.Client()
    try:
        logger.debug(f"執行 BigQuery 查詢:\n{query}")
        start_time = time.time()
        if query_job_config:
            query_job = client.query(query, job_config=query_job_config)
        else:
            query_job = client.query(query)

        results = query_job.result()
        elapsed_time = time.time() - start_time
        logger.debug(f"查詢完成，耗時 {elapsed_time:.2f} 秒")
        return results
    except Exception as e:
        logger.error(f"執行 BigQuery 查詢時發生錯誤: {str(e)}")
        logger.exception(e)
        return None

def detect_data_distribution(ec_id, time_range):
    """
    檢測數據在時間範圍內的分布情況
    返回有數據的時間段列表

    Args:
        ec_id: 電商平台 ID
        time_range: 時間範圍元組 (start_date, end_date)

    Returns:
        list: 包含有數據的時間段元組列表 [(start1, end1), (start2, end2), ...]
    """
    if not time_range:
        return []

    start_date, end_date = time_range

    # 計算總時間跨度
    total_months = (end_date.year - start_date.year) * 12 + end_date.month - start_date.month

    # 如果時間跨度小於3個月，直接返回整個時間段
    if total_months <= 3:
        logger.info(f"時間跨度較小 ({total_months} 個月)，跳過數據分布檢測")
        return [(start_date, end_date)]

    logger.info(f"檢測數據在 {total_months} 個月時間範圍內的分布情況...")

    # 按季度切分並檢查每個季度是否有數據
    active_periods = []
    current_start = start_date

    # 根據時間跨度決定采樣間隔
    if total_months > 36:  # 3年以上
        interval_months = 6  # 半年為間隔
    elif total_months > 12:  # 1-3年
        interval_months = 3  # 季度為間隔
    else:
        interval_months = 1  # 月為間隔

    logger.info(f"使用 {interval_months} 個月為採樣間隔")

    client = bigquery.Client()

    # 創建時間切片並快速檢查每個切片是否有數據
    while current_start < end_date:
        # 計算當前間隔的結束時間
        if interval_months == 1:
            # 下一個月的同一天
            next_month = current_start.month + 1
            next_year = current_start.year
            if next_month > 12:
                next_month = 1
                next_year += 1
            try:
                current_end = current_start.replace(year=next_year, month=next_month)
            except ValueError:  # 處理2月29日等特殊情況
                # 使用下個月的第一天
                if next_month > 12:
                    next_month = 1
                    next_year += 1
                current_end = current_start.replace(year=next_year, month=next_month, day=1)
        else:
            # 增加指定月數
            next_month = current_start.month + interval_months
            next_year = current_start.year
            while next_month > 12:
                next_month -= 12
                next_year += 1
            try:
                current_end = current_start.replace(year=next_year, month=next_month)
            except ValueError:  # 處理2月29日等特殊情況
                # 使用下個月的第一天
                if next_month > 12:
                    next_month = 1
                    next_year += 1
                current_end = current_start.replace(year=next_year, month=next_month, day=1)

        # 確保不超過總時間範圍
        current_end = min(current_end, end_date)

        # 快速查詢這個時間段是否有數據
        sample_query = f"""
        SELECT COUNT(*) as count
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
        AND event_time >= TIMESTAMP('{current_start.isoformat()}')
        AND event_time < TIMESTAMP('{current_end.isoformat()}')
        LIMIT 1
        """

        try:
            query_job = client.query(sample_query)
            result = list(query_job.result())[0]

            if result.count > 0:
                logger.info(f"✓ 在時間段 {current_start.isoformat()} 到 {current_end.isoformat()} 發現數據")
                active_periods.append((current_start, current_end))
            else:
                logger.info(f"✗ 時間段 {current_start.isoformat()} 到 {current_end.isoformat()} 沒有數據")
        except Exception as e:
            logger.warning(f"檢查時間段時出錯: {str(e)}")
            # 保險起見，將這個時間段包括進來
            active_periods.append((current_start, current_end))

        # 移動到下一個間隔
        current_start = current_end

    # 合併相鄰的活躍時間段
    if active_periods:
        merged_periods = [active_periods[0]]
        for period in active_periods[1:]:
            last_period = merged_periods[-1]
            # 如果當前段的開始時間是上一段的結束時間，合併它們
            if period[0] == last_period[1]:
                merged_periods[-1] = (last_period[0], period[1])
            else:
                merged_periods.append(period)

        logger.info(f"合併後共有 {len(merged_periods)} 個活躍時間段")
        return merged_periods

    return []

def estimate_period_rows(ec_id, period_start, period_end):
    """估算特定時間段內的數據行數"""
    try:
        client = bigquery.Client()

        # 直接計數
        count_query = f"""
        SELECT COUNT(*) as count
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
        AND event_time >= TIMESTAMP('{period_start.isoformat()}')
        AND event_time < TIMESTAMP('{period_end.isoformat()}')
        """

        query_job = client.query(count_query)
        result = list(query_job.result())[0]

        return result.count
    except Exception as e:
        logger.warning(f"估算時間段數據量時出錯: {str(e)}")
        # 預設為中等數據量
        return 1_000_000

def process_raw_data_with_progress(raw_data, ec_id):
    """處理原始數據並顯示進度條"""
    start_time = time.time()
    logger.info(f"[標準單進程] 開始處理原始數據，共 {len(raw_data):,} 行，{raw_data['permanent'].nunique():,} 個用戶")

    # 檢查數據型別
    logger.info(f"[標準單進程] 原始數據型別:\n{raw_data.dtypes}")

    # 檢查時間欄位
    time_columns = [col for col in raw_data.columns if 'time' in col.lower()]
    if time_columns:
        logger.info(f"[標準單進程] 檢測到時間欄位: {', '.join(time_columns)}")
        for col in time_columns:
            # 檢查時間欄位的類型和內容
            non_null_values = raw_data[col].count()
            logger.info(f"[標準單進程] 欄位 {col}: {non_null_values:,} 非空值 ({non_null_values/len(raw_data)*100:.2f}%)")

            # 檢查時間範圍
            if non_null_values > 0:
                try:
                    min_time = raw_data[col].min()
                    max_time = raw_data[col].max()
                    logger.info(f"[標準單進程] 欄位 {col} 時間範圍: {min_time} 到 {max_time}")
                except Exception as e:
                    logger.warning(f"[標準單進程] 無法獲取 {col} 的時間範圍: {str(e)}")

    # 檢查購買相關欄位
    purchase_columns = [col for col in raw_data.columns if 'purchase' in col.lower() or 'price' in col.lower() or 'order' in col.lower()]
    if purchase_columns:
        logger.info(f"[標準單進程] 檢測到購買相關欄位: {', '.join(purchase_columns)}")
        for col in purchase_columns:
            if raw_data[col].dtype == 'bool' or raw_data[col].dtype == 'object':
                try:
                    # 對於布爾或字符串欄位，檢查可能是購買確認的欄位
                    true_values = raw_data[col].map(lambda x: str(x).lower() in ['true', '1', 't', 'yes', 'y']).sum()
                    logger.info(f"[標準單進程] 欄位 {col}: {true_values:,} 個可能的購買事件 ({true_values/len(raw_data)*100:.2f}%)")
                except Exception as e:
                    logger.warning(f"[標準單進程] 無法分析 {col} 欄位: {str(e)}")
            elif 'float' in str(raw_data[col].dtype) or 'int' in str(raw_data[col].dtype):
                # 對於數值欄位，可能是價格
                non_zero_values = (raw_data[col] > 0).sum()
                logger.info(f"[標準單進程] 欄位 {col}: {non_zero_values:,} 個非零值 ({non_zero_values/len(raw_data)*100:.2f}%)")

    # 檢查會話相關欄位
    session_columns = [col for col in raw_data.columns if 'session' in col.lower()]
    if session_columns:
        logger.info(f"[標準單進程] 檢測到會話相關欄位: {', '.join(session_columns)}")
        for col in session_columns:
            unique_values = raw_data[col].nunique()
            logger.info(f"[標準單進程] 欄位 {col}: {unique_values:,} 個唯一值")

    # 分塊處理用戶數據以節省內存並顯示進度
    unique_users = raw_data['permanent'].unique()
    user_count = len(unique_users)
    logger.info(f"[標準單進程] 開始處理 {user_count:,} 個用戶的數據...")

    # 計算合適的分塊大小
    chunk_size = min(5000, max(100, user_count // 100))  # 確保至少顯示 100 個進度更新
    user_chunks = [unique_users[i:i + chunk_size] for i in range(0, user_count, chunk_size)]
    logger.info(f"[標準單進程] 將用戶分為 {len(user_chunks)} 個批次處理，每批次 {chunk_size} 個用戶")

    # 初始化結果
    all_user_data = []
    total_processed = 0
    processed_sessions = 0
    processed_purchases = 0

    # 使用 tqdm 顯示進度條
    for chunk_index, user_chunk in enumerate(tqdm(user_chunks, desc="[標準單進程] 處理用戶數據")):
        chunk_start = time.time()
        logger.info(f"[標準單進程] 正在處理第 {chunk_index + 1}/{len(user_chunks)} 批次，{len(user_chunk):,} 個用戶")

        # 將用戶數據變成字典以加速查找
        chunk_data = {user: raw_data[raw_data['permanent'] == user] for user in user_chunk}

        # 處理當前批次的用戶
        try:
            user_data_chunk = pd.DataFrame([
                process_user_data(user, user_df, ec_id)
                for user, user_df in chunk_data.items()
            ])

            # 統計處理結果
            if 'total_sessions' in user_data_chunk.columns:
                chunk_sessions = user_data_chunk['total_sessions'].sum()
                processed_sessions += chunk_sessions
                logger.info(f"[標準單進程] 批次 {chunk_index + 1} 處理了 {chunk_sessions:,} 個會話，平均每用戶 {chunk_sessions/len(user_data_chunk):.2f} 個")

            if 'purchase_count' in user_data_chunk.columns:
                chunk_purchases = user_data_chunk['purchase_count'].sum()
                purchase_users = (user_data_chunk['purchase_count'] > 0).sum()
                processed_purchases += chunk_purchases
                logger.info(f"[標準單進程] 批次 {chunk_index + 1} 處理了 {chunk_purchases:,} 個購買事件，{purchase_users} 個購買用戶")

            all_user_data.append(user_data_chunk)
            total_processed += len(user_chunk)

            # 報告進度
            elapsed = time.time() - chunk_start
            logger.info(f"[標準單進程] 批次 {chunk_index + 1} 處理完成，耗時 {elapsed:.2f} 秒，處理速度: {len(user_chunk)/elapsed:.2f} 用戶/秒")
            logger.info(f"[標準單進程] 已處理 {total_processed:,}/{user_count:,} 個用戶 ({total_processed/user_count*100:.2f}%)")

        except Exception as e:
            logger.error(f"[標準單進程] 處理批次 {chunk_index + 1} 時發生錯誤: {str(e)}")
            logger.exception("[標準單進程] 詳細錯誤信息:")
            # 繼續處理下一批次

    try:
        # 合併所有批次的結果
        if all_user_data:
            logger.info(f"[標準單進程] 合併 {len(all_user_data)} 個批次的結果...")
            result = pd.concat(all_user_data, ignore_index=True)

            # 輸出最終統計信息
            logger.info(f"[標準單進程] 處理完成! 總計 {len(result):,} 個用戶記錄")
            logger.info(f"[標準單進程] 總計 {processed_sessions:,} 個會話，平均每用戶 {processed_sessions/len(result) if len(result) > 0 else 0:.2f} 個")
            logger.info(f"[標準單進程] 總計 {processed_purchases:,} 個購買事件")

            # 檢查關鍵欄位
            for col in ['first_interaction_time', 'last_interaction_time', 'first_purchase_time', 'last_purchase_time', 'registration_time']:
                if col in result.columns:
                    non_null = result[col].count()
                    logger.info(f"[標準單進程] {col}: {non_null:,} 個非空值 ({non_null/len(result)*100:.2f}%)")

            total_time = time.time() - start_time
            logger.info(f"[標準單進程] 總處理時間: {total_time:.2f} 秒，處理速度: {user_count/total_time:.2f} 用戶/秒")

            return result
        else:
            logger.warning("[標準單進程] 沒有處理到任何數據，返回空的 DataFrame")
            return pd.DataFrame()
    except Exception as e:
        logger.error(f"[標準單進程] 合併結果時發生錯誤: {str(e)}")
        logger.exception("[標準單進程] 詳細錯誤信息:")
        return pd.DataFrame()

def process_user_data(user_id, user_df, ec_id):
    """處理單一用戶的數據並返回統計信息"""
    start_time = time.time()

    # 檢查數據
    if user_df.empty:
        logger.warning(f"用戶 {user_id} 的數據為空")
        return {
            'permanent': user_id,
            'ec_id': ec_id,
            'total_sessions': 0,
            'purchase_count': 0,
            'total_purchase_amount': 0.0,
            'registration_time': pd.NaT,
            'first_interaction_time': pd.NaT,
            'last_interaction_time': pd.NaT,
            'first_purchase_time': pd.NaT,
            'last_purchase_time': pd.NaT,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }

    # 檢測時間欄位
    time_column = None
    for col in ['interaction_time', 'event_time']:
        if col in user_df.columns:
            time_column = col
            break

    if time_column is None:
        # 嘗試查找包含 'time' 的欄位
        time_candidates = [col for col in user_df.columns if 'time' in col.lower()]
        if time_candidates:
            time_column = time_candidates[0]
        else:
            logger.error(f"用戶 {user_id} 的數據中找不到時間欄位")
            return {
                'permanent': user_id,
                'ec_id': ec_id,
                'total_sessions': 0,
                'purchase_count': 0,
                'total_purchase_amount': 0.0,
                'registration_time': pd.NaT,
                'first_interaction_time': pd.NaT,
                'last_interaction_time': pd.NaT,
                'first_purchase_time': pd.NaT,
                'last_purchase_time': pd.NaT,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

    # 確保時間欄位是 datetime 類型
    try:
        user_df[time_column] = pd.to_datetime(user_df[time_column], errors='coerce')
    except Exception as e:
        logger.error(f"用戶 {user_id} 轉換時間欄位 {time_column} 失敗: {str(e)}")

    # 計算基本時間統計
    try:
        first_interaction = user_df[time_column].min()
        last_interaction = user_df[time_column].max()
    except Exception as e:
        logger.error(f"用戶 {user_id} 計算時間統計失敗: {str(e)}")
        first_interaction = pd.NaT
        last_interaction = pd.NaT

    # 計算會話數
    total_sessions = 1  # 默認至少有一個會話
    if 'session_id' in user_df.columns:
        try:
            # 檢查 session_id 是否為空或全部相同
            unique_sessions = user_df['session_id'].dropna().nunique()
            if unique_sessions > 0:
                total_sessions = unique_sessions
                logger.debug(f"用戶 {user_id} 有 {total_sessions} 個不同的會話 ID")
            else:
                logger.debug(f"用戶 {user_id} 的會話 ID 全部為空或相同，使用默認值 1")
        except Exception as e:
            logger.error(f"用戶 {user_id} 計算會話數失敗: {str(e)}")
    else:
        # 根據時間間隔估算會話數
        try:
            if len(user_df) > 1 and not pd.isna(first_interaction) and not pd.isna(last_interaction):
                # 按時間排序
                sorted_times = user_df[time_column].dropna().sort_values().reset_index(drop=True)
                if len(sorted_times) > 1:
                    # 計算時間差
                    time_diffs = sorted_times.diff()
                    # 會話間隔閾值為 30 分鐘
                    session_breaks = (time_diffs > pd.Timedelta(minutes=30)).sum()
                    total_sessions = session_breaks + 1
                    logger.debug(f"用戶 {user_id} 根據時間間隔估算有 {total_sessions} 個會話")
        except Exception as e:
            logger.error(f"用戶 {user_id} 估算會話數失敗: {str(e)}")

    # 計算購買相關數據
    purchase_count = 0
    total_purchase_amount = 0.0
    first_purchase_time = pd.NaT
    last_purchase_time = pd.NaT

    # 檢測購買確認欄位
    purchase_column = None
    if 'purchase_confirmation' in user_df.columns:
        purchase_column = 'purchase_confirmation'
    elif 'event_name' in user_df.columns:
        # 創建臨時購買確認欄位
        user_df['temp_purchase'] = user_df['event_name'].astype(str) == 'purchase'
        purchase_column = 'temp_purchase'

    # 檢測價格欄位
    price_column = None
    if 'event_value' in user_df.columns:
        price_column = 'event_value'
        logger.debug(f"用戶 {user_id} 使用 event_value 作為價格欄位")

    # 處理購買數據
    if purchase_column:
        try:
            # 確保購買欄位是布爾類型
            if user_df[purchase_column].dtype != 'bool':
                user_df[purchase_column] = user_df[purchase_column].astype(str).map(
                    lambda x: str(x).lower() in ['true', '1', 't', 'yes', 'y', 'purchase']
                )

            # 過濾購買事件
            purchase_events = user_df[user_df[purchase_column] == True]
            purchase_count = len(purchase_events)

            if purchase_count > 0:
                # 計算購買時間
                first_purchase_time = purchase_events[time_column].min()
                last_purchase_time = purchase_events[time_column].max()

                # 計算購買金額
                if price_column and price_column in purchase_events.columns:
                    try:
                        # 確保價格是數值類型
                        purchase_events[price_column] = pd.to_numeric(purchase_events[price_column], errors='coerce')
                        total_purchase_amount = purchase_events[price_column].fillna(0).sum()
                    except Exception as e:
                        logger.error(f"用戶 {user_id} 計算購買金額失敗: {str(e)}")
                        total_purchase_amount = 0.0
                else:
                    logger.debug(f"用戶 {user_id} 缺少價格欄位或價格全為空")
        except Exception as e:
            logger.error(f"用戶 {user_id} 處理購買數據失敗: {str(e)}")

    # 處理註冊時間
    registration_time = pd.NaT
    if 'event_name' in user_df.columns:
        try:
            register_events = user_df[user_df['event_name'].astype(str) == 'register']
            if not register_events.empty:
                registration_time = register_events[time_column].min()
                logger.debug(f"用戶 {user_id} 的註冊時間為 {registration_time}")
        except Exception as e:
            logger.error(f"用戶 {user_id} 處理註冊時間失敗: {str(e)}")

    # 創建用戶統計結果
    current_time = datetime.now()
    result = {
        'permanent': user_id,
        'ec_id': ec_id,
        'total_sessions': total_sessions,
        'registration_time': registration_time,
        'first_interaction_time': first_interaction,
        'last_interaction_time': last_interaction,
        'first_purchase_time': first_purchase_time,
        'last_purchase_time': last_purchase_time,
        'purchase_count': purchase_count,
        'total_purchase_amount': total_purchase_amount,
        'created_at': current_time,
        'updated_at': current_time
    }

    # 計算處理時間
    processing_time = time.time() - start_time
    logger.debug(f"用戶 {user_id} 處理完成，耗時 {processing_time:.3f} 秒，{purchase_count} 個購買，{total_sessions} 個會話")

    return result

def process_user_subset(users_subset, df_chunk_dict, ec_id, time_column, purchase_column, price_column):
    """
    處理用戶數據子集的全局函數，用於多進程處理

    Args:
        users_subset: 要處理的用戶ID列表
        df_chunk_dict: 數據字典，包含必要的數據欄位
        ec_id: 電商ID
        time_column: 時間列名稱
        purchase_column: 購買標記列名稱
        price_column: 價格列名稱

    Returns:
        處理後的用戶統計 DataFrame
    """
    start_time = time.time()
    process_id = os.getpid()
    logger.info(f"[子進程 {process_id}] 開始處理 {len(users_subset)} 個用戶...")

    # 從字典重建子集 DataFrame
    logger.info(f"[子進程 {process_id}] 重建用戶數據子集...")
    dict_keys = list(df_chunk_dict.keys())
    logger.info(f"[子進程 {process_id}] 字典包含以下欄位: {dict_keys}")

    subset_df = pd.DataFrame(df_chunk_dict)
    initial_size = len(subset_df)
    subset_df = subset_df[subset_df['permanent'].isin(users_subset)]
    filtered_size = len(subset_df)

    # 記錄處理信息
    logger.info(f"[子進程 {process_id}] 原始數據 {initial_size} 行過濾後剩餘 {filtered_size} 行")
    subset_size = len(subset_df)
    unique_users = subset_df['permanent'].nunique()
    logger.info(f"[子進程 {process_id}] 處理數據包含 {subset_size} 行，{unique_users} 個唯一用戶")

    # 檢查數據列類型
    logger.info(f"[子進程 {process_id}] 數據列類型: \n{subset_df.dtypes}")

    # 檢查時間列
    logger.info(f"[子進程 {process_id}] 使用時間列: {time_column}")
    if time_column in subset_df.columns:
        time_range = f"{subset_df[time_column].min()} 到 {subset_df[time_column].max()}"
        logger.info(f"[子進程 {process_id}] 時間範圍: {time_range}")

    logger.info(f"[子進程 {process_id}] 開始計算基本時間統計...")
    # 使用向量化操作處理這個子集
    user_stats = subset_df.groupby('permanent').agg(
        first_interaction_time=(time_column, 'min'),
        last_interaction_time=(time_column, 'max')
    ).reset_index()
    logger.info(f"[子進程 {process_id}] 基本時間統計計算完成，包含 {len(user_stats)} 個用戶")

    # 計算 session 數量
    if 'session_id' in df_chunk_dict:
        logger.info(f"[子進程 {process_id}] 使用 session_id 計算會話數量...")
        # 創建一個臨時 DataFrame 用於計算唯一會話 ID
        session_df = pd.DataFrame({
            'permanent': [df_chunk_dict['permanent'][i] for i in range(len(df_chunk_dict['permanent']))],
            'session_id': [df_chunk_dict['session_id'][i] for i in range(len(df_chunk_dict['session_id']))]
        })
        session_df = session_df[session_df['permanent'].isin(users_subset)]

        # 計算每個用戶的唯一會話數
        session_stats = session_df.groupby('permanent')['session_id'].nunique().reset_index()
        session_stats.rename(columns={'session_id': 'total_sessions'}, inplace=True)
        logger.info(f"[子進程 {process_id}] 計算得到 {len(session_stats)} 個用戶的會話數")
        user_stats = pd.merge(user_stats, session_stats, on='permanent', how='left')
    else:
        logger.info(f"[子進程 {process_id}] 使用互動次數估算會話數...")
        # 如果沒有會話 ID，則使用時間間隔來估算會話
        sessions_start_time = time.time()

        # 對每個用戶進行會話估算
        total_sessions = 0
        session_dict = {}  # 用於存儲每個用戶的會話數

        # 預先過濾用戶，提高處理速度
        for user in users_subset:
            # 獲取用戶的所有互動時間
            user_indices = [i for i, p in enumerate(df_chunk_dict['permanent']) if p == user]
            if not user_indices:
                # 如果找不到用戶數據，設定為1個會話
                session_dict[user] = 1
                total_sessions += 1
                continue

            # 獲取用戶的互動時間並排序
            user_times = [df_chunk_dict[time_column][i] for i in user_indices]
            user_times.sort()

            # 使用30分鐘間隔來識別會話
            SESSION_TIMEOUT = pd.Timedelta(minutes=30)
            session_count = 1
            last_time = user_times[0]

            for current_time in user_times[1:]:
                # 如果兩次互動間隔超過30分鐘，認為是新會話
                if current_time - last_time > SESSION_TIMEOUT:
                    session_count += 1
                last_time = current_time

            session_dict[user] = session_count
            total_sessions += session_count

        # 將會話數據添加到用戶統計中
        user_stats['total_sessions'] = user_stats['permanent'].map(
            lambda x: session_dict.get(x, 1)  # 默認為1個會話
        )

        sessions_time = time.time() - sessions_start_time
        logger.info(f"[子進程 {process_id}] 完成會話數估算，共 {total_sessions} 個會話，耗時 {sessions_time:.2f} 秒")
        logger.info(f"[子進程 {process_id}] 總會話數: {total_sessions}, 平均每用戶會話數: {total_sessions/len(users_subset):.2f}")

    # 確保 total_sessions 不為空
    user_stats['total_sessions'] = user_stats['total_sessions'].fillna(1).astype(int)
    total_sessions = user_stats['total_sessions'].sum()
    avg_sessions = user_stats['total_sessions'].mean()
    logger.info(f"[子進程 {process_id}] 總會話數: {total_sessions}, 平均每用戶會話數: {avg_sessions:.2f}")

    # 處理購買數據
    logger.info(f"[子進程 {process_id}] 開始處理購買數據...")
    if purchase_column and price_column:
        # 確保 purchase_column 是布爾值
        if len(subset_df) > 0:  # 確保 DataFrame 不為空
            if isinstance(subset_df[purchase_column].iloc[0], str):
                logger.info(f"[子進程 {process_id}] 購買列是字符串類型，轉換為布爾值")
                purchase_df = subset_df[subset_df[purchase_column].astype(str) == 'True']
            else:
                purchase_df = subset_df[subset_df[purchase_column] == True]

            logger.info(f"[子進程 {process_id}] 找到 {len(purchase_df)} 個購買事件")

            if not purchase_df.empty:
                purchase_stats = purchase_df.groupby('permanent').agg(
                    purchase_count=(time_column, 'count'),
                    first_purchase_time=(time_column, 'min'),
                    last_purchase_time=(time_column, 'max'),
                    total_purchase_amount=(price_column, 'sum')
                ).reset_index()

                logger.info(f"[子進程 {process_id}] 計算得到 {len(purchase_stats)} 個購買用戶")
                if len(purchase_stats) > 0:
                    total_purchases = purchase_stats['purchase_count'].sum()
                    total_amount = purchase_stats['total_purchase_amount'].sum()
                    logger.info(f"[子進程 {process_id}] 總購買次數: {total_purchases}, 總購買金額: {total_amount:.2f}")

                user_stats = pd.merge(user_stats, purchase_stats, on='permanent', how='left')
            else:
                logger.info(f"[子進程 {process_id}] 無購買數據，添加空欄位")
                user_stats['purchase_count'] = 0
                user_stats['first_purchase_time'] = pd.NaT
                user_stats['last_purchase_time'] = pd.NaT
                user_stats['total_purchase_amount'] = 0.0
        else:
            logger.info(f"[子進程 {process_id}] 數據為空，添加默認購買欄位")
            user_stats['purchase_count'] = 0
            user_stats['first_purchase_time'] = pd.NaT
            user_stats['last_purchase_time'] = pd.NaT
            user_stats['total_purchase_amount'] = 0.0
    else:
        logger.info(f"[子進程 {process_id}] 缺少購買相關欄位，添加默認值")
        user_stats['purchase_count'] = 0
        user_stats['first_purchase_time'] = pd.NaT
        user_stats['last_purchase_time'] = pd.NaT
        user_stats['total_purchase_amount'] = 0.0

    # 處理註冊時間
    logger.info(f"[子進程 {process_id}] 開始處理註冊時間...")
    register_event = 'event_name' in df_chunk_dict
    if register_event:
        logger.info(f"[子進程 {process_id}] 找到 event_name 欄位，處理註冊事件")

        # 添加檢查 event_name 內容的分佈情況
        register_start = time.time()
        event_types = {}
        for name in df_chunk_dict['event_name']:
            name_str = str(name)
            if name_str in event_types:
                event_types[name_str] += 1
            else:
                event_types[name_str] = 1

        logger.info(f"[子進程 {process_id}] 事件類型分佈: {event_types}")

        # 創建一個標記註冊事件的布爾數組
        register_indices = []
        for i in range(len(df_chunk_dict['event_name'])):
            if df_chunk_dict['permanent'][i] in users_subset and str(df_chunk_dict['event_name'][i]) == 'register':
                register_indices.append(i)

        register_time = time.time() - register_start
        logger.info(f"[子進程 {process_id}] 找到 {len(register_indices)} 個註冊事件，耗時 {register_time:.2f} 秒")

    # 添加當前時間作為 created_at 和 updated_at
    current_time = datetime.now()
    user_stats['created_at'] = current_time
    user_stats['updated_at'] = current_time

    # 添加電商ID
    user_stats['ec_id'] = ec_id

    # 移除 customer_status 和 average_purchase_amount 欄位 (不再需要)

    vectorized_time = time.time() - vectorized_start
    logger.info(f"向量化計算完成，處理 {len(user_stats)} 筆用戶資料，耗時 {vectorized_time:.2f} 秒")
    logger.info(f"處理速度: {len(user_stats)/vectorized_time:.1f} 用戶/秒")

    # 3. 標準化輸出欄位和類型
    logger.info("3/4 標準化輸出欄位和類型...")

    # 確保數值欄位的類型正確
    user_stats['total_purchase_amount'] = user_stats['total_purchase_amount'].astype(float)

    # 4. 重新排序欄位
    logger.info("4/4 重新排序欄位...")

    # 定義欄位順序 - 移除不需要的欄位
    columns_order = [
        'ec_id', 'permanent', 'total_sessions',
        'registration_time', 'first_interaction_time', 'last_interaction_time',
        'first_purchase_time', 'last_purchase_time',
        'purchase_count', 'total_purchase_amount',
        'created_at', 'updated_at'
    ]

    # 只選擇存在的欄位
    available_columns = [col for col in columns_order if col in user_stats.columns]
    user_stats = user_stats[available_columns]

    # 計算總處理時間
    processing_time = time.time() - start_time
    logger.info(f"✅ 數據處理完成: {len(user_stats):,} 用戶統計記錄，處理耗時 {processing_time:.2f} 秒")
    logger.info(f"整體處理速度: {len(user_stats)/processing_time:.1f} 用戶/秒")

    # 添加以下代碼
    logger.info(f"原始數據前 5 行: \n{df.head().to_string()}")
    logger.info(f"原始數據列類型: \n{df.dtypes}")

    # 檢查是否有 'event_name' 且包含 'purchase'
    if 'event_name' in df.columns:
        purchase_events = df[df['event_name'].astype(str) == 'purchase']
        logger.info(f"找到 {len(purchase_events)} 筆 'purchase' 事件")
        if not purchase_events.empty:
            logger.info(f"purchase 事件樣本: \n{purchase_events.head().to_string()}")

    return user_stats

def process_user_subset_with_df(users_subset, df_slice, ec_id, time_column, purchase_column=None, price_column=None):
    """
    處理用戶數據子集 - 直接使用 DataFrame 版本

    這個函數處理特定子集的用戶數據，使用 DataFrame 切片而不是字典。
    它計算用戶的統計數據，例如第一次和最後一次交互時間、會話數、購買數等。

    參數:
        users_subset (list): 要處理的用戶 ID 清單
        df_slice (pandas.DataFrame): 包含用戶數據的 DataFrame 切片
        ec_id (int): 電商 ID
        time_column (str): 時間列的名稱
        purchase_column (str, optional): 購買事件標記列的名稱
        price_column (str, optional): 價格列的名稱

    返回:
        pandas.DataFrame: 包含處理後的用戶統計數據
    """
    import random
    import os
    import time
    from datetime import datetime, timedelta

    # 獲取當前進程ID並設置隨機種子
    pid = os.getpid()
    random.seed(pid)

    # 記錄開始處理
    start_time = time.time()
    logger.info(f"[PID {pid}] 開始處理 {len(users_subset)} 個用戶的數據（使用 DataFrame 切片）")

    # 檢查 DataFrame 切片是否已經為這些用戶過濾了數據
    user_count_in_slice = df_slice['permanent'].nunique()
    if user_count_in_slice != len(users_subset):
        logger.warning(f"[PID {pid}] DataFrame 切片中有 {user_count_in_slice} 個用戶, 但需要處理 {len(users_subset)} 個用戶")

    # 記錄 DataFrame 的大小和列類型
    logger.info(f"[PID {pid}] DataFrame 切片大小: {df_slice.shape[0]} 行, {df_slice.shape[1]} 列")
    logger.info(f"[PID {pid}] DataFrame 中的唯一用戶數: {user_count_in_slice}")
    logger.info(f"[PID {pid}] DataFrame 列數據類型: {df_slice.dtypes.to_dict()}")
    logger.info(f"[PID {pid}] 使用的時間列: {time_column}")

    # 獲取時間範圍
    try:
        min_time = df_slice[time_column].min()
        max_time = df_slice[time_column].max()
        logger.info(f"[PID {pid}] 數據時間範圍: {min_time} 到 {max_time}")
    except Exception as e:
        logger.error(f"[PID {pid}] 獲取時間範圍時出錯: {e}")
        raise

    # 使用向量化操作計算基本時間統計
    try:
        # 按用戶分組找出第一次和最後一次交互時間
        time_stats = df_slice.groupby('permanent')[time_column].agg(['min', 'max']).reset_index()
        time_stats.columns = ['permanent', 'first_interaction_time', 'last_interaction_time']
        logger.info(f"[PID {pid}] 成功計算了 {len(time_stats)} 個用戶的時間統計數據")
    except Exception as e:
        logger.error(f"[PID {pid}] 計算時間統計數據時出錯: {e}")
        raise

    # 初始化用戶統計 DataFrame
    user_stats = pd.DataFrame(time_stats)

    # 計算會話數
    if 'session_id' in df_slice.columns:
        logger.info(f"[PID {pid}] 使用 session_id 計算會話數")
        try:
            # 按用戶和會話ID分組計算唯一會話數
            session_counts = df_slice.groupby('permanent')['session_id'].nunique().reset_index()
            session_counts.columns = ['permanent', 'total_sessions']
            logger.info(f"[PID {pid}] 成功計算了 {len(session_counts)} 個用戶的會話數")

            # 合併會話數到用戶統計中
            user_stats = user_stats.merge(session_counts, on='permanent', how='left')
        except Exception as e:
            logger.error(f"[PID {pid}] 計算會話數時出錯: {e}")
            # 如果計算會話數失敗, 使用時間間隔方法作為後備
            logger.info(f"[PID {pid}] 改用時間間隔方法估算會話數")
            user_stats['total_sessions'] = 1  # 默認至少有一個會話
    else:
        logger.info(f"[PID {pid}] session_id 欄位不存在, 使用時間間隔方法估算會話數")
        try:
            # 按時間排序
            df_sorted = df_slice.sort_values(by=[time_column])

            # 按用戶分組, 計算時間差
            session_counts = []
            for user in users_subset:
                user_data = df_sorted[df_sorted['permanent'] == user].copy()
                if len(user_data) > 0:
                    # 計算時間差
                    user_data['time_diff'] = user_data[time_column].diff()
                    # 設置會話超時時間（30分鐘）
                    session_timeout = pd.Timedelta(minutes=30)
                    # 計算會話開始（第一個事件或時間差大於超時的事件）
                    session_starts = (user_data['time_diff'].isnull()) | (user_data['time_diff'] > session_timeout)
                    # 計算總會話數
                    total_sessions = session_starts.sum()
                    session_counts.append({'permanent': user, 'total_sessions': total_sessions})
                else:
                    # 如果沒有數據, 則設為0
                    session_counts.append({'permanent': user, 'total_sessions': 0})

            # 轉換為 DataFrame
            session_counts_df = pd.DataFrame(session_counts)
            logger.info(f"[PID {pid}] 成功估算了 {len(session_counts_df)} 個用戶的會話數")

            # 合併會話數到用戶統計中
            user_stats = user_stats.merge(session_counts_df, on='permanent', how='left')
        except Exception as e:
            logger.error(f"[PID {pid}] 使用時間間隔方法估算會話數時出錯: {e}")
            user_stats['total_sessions'] = 1  # 默認至少有一個會話

    # 確保 total_sessions 列已填充
    user_stats['total_sessions'] = user_stats['total_sessions'].fillna(1)
    logger.info(f"[PID {pid}] 總會話數: {user_stats['total_sessions'].sum()}")

    # 處理購買數據
    if purchase_column is not None and purchase_column in df_slice.columns:
        logger.info(f"[PID {pid}] 處理購買數據, 使用列: {purchase_column}")
        try:
            # 如果購買列不是布爾型, 則轉換為布爾型
            if df_slice[purchase_column].dtype != bool:
                purchase_events = df_slice[df_slice[purchase_column] == 'purchase'].copy()
            else:
                purchase_events = df_slice[df_slice[purchase_column]].copy()

            logger.info(f"[PID {pid}] 找到 {len(purchase_events)} 個購買事件")

            # 計算每個用戶的購買統計數據
            if len(purchase_events) > 0:
                # 計算購買數量
                purchase_counts = purchase_events.groupby('permanent').size().reset_index(name='purchase_count')

                # 計算第一次和最後一次購買時間
                purchase_times = purchase_events.groupby('permanent')[time_column].agg(['min', 'max']).reset_index()
                purchase_times.columns = ['permanent', 'first_purchase_time', 'last_purchase_time']

                # 如果提供了價格列, 則計算總金額
                if price_column is not None and price_column in df_slice.columns:
                    purchase_amounts = purchase_events.groupby('permanent')[price_column].sum().reset_index(name='total_purchase_amount')

                    # 合併所有購買統計數據
                    purchase_stats = purchase_counts.merge(purchase_times, on='permanent', how='left')
                    purchase_stats = purchase_stats.merge(purchase_amounts, on='permanent', how='left')
                else:
                    # 合併購買數量和時間
                    purchase_stats = purchase_counts.merge(purchase_times, on='permanent', how='left')
                    purchase_stats['total_purchase_amount'] = np.nan

                # 合併購買統計數據到用戶統計中
                user_stats = user_stats.merge(purchase_stats, on='permanent', how='left')
            else:
                # 如果沒有購買事件, 則設為0或NaT
                user_stats['purchase_count'] = 0
                user_stats['first_purchase_time'] = pd.NaT
                user_stats['last_purchase_time'] = pd.NaT
                user_stats['total_purchase_amount'] = 0.0
        except Exception as e:
            logger.error(f"[PID {pid}] 處理購買數據時出錯: {e}")
            user_stats['purchase_count'] = 0
            user_stats['first_purchase_time'] = pd.NaT
            user_stats['last_purchase_time'] = pd.NaT
            user_stats['total_purchase_amount'] = 0.0
    else:
        # 如果沒有購買列, 則設為0或NaT
        logger.info(f"[PID {pid}] 沒有購買數據列")
        user_stats['purchase_count'] = 0
        user_stats['first_purchase_time'] = pd.NaT
        user_stats['last_purchase_time'] = pd.NaT
        user_stats['total_purchase_amount'] = 0.0

    # 處理註冊時間
    if 'event.name' in df_slice.columns:
        logger.info(f"[PID {pid}] 處理註冊事件")
        try:
            # 查看事件類型分佈
            event_distribution = df_slice['event.name'].value_counts()
            logger.info(f"[PID {pid}] 事件類型分佈: {dict(event_distribution)}")

            # 過濾註冊事件
            register_events = df_slice[df_slice['event.name'] == 'register'].copy()

            if len(register_events) > 0:
                # 找出每個用戶的最早註冊時間
                register_times = register_events.groupby('permanent')[time_column].min().reset_index()
                register_times.columns = ['permanent', 'registration_time']

                # 合併註冊時間到用戶統計中
                user_stats = user_stats.merge(register_times, on='permanent', how='left')
            else:
                user_stats['registration_time'] = pd.NaT
        except Exception as e:
            logger.error(f"[PID {pid}] 處理註冊時間時出錯: {e}")
            user_stats['registration_time'] = pd.NaT
    else:
        user_stats['registration_time'] = pd.NaT

    # 添加時間戳
    current_time = datetime.now()
    user_stats['created_at'] = current_time
    user_stats['updated_at'] = current_time

    # 添加 EC ID
    user_stats['ec_id'] = ec_id

    logger.info(f"[PID {pid}] 完成向量化計算")

    try:
        # 計算處理速度
        total_rows = df_slice.shape[0]
        total_users = len(users_subset)
        processing_time = time.time() - start_time
        logger.info(f"[PID {pid}] 處理了 {total_rows} 行數據, {total_users} 個用戶, 耗時 {processing_time:.2f} 秒")
        logger.info(f"[PID {pid}] 處理速度: {total_rows/processing_time:.1f} 行/秒, {total_users/processing_time:.1f} 用戶/秒")
    except Exception as e:
        logger.error(f"[PID {pid}] 計算處理速度時出錯: {e}")

    # 標準化輸出列和類型
    try:
        # 確保數值字段為數值類型
        for col in ['total_sessions', 'purchase_count', 'total_purchase_amount']:
            if col in user_stats.columns:
                user_stats[col] = pd.to_numeric(user_stats[col], errors='coerce')

        # 確保只返回可用的列
        available_columns = [
            'ec_id', 'permanent', 'total_sessions',
            'registration_time', 'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'purchase_count', 'total_purchase_amount',
            'created_at', 'updated_at'
        ]
        output_columns = [col for col in available_columns if col in user_stats.columns]
        result_df = user_stats[output_columns].copy()

        logger.info(f"[PID {pid}] 返回 DataFrame 大小: {result_df.shape[0]} 行, {result_df.shape[1]} 列")
    except Exception as e:
        logger.error(f"[PID {pid}] 標準化輸出時出錯: {e}")
        raise

    logger.info(f"[PID {pid}] 完成處理 {len(users_subset)} 個用戶, 總耗時 {time.time() - start_time:.2f} 秒")
    return result_df

# 定義一個專門處理 DataFrame 切片的函數，放在 process_user_chunk_parallel 外部
def process_dataframe_slice(args):
    """
    處理 DataFrame 切片的函數（用於多進程處理）

    參數:
        args: tuple，包含 (user_subset, df_chunk, ec_id, time_column, purchase_column, price_column)

    返回:
        DataFrame: 處理後的用戶統計結果
    """
    user_subset, df_chunk, ec_id, time_column, purchase_column, price_column = args
    # 篩選出該用戶子集的數據
    user_df_slice = df_chunk[df_chunk['permanent'].isin(user_subset)].copy()
    # 調用原處理函數，但傳遞 DataFrame 而非字典
    return process_user_subset_with_df(user_subset, user_df_slice, ec_id, time_column, purchase_column, price_column)

def process_user_chunk_parallel(df_chunk, ec_id, n_workers=None):
    """
    使用並行處理方式處理大量用戶數據

    Args:
        df_chunk: 數據分片
        ec_id: 電商ID
        n_workers: 工作進程數

    Returns:
        處理後的用戶統計 DataFrame
    """
    # 確保使用全局函數
    global process_user_subset

    if n_workers is None:
        n_workers = mp.cpu_count()

    start_time = time.time()
    logger.info(f"[並行處理] 開始處理 {len(df_chunk):,} 筆數據，使用 {n_workers} 個工作進程...")
    logger.info(f"[並行處理] 數據包含 {df_chunk['permanent'].nunique():,} 個唯一用戶")

    # 檢查DataFrame結構並記錄列名
    logger.info(f"[並行處理] DataFrame 列名: {', '.join(df_chunk.columns)}")
    logger.info(f"[並行處理] DataFrame 類型: \n{df_chunk.dtypes}")
    logger.info(f"[並行處理] DataFrame 前 5 行: \n{df_chunk.head().to_string()}")

    # 確定時間列名稱
    time_column = None
    if 'interaction_time' in df_chunk.columns:
        time_column = 'interaction_time'
    elif 'event_time' in df_chunk.columns:
        time_column = 'event_time'
    else:
        for col in df_chunk.columns:
            if 'time' in col.lower():
                logger.info(f"[並行處理] 使用 {col} 作為時間列")
                time_column = col
                break

    if time_column is None:
        logger.error("[並行處理] 找不到時間列，無法進行處理")
        raise ValueError("DataFrame中沒有找到時間列，請檢查數據結構")

    # 檢查購買確認欄位
    purchase_column = None
    if 'purchase_confirmation' in df_chunk.columns:
        purchase_column = 'purchase_confirmation'
        logger.info(f"[並行處理] 使用 'purchase_confirmation' 作為購買標記列")
    elif 'event_name' in df_chunk.columns:
        # 創建臨時購買確認欄位
        df_chunk['purchase_confirmation'] = df_chunk['event_name'].astype(str) == 'purchase'
        purchase_column = 'purchase_confirmation'
        logger.info(f"[並行處理] 從 'event_name' 創建了購買標記列")

        # 記錄購買事件數量
        purchase_events = df_chunk[df_chunk['purchase_confirmation'] == True]
        logger.info(f"[並行處理] 檢測到 {len(purchase_events)} 個購買事件")

    # 檢查訂單價格欄位
    price_column = None
    if 'order_price' in df_chunk.columns:
        price_column = 'order_price'
        logger.info(f"[並行處理] 使用 'order_price' 作為價格列")
    elif 'event_value' in df_chunk.columns:
        price_column = 'event_value'
        logger.info(f"[並行處理] 使用 'event_value' 作為價格列")

    logger.info(f"[並行處理] 時間列={time_column}, 購買列={purchase_column}, 價格列={price_column}")

    # 確保類型轉換
    logger.info("[並行處理] 進行數據類型轉換...")
    if time_column:
        logger.info(f"[並行處理] 轉換時間列 {time_column}")
        df_chunk = standardize_datetime_columns(df_chunk, [time_column])
    if 'permanent' in df_chunk.columns:
        logger.info("[並行處理] 轉換 permanent 列為字符串類型")
        df_chunk['permanent'] = df_chunk['permanent'].astype(str)
    if purchase_column:
        logger.info(f"[並行處理] 轉換購買列 {purchase_column} 為布爾類型")
        df_chunk[purchase_column] = df_chunk[purchase_column].astype(bool)
    if price_column:
        logger.info(f"[並行處理] 轉換價格列 {price_column} 為浮點類型")
        df_chunk[price_column] = df_chunk[price_column].fillna(0).astype(float)

    # 按照 permanent 劃分數據塊
    unique_users = df_chunk['permanent'].unique()
    user_chunks = np.array_split(unique_users, n_workers)
    logger.info(f"[並行處理] 將 {len(unique_users)} 個用戶分成 {len(user_chunks)} 個塊進行處理")

    # 檢查每個塊的大小
    for i, chunk in enumerate(user_chunks):
        logger.info(f"[並行處理] 塊 {i+1} 包含 {len(chunk)} 個用戶")

    # 將 DataFrame 轉換為字典，以便於在進程間傳遞
    # 這樣可以解決 DataFrame 序列化問題
    try:
        start_dict_time = time.time()

        # 根據數據規模決定使用哪種方法傳遞數據
        use_dictionary = True  # 默認使用字典方法

        # 如果數據量較小，可以考慮直接傳遞 DataFrame 切片
        if len(df_chunk) < 1_000_000:  # 數據量小於 100 萬時
            use_dictionary = False
            logger.info("[並行處理] 數據量較小，嘗試直接傳遞 DataFrame 切片而非轉換字典")

        if use_dictionary:
            # 優化字典轉換 - 僅包含必要欄位以減少記憶體使用
            necessary_columns = [
                'permanent',
                time_column,
                purchase_column,
                price_column
            ]

            # 添加會話、事件和其他有用欄位
            additional_columns = ['event_name', 'session_id']
            for col in additional_columns:
                if col in df_chunk.columns:
                    necessary_columns.append(col)
                    logger.info(f"[並行處理] 額外添加欄位: {col}")

            # 只轉換必要欄位
            df_dict = {col: df_chunk[col].to_list() for col in necessary_columns if col in df_chunk.columns}

            # 添加 ec_id 作為標量而非列表，節省記憶體
            if 'ec_id' not in df_dict and 'ec_id' in df_chunk.columns:
                # 檢查是否所有值都相同
                unique_ec_ids = df_chunk['ec_id'].unique()
                if len(unique_ec_ids) == 1:
                    # 如果只有一個唯一值，作為標量存儲
                    df_dict['ec_id'] = int(unique_ec_ids[0])
                    logger.info(f"[並行處理] ec_id 作為標量 {df_dict['ec_id']} 儲存，節省記憶體")
                else:
                    # 否則仍作為列表
                    df_dict['ec_id'] = df_chunk['ec_id'].to_list()

            dict_time = time.time() - start_dict_time
            logger.info(f"[並行處理] 將 DataFrame 轉換為字典，耗時 {dict_time:.2f} 秒")
            logger.info(f"[並行處理] 優化後字典欄位: {list(df_dict.keys())}")

            # 更準確地估算字典大小
            total_items = sum(len(value) if isinstance(value, list) else 1 for value in df_dict.values())
            estimated_size_mb = total_items * 8 / (1024 * 1024)  # 8 bytes per item average
            logger.info(f"[並行處理] 估計字典大小約 {estimated_size_mb:.2f} MB (優化後)")
            logger.info(f"[並行處理] 字典包含 {len(df_dict)} 個欄位的數據")
        else:
            # 使用 DataFrame 切片而非字典
            df_dict = None  # 不使用字典
            dict_time = time.time() - start_dict_time
            logger.info(f"[並行處理] 使用 DataFrame 切片而非字典，準備耗時 {dict_time:.2f} 秒")
    except Exception as e:
        logger.error(f"[並行處理] 數據準備時出錯: {str(e)}")
        # 確保 df_dict 有定義
        df_dict = df_chunk.to_dict('list')

    # 記錄使用的是全局 process_user_subset 函數
    logger.info(f"[並行處理] 使用全局定義的 process_user_subset 函數處理，函數ID: {id(process_user_subset)}")

    try:
        # 使用進程池進行並行處理
        logger.info("[並行處理] 啟動進程池...")
        pool_start_time = time.time()
        with mp.Pool(processes=n_workers) as pool:
            # 使用 starmap 傳遞多個參數
            logger.info("[並行處理] 開始並行處理任務...")

            # 根據使用的數據傳遞方式選擇相應的處理方法
            if use_dictionary:
                # 使用字典方式傳遞數據
                logger.info("[並行處理] 使用字典方式傳遞數據給子進程")
                results = pool.starmap(
                    process_user_subset,
                    [(chunk, df_dict, ec_id, time_column, purchase_column, price_column)
                     for chunk in user_chunks]
                )
            else:
                # 使用 DataFrame 切片方式傳遞數據
                logger.info("[並行處理] 使用 DataFrame 切片方式傳遞數據給子進程")

                # 準備參數列表
                process_args = [(chunk, df_chunk, ec_id, time_column, purchase_column, price_column)
                               for chunk in user_chunks]

                # 映射到進程池
                results = pool.map(process_dataframe_slice, process_args)

        pool_time = time.time() - pool_start_time
        logger.info(f"[並行處理] 並行處理完成，耗時 {pool_time:.2f} 秒")

        # 記錄每個結果的大小
        for i, result in enumerate(results):
            logger.info(f"[並行處理] 結果 {i+1} 包含 {len(result)} 行")

        # 合併結果
        logger.info("[並行處理] 合併處理結果...")
        merge_start_time = time.time()
        combined_results = pd.concat(results, ignore_index=True)
        merge_time = time.time() - merge_start_time
        logger.info(f"[並行處理] 合併完成，耗時 {merge_time:.2f} 秒，總結果包含 {len(combined_results)} 行")

        total_time = time.time() - start_time
        # 添加更詳細的效能統計
        users_count = combined_results['permanent'].nunique()
        rows_per_second = len(df_chunk) / total_time
        users_per_second = users_count / total_time

        logger.info(f"[並行處理] 總處理時間: {total_time:.2f} 秒")
        logger.info(f"[並行處理] 處理速度: {rows_per_second:.1f} 筆/秒, {users_per_second:.1f} 用戶/秒")

        # 檢查關鍵統計
        if 'total_sessions' in combined_results.columns:
            total_sessions = combined_results['total_sessions'].sum()
            avg_sessions = combined_results['total_sessions'].mean()
            logger.info(f"[並行處理] 總計 {total_sessions:,} 個會話，平均每用戶 {avg_sessions:.2f} 個")

        if 'purchase_count' in combined_results.columns:
            total_purchases = combined_results['purchase_count'].sum()
            purchase_users = (combined_results['purchase_count'] > 0).sum()
            logger.info(f"[並行處理] 總計 {total_purchases:,} 個購買事件，{purchase_users} 個購買用戶 ({purchase_users/len(combined_results)*100:.2f}%)")

        # 添加進程分配統計
        for i, result in enumerate(results):
            logger.info(f"[並行處理] 進程 {i+1} 處理了 {len(result)} 用戶 ({len(result)/len(combined_results)*100:.2f}%)")

        return combined_results
    except Exception as e:
        logger.error(f"[並行處理] 處理時發生錯誤: {str(e)}")
        # 如果是序列化或多處理相關錯誤，嘗試使用非並行處理方式
        logger.warning("[並行處理] 嘗試使用非並行處理方式...")

        # 非並行處理方式，直接在主進程中處理所有數據
        fallback_start_time = time.time()
        all_results = []
        for i, chunk in enumerate(user_chunks):
            logger.info(f"[並行處理-回退] 處理塊 {i+1}/{len(user_chunks)}...")
            chunk_start_time = time.time()
            result = process_user_subset(chunk, df_dict, ec_id, time_column, purchase_column, price_column)
            chunk_time = time.time() - chunk_start_time
            logger.info(f"[並行處理-回退] 塊 {i+1} 處理完成，耗時 {chunk_time:.2f} 秒，結果包含 {len(result)} 行")
            all_results.append(result)

        logger.info("[並行處理-回退] 合併處理結果...")
        combined_results = pd.concat(all_results, ignore_index=True)
        fallback_time = time.time() - fallback_start_time
        logger.info(f"[並行處理-回退] 使用非並行處理完成，耗時 {fallback_time:.2f} 秒")

        return combined_results

def get_user_data_from_parquet(parquet_path, batch_size=10000, num_workers=4):
    """從 Parquet 檔案中獲取數據

    Args:
        parquet_path: Parquet 檔案路徑
        batch_size: 批次大小
        num_workers: 工作進程數

    Returns:
        pd.DataFrame: 用戶數據
    """
    logger.info(f"從 Parquet 檔案獲取用戶數據: {parquet_path}")

    # 檢查文件是否存在
    if not os.path.exists(parquet_path):
        logger.error(f"文件不存在: {parquet_path}")
        return None

    try:
        # 使用 pandas 直接讀取 parquet 文件
        logger.info("讀取 Parquet 檔案...")
        user_data = pd.read_parquet(parquet_path)
        logger.info(f"成功讀取 {len(user_data)} 行數據")
        return user_data
    except Exception as e:
        logger.error(f"讀取 Parquet 檔案時發生錯誤: {str(e)}")
        return None

def estimate_query_size(ec_id, time_range=None):
    """
    高效估算特定 EC ID 和時間範圍內的資料數量

    Args:
        ec_id: 電商平台 ID
        time_range: 時間範圍元組 (start_date, end_date)，UTC timezone

    Returns:
        估計的資料筆數和預估處理時間
    """
    # 構建時間範圍條件（轉換為分區格式）
    partition_filter = ""
    if time_range:
        start_date, end_date = time_range
        # 轉換為分區 ID 格式 (YYYYMMDD)
        start_partition = start_date.strftime('%Y%m%d')
        end_partition = end_date.strftime('%Y%m%d')
        partition_filter = f"AND partition_id BETWEEN '{start_partition}' AND '{end_partition}'"

    # 查詢指定 EC ID 和時間範圍的資料統計
    # 這個查詢同時利用了分區和叢集的統計資訊，非常高效
    cluster_query = f"""
    SELECT
        table_name,
        partition_id,
        total_logical_bytes,
        total_rows
    FROM
        `tagtoo-tracking.event_prod.INFORMATION_SCHEMA.PARTITIONS`
    WHERE
        table_name = 'tagtoo_event'
        {partition_filter}
    ORDER BY
        partition_id
    """

    logger.info(f"使用 INFORMATION_SCHEMA 估算 ec_id={ec_id} 的資料量...")

    try:
        client = bigquery.Client()

        # 查詢分區統計
        cluster_job = client.query(cluster_query)
        cluster_results = list(cluster_job.result())

        # 計算總行數
        total_partition_rows = sum(row.total_rows for row in cluster_results if row.total_rows is not None)

        # 進行一個小型抽樣查詢來估計特定 EC ID 的比例
        sample_query = f"""
        SELECT
            COUNT(*) AS total_samples,
            COUNTIF(ec_id = {ec_id}) AS ec_samples
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE
            RAND() < 0.0001 -- 只抽樣 0.01% 資料
            {f"AND event_time BETWEEN TIMESTAMP('{time_range[0].isoformat()}') AND TIMESTAMP('{time_range[1].isoformat()}')" if time_range else ""}
        """

        logger.info("執行小型抽樣查詢以估計特定 EC ID 的佔比...")
        sample_job = client.query(sample_query)
        sample_result = list(sample_job.result())[0]

        # 計算 EC ID 的佔比
        if sample_result.total_samples > 0 and sample_result.ec_samples > 0:
            ec_ratio = sample_result.ec_samples / sample_result.total_samples
            logger.info(f"從抽樣結果看，ec_id={ec_id} 佔總資料的 {ec_ratio*100:.4f}%")
        else:
            # 如果抽樣為零，嘗試更多資料
            logger.warning("抽樣結果為零或太小，增加抽樣量再試一次...")
            larger_sample_query = f"""
            SELECT
                COUNT(*) AS total_samples,
                COUNTIF(ec_id = {ec_id}) AS ec_samples
            FROM
                `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE
                RAND() < 0.001 -- 增加到 0.1% 抽樣
                {f"AND event_time BETWEEN TIMESTAMP('{time_range[0].isoformat()}') AND TIMESTAMP('{time_range[1].isoformat()}')" if time_range else ""}
            """

            larger_sample_job = client.query(larger_sample_query)
            larger_result = list(larger_sample_job.result())[0]

            if larger_result.total_samples > 0 and larger_result.ec_samples > 0:
                ec_ratio = larger_result.ec_samples / larger_result.total_samples
                logger.info(f"從較大抽樣結果看，ec_id={ec_id} 佔總資料的 {ec_ratio*100:.4f}%")
            else:
                # 如果仍然沒有結果，進行直接計數
                logger.warning("抽樣仍未找到匹配資料，進行直接計數...")
                direct_query = f"""
                SELECT COUNT(*) AS direct_count
                FROM `tagtoo-tracking.event_prod.tagtoo_event`
                WHERE ec_id = {ec_id}
                {f"AND event_time BETWEEN TIMESTAMP('{time_range[0].isoformat()}') AND TIMESTAMP('{time_range[1].isoformat()}')" if time_range else ""}
                """
                direct_job = client.query(direct_query)
                direct_result = list(direct_job.result())[0]
                estimated_rows = direct_result.direct_count

                logger.info(f"直接計數結果: ec_id={ec_id} 有 {estimated_rows:,} 筆資料")

                # 估算處理時間
                processing_rate = 5000  # 每秒處理筆數（根據實際性能調整）
                estimated_time = estimated_rows / processing_rate if estimated_rows > 0 else 1

                return estimated_rows, estimated_time

        # 估算特定 EC ID 的行數
        estimated_rows = max(int(total_partition_rows * ec_ratio), 1)

        # 處理極小值情況
        if estimated_rows < 100:
            # 進行一次直接計數確認
            logger.info("估計資料量很小，進行直接計數確認...")
            exact_query = f"""
            SELECT COUNT(*) AS exact_count
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ec_id = {ec_id}
            {f"AND event_time BETWEEN TIMESTAMP('{time_range[0].isoformat()}') AND TIMESTAMP('{time_range[1].isoformat()}')" if time_range else ""}
            """
            exact_job = client.query(exact_query)
            exact_result = list(exact_job.result())[0]
            estimated_rows = exact_result.exact_count
            logger.info(f"直接計數結果: {estimated_rows} 筆資料")

        # 估算處理時間
        processing_rate = 5000  # 每秒處理筆數（根據實際性能調整）
        estimated_time = estimated_rows / processing_rate if estimated_rows > 0 else 1

        logger.info(f"估計 ec_id={ec_id} 在指定時間範圍內有約 {estimated_rows:,} 筆資料")
        logger.info(f"預估處理時間: {estimated_time:.1f} 秒 (約 {estimated_time/60:.1f} 分鐘)")

        return estimated_rows, estimated_time

    except Exception as e:
        logger.error(f"估算資料量時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return 1000000, 200  # 預設值：100萬筆，200秒

def display_data_summary(data):
    """顯示數據摘要"""
    try:
        logger.info("=" * 40)
        logger.info("數據摘要")
        logger.info("=" * 40)

        # 數據基本信息
        logger.info(f"總行數: {len(data)}")
        logger.info(f"列數: {len(data.columns)}")
        logger.info(f"列名: {', '.join(data.columns)}")

        # 用戶統計摘要
        logger.info("-" * 40)
        logger.info("用戶統計摘要")
        logger.info("-" * 40)

        # 檢查 ec_id 情況
        if 'ec_id' in data.columns:
            ec_ids = data['ec_id'].unique()
            if len(ec_ids) == 1:
                logger.info(f"單一 EC ID: {ec_ids[0]}")
            else:
                logger.info(f"包含 {len(ec_ids)} 個不同的 EC ID")
                if len(ec_ids) <= 10:
                    logger.info(f"EC ID 列表: {', '.join(str(ec_id) for ec_id in ec_ids)}")

        # 檢查用戶數量
        if 'permanent' in data.columns:
            user_count = data['permanent'].nunique()
            logger.info(f"總用戶數: {user_count}")

        # 檢查會話數量
        if 'total_sessions' in data.columns:
            total_sessions = data['total_sessions'].sum()
            avg_sessions = total_sessions / len(data) if len(data) > 0 else 0
            logger.info(f"總會話數: {total_sessions}")
            logger.info(f"平均每用戶會話數: {avg_sessions:.2f}")

        # 檢查購買情況
        if 'purchase_count' in data.columns:
            purchasers = data[data['purchase_count'] > 0]
            purchaser_count = len(purchasers)
            purchase_rate = purchaser_count / len(data) * 100 if len(data) > 0 else 0
            logger.info(f"購買用戶數: {purchaser_count} ({purchase_rate:.2f}%)")
            logger.info(f"總購買次數: {data['purchase_count'].sum()}")

        # 檢查購買金額
        if 'total_purchase_amount' in data.columns:
            total_amount = data['total_purchase_amount'].sum()
            if purchaser_count > 0:
                avg_purchase = total_amount / purchaser_count
                logger.info(f"總購買金額: {total_amount:.2f}")
                logger.info(f"平均每位購買用戶的消費金額: {avg_purchase:.2f}")

        # 檢查時間分佈
        if 'first_interaction_time' in data.columns:
            # 篩選出非空的時間值
            valid_first_times = [t for t in data['first_interaction_time'] if pd.notna(t)]
            if valid_first_times:
                first_time = min(valid_first_times)
                logger.info(f"最早互動時間: {first_time}")
            else:
                logger.info("最早互動時間: 無有效數據")

        if 'last_interaction_time' in data.columns:
            # 篩選出非空的時間值
            valid_last_times = [t for t in data['last_interaction_time'] if pd.notna(t)]
            if valid_last_times:
                last_time = max(valid_last_times)
                logger.info(f"最晚互動時間: {last_time}")
            else:
                logger.info("最晚互動時間: 無有效數據")

        # 檢查購買時間分佈
        if 'first_purchase_time' in data.columns:
            valid_first_purchase_times = [t for t in data['first_purchase_time'] if pd.notna(t)]
            if valid_first_purchase_times:
                first_purchase_time = min(valid_first_purchase_times)
                logger.info(f"最早購買時間: {first_purchase_time}")
            else:
                logger.info("最早購買時間: 無購買資料")

        if 'last_purchase_time' in data.columns:
            valid_last_purchase_times = [t for t in data['last_purchase_time'] if pd.notna(t)]
            if valid_last_purchase_times:
                last_purchase_time = max(valid_last_purchase_times)
                logger.info(f"最晚購買時間: {last_purchase_time}")
            else:
                logger.info("最晚購買時間: 無購買資料")

        # 檢查註冊時間分佈
        if 'registration_time' in data.columns:
            valid_reg_times = [t for t in data['registration_time'] if pd.notna(t)]
            if valid_reg_times:
                first_reg_time = min(valid_reg_times)
                last_reg_time = max(valid_reg_times)
                reg_count = sum(data['registration_time'].notna())
                logger.info(f"註冊用戶數: {reg_count} ({reg_count/len(data)*100:.2f}%)")
                logger.info(f"最早註冊時間: {first_reg_time}")
                logger.info(f"最晚註冊時間: {last_reg_time}")
            else:
                logger.info("註冊用戶數: 0 (0.00%)")
                logger.info("註冊時間: 無註冊資料")

        # 檢查購物車行為
        if 'first_add_to_cart_time' in data.columns:
            cart_users = data[data['first_add_to_cart_time'].notna()]
            if not cart_users.empty:
                cart_user_count = len(cart_users)
                first_cart_time = cart_users['first_add_to_cart_time'].min()
                last_cart_time = cart_users['last_add_to_cart_time'].max()
                logger.info(f"加入購物車用戶數: {cart_user_count} ({cart_user_count/len(data)*100:.2f}%)")
                logger.info(f"最早加入購物車時間: {first_cart_time}")
                logger.info(f"最晚加入購物車時間: {last_cart_time}")
            else:
                logger.info("加入購物車用戶數: 0 (0.00%)")
                logger.info("購物車行為: 無資料")

        # 檢查商品瀏覽行為
        if 'first_view_item_time' in data.columns:
            view_item_users = data[data['first_view_item_time'].notna()]
            if not view_item_users.empty:
                view_item_count = len(view_item_users)
                first_view_time = view_item_users['first_view_item_time'].min()
                last_view_time = view_item_users['last_view_item_time'].max()
                logger.info(f"瀏覽商品用戶數: {view_item_count} ({view_item_count/len(data)*100:.2f}%)")
                logger.info(f"最早瀏覽商品時間: {first_view_time}")
                logger.info(f"最晚瀏覽商品時間: {last_view_time}")
            else:
                logger.info("瀏覽商品用戶數: 0 (0.00%)")
                logger.info("商品瀏覽行為: 無資料")

        logger.info("=" * 40)
    except Exception as e:
        logger.error(f"顯示數據摘要時發生錯誤: {str(e)}")
        logger.exception(e)

def calculate_user_stats_via_bigquery(ec_id, time_range=None, query_job_config=None, force_query=False):
    """
    直接使用 BigQuery 計算用戶統計數據

    Args:
        ec_id: 電商 ID
        time_range: 可選，時間範圍 [start_date, end_date]
        query_job_config: 可選，查詢配置
        force_query: 是否強制執行查詢，忽略成本警告

    Returns:
        pandas.DataFrame: 包含用戶統計數據的 DataFrame
    """
    logger.info(f"準備在 BigQuery 中直接計算 EC ID {ec_id} 的用戶統計數據...")

    # 構建時間範圍條件
    time_condition = ""
    if time_range:
        start_date, end_date = time_range
        # 確保時間格式正確 (UTC)
        start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S')
        logger.info(f"使用時間範圍: {start_date_str} 到 {end_date_str} (UTC)")
        time_condition = f"""
            AND event_time BETWEEN TIMESTAMP('{start_date_str}') AND TIMESTAMP('{end_date_str}')
        """

    # 構建計算用戶統計的 BigQuery SQL 查詢
    query = f"""
    WITH UserSessions AS (
        -- 計算用戶會話
        SELECT
            permanent,
            COUNT(DISTINCT session.id) AS total_sessions
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
        GROUP BY permanent
    ),

    UserInteractions AS (
        -- 計算用戶互動時間
        SELECT
            permanent,
            MIN(event_time) AS first_interaction_time,
            -- 如果只有一筆記錄，first_interaction_time 和 last_interaction_time 應該相同
            CASE
                WHEN COUNT(*) = 1 THEN MIN(event_time)
                ELSE MAX(event_time)
            END AS last_interaction_time
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
        GROUP BY permanent
    ),

    UserPurchases AS (
        -- 計算用戶購買記錄
        SELECT
            permanent,
            MIN(event_time) AS first_purchase_time,
            -- 如果只有一筆記錄，first_purchase_time 和 last_purchase_time 應該相同
            CASE
                WHEN COUNT(*) = 1 THEN MIN(event_time)
                ELSE MAX(event_time)
            END AS last_purchase_time,
            COUNT(*) AS purchase_count,
            SUM(event.value) AS total_purchase_amount
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
            AND event.name = 'purchase'
            AND event.value > 0
        GROUP BY permanent
    ),

    UserRegistrations AS (
        -- 計算用戶註冊時間
        SELECT
            permanent,
            MIN(event_time) AS registration_time
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
            AND event.name = 'register'
        GROUP BY permanent
    ),

    UserAddToCarts AS (
        -- 計算用戶加入購物車記錄
        SELECT
            permanent,
            MIN(event_time) AS first_add_to_cart_time,
            -- 如果只有一筆記錄，first_add_to_cart_time 和 last_add_to_cart_time 應該相同
            CASE
                WHEN COUNT(*) = 1 THEN MIN(event_time)
                ELSE MAX(event_time)
            END AS last_add_to_cart_time
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
            AND event.name = 'add_to_cart'
        GROUP BY permanent
    ),

    UserViewItems AS (
        -- 計算用戶查看商品記錄
        SELECT
            permanent,
            MIN(event_time) AS first_view_item_time,
            -- 如果只有一筆記錄，first_view_item_time 和 last_view_item_time 應該相同
            CASE
                WHEN COUNT(*) = 1 THEN MIN(event_time)
                ELSE MAX(event_time)
            END AS last_view_item_time
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE ec_id = {ec_id}
            {time_condition}
            AND permanent IS NOT NULL
            AND permanent != ''
            AND event.name = 'view_item'
        GROUP BY permanent
    )

    -- 最終合併所有用戶數據
    SELECT
        {ec_id} AS ec_id,
        i.permanent,
        COALESCE(s.total_sessions, 0) AS total_sessions,
        r.registration_time,
        i.first_interaction_time,
        i.last_interaction_time,
        p.first_purchase_time,
        p.last_purchase_time,
        a.first_add_to_cart_time,
        a.last_add_to_cart_time,
        c.first_view_item_time,
        c.last_view_item_time,
        COALESCE(p.purchase_count, 0) AS purchase_count,
        COALESCE(p.total_purchase_amount, 0) AS total_purchase_amount,
        CURRENT_TIMESTAMP() AS created_at,
        CURRENT_TIMESTAMP() AS updated_at
    FROM UserInteractions AS i
    LEFT JOIN UserSessions AS s ON i.permanent = s.permanent
    LEFT JOIN UserPurchases AS p ON i.permanent = p.permanent
    LEFT JOIN UserRegistrations AS r ON i.permanent = r.permanent
    LEFT JOIN UserAddToCarts AS a ON i.permanent = a.permanent
    LEFT JOIN UserViewItems AS c ON i.permanent = c.permanent
    """

    # 記錄開始時間
    start_time = time.time()

    # 預估查詢資料量和費用
    client = bigquery.Client()
    # 創建乾跑配置
    dry_run_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)

    try:
        # 執行乾跑查詢估算
        logger.info("正在預估 BigQuery 查詢資料量和費用...")
        dry_run_job = client.query(query, job_config=dry_run_config)

        # 獲取處理的字節數
        bytes_processed = dry_run_job.total_bytes_processed

        # 轉換為易讀的格式
        if bytes_processed < 1024:
            readable_size = f"{bytes_processed} bytes"
        elif bytes_processed < 1024 * 1024:
            readable_size = f"{bytes_processed / 1024:.2f} KB"
        elif bytes_processed < 1024 * 1024 * 1024:
            readable_size = f"{bytes_processed / (1024 * 1024):.2f} MB"
        elif bytes_processed < 1024 * 1024 * 1024 * 1024:
            readable_size = f"{bytes_processed / (1024 * 1024 * 1024):.2f} GB"
        else:
            readable_size = f"{bytes_processed / (1024 * 1024 * 1024):.2f} GB"

        # 估算費用 (以 GB 為單位，每 TB $5.00 美元，每 GB 約 NT$0.14649)
        gb_processed = bytes_processed / (1024 * 1024 * 1024)
        tb_processed = bytes_processed / (1024 * 1024 * 1024 * 1024)
        estimated_cost = tb_processed * 150.00  # NT$150.00 per TB

        # 輸出預估結果
        logger.info(f"預估處理資料量: {readable_size} ({bytes_processed:,} bytes)")
        logger.info(f"預估處理資料量: {gb_processed:.6f} GB")
        logger.info(f"預估查詢費用: NT${estimated_cost:.2f}")

        # 如果費用超過閾值，顯示警告
        if estimated_cost > 150.00 and not force_query:
            logger.warning(f"注意: 此查詢預估費用為 NT${estimated_cost:.2f}，超過 NT$150.00")
            user_input = input("是否繼續執行此高成本查詢? (y/n): ")
            if user_input.lower() != 'y':
                logger.info("使用者取消了高成本查詢執行")
                # 返回空 DataFrame，但包含所有必要的列
                return pd.DataFrame(columns=[
                    'ec_id', 'permanent', 'total_sessions', 'registration_time',
                    'first_interaction_time', 'last_interaction_time',
                    'first_purchase_time', 'last_purchase_time',
                    'first_add_to_cart_time', 'last_add_to_cart_time',
                    'first_view_item_time', 'last_view_item_time',
                    'purchase_count', 'total_purchase_amount',
                    'created_at', 'updated_at'
                ])
        elif estimated_cost > 30.00:
            logger.warning(f"注意: 此查詢預估費用為 NT${estimated_cost:.2f}，超過 NT$30.00")

    except Exception as e:
        logger.error(f"預估查詢費用時發生錯誤: {str(e)}")
        logger.warning("無法估算查詢費用，將繼續執行查詢")

    # 執行查詢
    logger.info("開始在 BigQuery 中執行用戶統計計算...")
    results = execute_single_query(query, query_job_config)

    if results is None:
        logger.error("無法從 BigQuery 獲取用戶統計數據")
        # 返回空 DataFrame，但包含所有必要的列
        return pd.DataFrame(columns=[
            'ec_id', 'permanent', 'total_sessions', 'registration_time',
            'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'first_add_to_cart_time', 'last_add_to_cart_time',
            'first_view_item_time', 'last_view_item_time',
            'purchase_count', 'total_purchase_amount',
            'created_at', 'updated_at'
        ])

    # 轉換為 DataFrame
    logger.info("將 BigQuery 結果轉換為 DataFrame...")
    df = results.to_dataframe()

    # 檢查 DataFrame 是否為空
    if df.empty:
        logger.warning("BigQuery 查詢返回空結果")
        # 返回空 DataFrame，但包含所有必要的列
        return pd.DataFrame(columns=[
            'ec_id', 'permanent', 'total_sessions', 'registration_time',
            'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'first_add_to_cart_time', 'last_add_to_cart_time',
            'first_view_item_time', 'last_view_item_time',
            'purchase_count', 'total_purchase_amount',
            'created_at', 'updated_at'
        ])

    # 計算耗時
    elapsed_time = time.time() - start_time
    logger.info(f"BigQuery 計算完成，共 {len(df)} 筆用戶數據，耗時 {elapsed_time:.2f} 秒")

    # 顯示數據分布統計
    if 'purchase_count' in df.columns and len(df) > 0:
        purchase_users = (df['purchase_count'] > 0).sum()
        purchase_percentage = (purchase_users / len(df) * 100)
        logger.info(f"有購買記錄的用戶: {purchase_users} ({purchase_percentage:.2f}%)")

    if 'total_sessions' in df.columns and len(df) > 0:
        avg_sessions = df['total_sessions'].mean()
        logger.info(f"平均會話數: {avg_sessions:.2f}")

    # 檢查數據品質
    if 'permanent' in df.columns:
        null_permanents = df['permanent'].isnull().sum()
        if null_permanents > 0:
            logger.warning(f"發現 {null_permanents} 筆缺失 permanent ID 的記錄")

    return df

def main():
    """主函數"""
    # 解析命令列參數
    args = parse_args()

    # 配置日誌
    log_level = logging.INFO
    if args.verbose:
        log_level = logging.DEBUG
    elif args.quiet:
        log_level = logging.WARNING

    # 設置日誌格式
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s')

    # 輸出參數概覽
    logger.info(f"執行參數: ec_id={args.ec_id}, batch_size={args.batch_size}, verbose={args.verbose}")
    if args.max_rows:
        logger.info(f"限制數據量: max_rows={args.max_rows}")
    if args.output_path:
        logger.info(f"輸出路徑: {args.output_path}")

    # 設定輸出路徑
    if args.output_filename:
        output_basedir = 'init_snapshot'
        if not os.path.exists(output_basedir):
            os.makedirs(output_basedir)
        output_name = f"ec_{args.ec_id}_user_stats"
        logger.info(f"將使用指定的文件名: {args.output_filename}")
        output_path = f"{output_basedir}/{args.output_filename}"
    elif not args.output_path:
        output_basedir = 'init_snapshot'
        if not os.path.exists(output_basedir):
            os.makedirs(output_basedir)
        output_name = f"ec_{args.ec_id}_user_stats"
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"{output_basedir}/{output_name}_{timestamp}.parquet"
    else:
        output_path = args.output_path

    try:
        # 設定並行處理數量 - 根據數據量自動調整
        num_workers = args.workers
        if num_workers > mp.cpu_count():
            logger.warning(f"指定的工作進程數 {num_workers} 超過可用CPU數量 {mp.cpu_count()}，將設為 {mp.cpu_count()}")
            num_workers = mp.cpu_count()

        # 根據環境決定最佳工作進程數
        if num_workers > 2:
            recommended_workers = max(2, mp.cpu_count() - 1)  # 保留至少一個核心給系統使用
            if num_workers > recommended_workers:
                logger.info(f"為系統穩定性將工作進程數從 {num_workers} 調整為 {recommended_workers}")
                num_workers = recommended_workers

        logger.info(f"使用 {num_workers} 個工作進程進行並行處理")

        # 批次大小設定 - 對於大數據量較重要
        batch_size = args.batch_size
        if batch_size <= 0:
            logger.warning("批次大小不可為負數或零，使用默認值 50000")
            batch_size = 50000
        elif batch_size > 100000:
            logger.warning(f"批次大小 {batch_size} 較大，可能導致內存不足問題")

        logger.info(f"批次大小設定為 {batch_size} 筆數據")

        # 獲取時間範圍（如果有指定）
        time_range = None
        if args.start_date:
            if args.end_date:
                time_range = get_custom_time_range(args.start_date, args.end_date)
            else:
                time_range = get_custom_time_range(args.start_date)
        else:
            # 使用默認時間範圍(從2022-01-01到昨天)
            time_range = get_custom_time_range('2022-01-01')

        # 開始計時
        overall_start_time = time.time()

        # 添加全局狀態更新功能
        def start_status_reporter():
            """啟動一個後台線程，定期報告程式運行狀態"""
            stop_event = threading.Event()

            def status_reporter():
                start_time = time.time()
                last_time = start_time

                while not stop_event.is_set():
                    current_time = time.time()

                    # 每30秒輸出一次狀態
                    if current_time - last_time >= 30:
                        total_runtime = current_time - start_time
                        hours, remainder = divmod(total_runtime, 3600)
                        minutes, seconds = divmod(remainder, 60)
                        logger.info(f"程式仍在運行中... 總運行時間: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")
                        last_time = current_time

                    time.sleep(1)

            # 啟動後台線程
            status_thread = threading.Thread(target=status_reporter)
            status_thread.daemon = True
            status_thread.start()

            return stop_event, status_thread

        # 啟動狀態報告器
        status_stop_event, status_thread = start_status_reporter()

        # 根據參數選擇處理方式：BigQuery 直接計算或本地處理
        if args.use_bigquery_compute:
            logger.info("使用 BigQuery 直接計算用戶統計數據...")
            start_compute_time = time.time()

            # 創建查詢配置
            query_job_config = bigquery.QueryJobConfig()
            query_job_config.use_query_cache = True
            query_job_config.priority = "INTERACTIVE"
            query_job_config.use_legacy_sql = False
            query_job_config.maximum_bytes_billed = 100_000_000_000  # 100GB

            if args.max_rows:
                query_job_config.maximum_results = args.max_rows
                logger.info(f"已設置查詢結果上限: {args.max_rows} 行")

            # 執行計算
            user_data = calculate_user_stats_via_bigquery(
                ec_id=args.ec_id,
                time_range=time_range,
                query_job_config=query_job_config,
                force_query=args.force_query
            )

            compute_time = time.time() - start_compute_time
            logger.info(f"成功通過 BigQuery 計算得到 {len(user_data)} 筆用戶統計資料，耗時 {compute_time:.2f} 秒")

            # 檢查關鍵欄位
            if 'first_interaction_time' in user_data.columns:
                non_null_count = user_data['first_interaction_time'].count()
                logger.info(f"first_interaction_time 非空值數量: {non_null_count} ({non_null_count/len(user_data)*100:.2f}%)")

            if 'purchase_count' in user_data.columns:
                purchase_users = (user_data['purchase_count'] > 0).sum()
                logger.info(f"有購買記錄的用戶數: {purchase_users} ({purchase_users/len(user_data)*100:.2f}%)")

            if 'total_sessions' in user_data.columns:
                avg_sessions = user_data['total_sessions'].mean()
                logger.info(f"平均每用戶會話數: {avg_sessions:.2f}")
        else:
            # 使用原有的本地處理方式
            # 從 BigQuery 獲取數據，使用新的簡化方法
            logger.info(f"開始從 BigQuery 獲取 EC ID {args.ec_id} 的數據...")
            logger.info(f"數據範圍: {time_range[0].isoformat() if time_range else '全部'} 到 {time_range[1].isoformat() if time_range else '全部'}")
            if args.max_rows:
                logger.info(f"使用 --max-rows={args.max_rows} 限制數據量")

            raw_data = get_user_data_from_bigquery_simple(args.ec_id, batch_size, args.max_rows, time_range)

            if raw_data is None or len(raw_data) == 0:
                logger.error("未能獲取用戶數據")
                return

            # 輸出原始數據統計信息
            logger.info(f"獲取原始數據: {len(raw_data):,} 行, {raw_data['permanent'].nunique():,} 個用戶")
            logger.info(f"原始數據欄位: {', '.join(raw_data.columns)}")
            logger.info(f"原始數據類型: \n{raw_data.dtypes}")

            # 判斷數據量大小，決定是否使用並行處理
            # 對於大數據集才使用並行處理，小數據集用標準處理更高效
            # large_dataset_threshold = 1_000_000  # 一百萬筆數據的閾值
            # if len(raw_data) > large_dataset_threshold and not args.disable_parallel:
            if not args.disable_parallel:
                logger.info(f"數據量 ({len(raw_data):,} 筆)，使用 {num_workers} 個工作進程並行處理...")
                user_data = process_user_chunk_parallel(raw_data, args.ec_id, num_workers)
            else:
                # 使用非平行處理
                if args.disable_parallel:
                    logger.info(f"用戶指定禁用平行處理，使用單進程標準處理方式處理 {len(raw_data):,} 筆數據...")
                else:
                    logger.info(f"數據量適中 ({len(raw_data):,} 筆)，自動選擇使用單進程標準處理方式...")
                user_data = process_raw_data_with_progress(raw_data, args.ec_id)

            # 處理後驗證數據
            logger.info(f"處理後數據: {len(user_data):,} 行")
            logger.info(f"處理後數據欄位: {', '.join(user_data.columns)}")

            # 檢查關鍵欄位
            if 'first_interaction_time' in user_data.columns:
                non_null_count = user_data['first_interaction_time'].count()
                logger.info(f"first_interaction_time 非空值數量: {non_null_count} ({non_null_count/len(user_data)*100:.2f}%)")

            if 'purchase_count' in user_data.columns:
                purchase_users = (user_data['purchase_count'] > 0).sum()
                logger.info(f"有購買記錄的用戶數: {purchase_users} ({purchase_users/len(user_data)*100:.2f}%)")

            if 'total_sessions' in user_data.columns:
                avg_sessions = user_data['total_sessions'].mean()
                logger.info(f"平均每用戶會話數: {avg_sessions:.2f}")

        # 補充缺失欄位 (無論使用哪種方式都確保數據完整性)
        required_columns = ['ec_id', 'permanent', 'total_sessions', 'registration_time',
                          'first_interaction_time', 'last_interaction_time',
                          'first_purchase_time', 'last_purchase_time',
                          'first_add_to_cart_time', 'last_add_to_cart_time',
                          'first_view_item_time', 'last_view_item_time',
                          'purchase_count', 'total_purchase_amount',
                          'created_at', 'updated_at']

        for col in required_columns:
            if col not in user_data.columns:
                logger.warning(f"缺少必要欄位 {col}，添加默認值")
                if col in ['purchase_count', 'total_sessions']:
                    user_data[col] = 0
                elif col in ['total_purchase_amount']:
                    user_data[col] = 0.0
                elif col in ['created_at', 'updated_at']:
                    user_data[col] = pd.Timestamp.now(tz='UTC')
                elif col == 'ec_id':
                    user_data[col] = args.ec_id
                else:
                    user_data[col] = None

        # 計算處理時間
        process_time = time.time() - overall_start_time
        logger.info(f"數據處理完成，耗時 {process_time:.2f} 秒")
        logger.info(f"獲取到用戶數據: {len(user_data)} 行")

        # 保存數據 - 根據不同情況選擇保存方式
        if args.local_parquet:
            # 使用 --local-parquet 選項時的儲存方式
            logger.info("保存為本地 parquet 文件")
            # 如果未指定本地路徑，使用默認路徑 './local_data'
            local_path = args.local_path if args.local_path else './local_data'
            save_local_parquet(user_data, args.ec_id, local_path, False, args.output_filename)
        elif args.output_path or not args.skip_local:
            # 使用標準輸出路徑或未跳過本地保存時的儲存方式
            logger.info(f"保存數據到 {output_path}")
            save_to_parquet(user_data, output_path, args.ec_id)

        # 保存到 BigQuery
        if not args.skip_bigquery:
            logger.info("上傳數據到 BigQuery")
            bq_result = save_to_bigquery(user_data, args.ec_id, args.skip_bigquery)
            logger.info(f"BigQuery 上傳結果: {bq_result}")
        else:
            logger.info("跳過上傳到 BigQuery")

        # 保存到 GCS
        if not args.skip_gcs:
            logger.info("上傳數據到 GCS")
            # 獲取查詢的結束日期（如果有）
            end_date_obj = None
            if time_range and len(time_range) == 2:
                end_date_obj = time_range[1]
                logger.info(f"使用查詢結束日期作為 GCS 檔名: {end_date_obj.strftime('%Y-%m-%d')}")

            gcs_result = save_to_gcs(user_data, args.ec_id, args.skip_gcs, end_date_obj)
            logger.info(f"GCS 上傳結果: {gcs_result}")
        else:
            logger.info("跳過上傳到 GCS")

        # 顯示數據摘要
        display_data_summary(user_data)

        # 停止狀態報告器
        status_stop_event.set()
        status_thread.join(timeout=1)

        # 計算總處理時間
        total_time = time.time() - overall_start_time
        logger.info(f"程序執行完成，總耗時 {total_time:.2f} 秒")
        logger.info(f"處理速度: {len(user_data)/total_time:.2f} 筆用戶統計/秒")

    except KeyboardInterrupt:
        logger.info("接收到中斷信號，程序中止")
    except Exception as e:
        logger.error(f"處理時發生錯誤: {str(e)}")
        logger.exception(e)
        return 1

    return 0

if __name__ == "__main__":
    main()
