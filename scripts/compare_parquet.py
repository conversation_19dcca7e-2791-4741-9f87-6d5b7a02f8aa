import pandas as pd
import numpy as np
import os
import sys

def print_separator():
    print("=" * 80)

def analyze_parquet(file_path):
    """分析 Parquet 檔案的內容與統計數據"""
    print(f"\n分析檔案: {file_path}")
    print_separator()

    try:
        df = pd.read_parquet(file_path)

        # 基本資訊
        print(f"總記錄數: {len(df)}")
        print(f"欄位列表: {', '.join(df.columns)}")
        print(f"記憶體使用量: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")

        # 檢查時間欄位
        time_cols = [col for col in df.columns if 'time' in col.lower()]
        for col in time_cols:
            if col in df.columns:
                valid_count = df[col].notna().sum()
                print(f"{col}: {valid_count} 有效值 ({valid_count/len(df)*100:.2f}%)")

        # 檢查購買相關欄位
        purchase_cols = [col for col in df.columns if 'purchase' in col.lower()]
        for col in purchase_cols:
            if col in df.columns:
                if df[col].dtype == 'bool':
                    print(f"{col}: {df[col].sum()} 為 True ({df[col].sum()/len(df)*100:.2f}%)")
                elif np.issubdtype(df[col].dtype, np.number):
                    print(f"{col}: 平均值 = {df[col].mean():.2f}, 最大值 = {df[col].max()}")
                else:
                    val_count = df[col].notna().sum()
                    print(f"{col}: {val_count} 有效值 ({val_count/len(df)*100:.2f}%)")

        # 檢查互動計數和會話計數
        interaction_cols = ['total_interaction_count', 'session_count', 'total_sessions']
        for col in interaction_cols:
            if col in df.columns:
                print(f"{col}: 平均值 = {df[col].mean():.2f}, 最大值 = {df[col].max()}")

        # 檢查 permanent 欄位
        if 'permanent' in df.columns:
            print(f"唯一用戶數: {df['permanent'].nunique()}")

        # 其他重要欄位的分佈
        for col in ['ec_id', 'customer_status']:
            if col in df.columns:
                print(f"{col} 分佈: {df[col].value_counts().to_dict()}")

        # 檢查缺失值比例
        null_percentages = (df.isna().sum() / len(df) * 100).sort_values(ascending=False)
        print("\n各欄位缺失值比例:")
        for col, pct in null_percentages.items():
            if pct > 0:
                print(f"{col}: {pct:.2f}%")

        return df
    except Exception as e:
        print(f"分析檔案時發生錯誤: {str(e)}")
        return None

def compare_parquets(file1, file2):
    """比較兩個 Parquet 檔案之間的差異"""
    print("\n比較兩個檔案的差異")
    print_separator()

    try:
        df1 = pd.read_parquet(file1)
        df2 = pd.read_parquet(file2)

        # 檢查欄位差異
        cols1 = set(df1.columns)
        cols2 = set(df2.columns)

        print(f"檔案1獨有欄位: {cols1 - cols2}")
        print(f"檔案2獨有欄位: {cols2 - cols1}")
        print(f"共同欄位: {cols1 & cols2}")

        # 比較共同欄位的資料分佈
        common_cols = list(cols1 & cols2)

        # 檢查記錄計數
        if len(df1) != len(df2):
            print(f"\n記錄數不同: 檔案1={len(df1)}, 檔案2={len(df2)}")

        # 檢查是否有相同的索引/識別碼
        if 'permanent' in common_cols:
            perm1 = set(df1['permanent'])
            perm2 = set(df2['permanent'])

            print(f"\n共同用戶數: {len(perm1 & perm2)}")
            print(f"檔案1獨有用戶數: {len(perm1 - perm2)}")
            print(f"檔案2獨有用戶數: {len(perm2 - perm1)}")

        # 比較數值型欄位
        numeric_cols = []
        for col in common_cols:
            if pd.api.types.is_numeric_dtype(df1[col]) and pd.api.types.is_numeric_dtype(df2[col]):
                numeric_cols.append(col)

        if numeric_cols:
            print("\n數值型欄位比較:")
            for col in numeric_cols:
                print(f"\n{col}:")
                print(f"  檔案1 - 平均值: {df1[col].mean():.2f}, 中位數: {df1[col].median():.2f}, 最大值: {df1[col].max()}")
                print(f"  檔案2 - 平均值: {df2[col].mean():.2f}, 中位數: {df2[col].median():.2f}, 最大值: {df2[col].max()}")

        # 檢查時間欄位
        datetime_cols = []
        for col in common_cols:
            if pd.api.types.is_datetime64_any_dtype(df1[col]) or pd.api.types.is_datetime64_any_dtype(df2[col]):
                datetime_cols.append(col)

        if datetime_cols:
            print("\n時間欄位比較:")
            for col in datetime_cols:
                valid1 = df1[col].notna().sum()
                valid2 = df2[col].notna().sum()
                print(f"\n{col}:")
                print(f"  檔案1 - 有效值數: {valid1} ({valid1/len(df1)*100:.2f}%)")
                print(f"  檔案2 - 有效值數: {valid2} ({valid2/len(df2)*100:.2f}%)")

    except Exception as e:
        print(f"比較檔案時發生錯誤: {str(e)}")

if __name__ == "__main__":
    original_file = "20250311.parquet"
    processed_file = "./local_data/20250311.parquet"

    print("\n=== 原始檔案分析 ===")
    original_df = analyze_parquet(original_file)

    print("\n=== 處理後檔案分析 ===")
    processed_df = analyze_parquet(processed_file)

    # 比較兩個檔案
    compare_parquets(original_file, processed_file)