import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, MagicMock

import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.rules.dynamic_audience_rules import calculate_days_since_event

def test_calculate_days_directly():
    """直接測試 calculate_days_since_event 函數"""
    # 不需要在這裡導入，因為已經在文件頂部導入了

    # 創建測試資料
    current_time = pd.Timestamp.now(tz='UTC')
    test_df = pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3', 'user4', 'user5'],
        'ec_id': [2980, 2980, 2980, 2980, 2980],
        'last_interaction_time': [
            current_time - pd.Timedelta(days=10),  # 10天前
            current_time - pd.Timedelta(days=40),  # 40天前
            current_time - pd.<PERSON>del<PERSON>(days=100), # 100天前
            current_time - pd.<PERSON>delta(days=400), # 超過1年
            pd.NaT                                # 空值
        ],
        'last_view_item_time': [
            current_time - pd.Timedelta(days=15),  # 15天前
            current_time - pd.Timedelta(days=45),  # 45天前
            pd.NaT,                               # 沒有瀏覽記錄
            current_time - pd.Timedelta(days=380), # 差不多1年前
            current_time - pd.Timedelta(days=5)    # 最近5天
        ],
        'last_add_to_cart_time': [
            current_time - pd.Timedelta(days=20),  # 20天前
            pd.NaT,                               # 沒有加購記錄
            current_time - pd.Timedelta(days=95),  # 95天前
            current_time - pd.Timedelta(days=370), # 差不多1年前
            current_time - pd.Timedelta(days=7)    # 最近1週
        ],
        'purchase_count': [2, 0, 1, 5, 0],
    })

    # 計算各欄位
    with patch('src.rules.dynamic_audience_rules.logger'):  # 避免日誌輸出
        # 計算 last_interaction_days
        df = calculate_days_since_event(
            test_df.copy(),
            event_time_column='last_interaction_time',
            event_days_column='last_interaction_days',
            current_time=current_time
        )

        # 檢查 last_interaction_days 欄位已正確生成
        assert 'last_interaction_days' in df.columns
        # user1: 10天前
        assert df['last_interaction_days'].iloc[0] >= 9 and df['last_interaction_days'].iloc[0] <= 11
        # user2: 40天前
        assert df['last_interaction_days'].iloc[1] >= 39 and df['last_interaction_days'].iloc[1] <= 41
        # user3: 100天前
        assert df['last_interaction_days'].iloc[2] >= 99 and df['last_interaction_days'].iloc[2] <= 101
        # user4: 400天前
        assert df['last_interaction_days'].iloc[3] >= 399 and df['last_interaction_days'].iloc[3] <= 401
        # user5: 空值
        assert df['last_interaction_days'].iloc[4] == -1

        # 計算 last_view_item_days
        df = calculate_days_since_event(
            df,
            event_time_column='last_view_item_time',
            event_days_column='last_view_item_days',
            current_time=current_time
        )

        # 檢查 last_view_item_days 欄位已正確生成
        assert 'last_view_item_days' in df.columns
        # user1: 15天前
        assert df['last_view_item_days'].iloc[0] >= 14 and df['last_view_item_days'].iloc[0] <= 16
        # user2: 45天前
        assert df['last_view_item_days'].iloc[1] >= 44 and df['last_view_item_days'].iloc[1] <= 46
        # user3: 空值
        assert df['last_view_item_days'].iloc[2] == -1
        # user4: 380天前
        assert df['last_view_item_days'].iloc[3] >= 379 and df['last_view_item_days'].iloc[3] <= 381
        # user5: 5天前
        assert df['last_view_item_days'].iloc[4] >= 4 and df['last_view_item_days'].iloc[4] <= 6

        # 計算 last_add_to_cart_days
        df = calculate_days_since_event(
            df,
            event_time_column='last_add_to_cart_time',
            event_days_column='last_add_to_cart_days',
            current_time=current_time
        )

        # 檢查 last_add_to_cart_days 欄位已正確生成
        assert 'last_add_to_cart_days' in df.columns
        # user1: 20天前
        assert df['last_add_to_cart_days'].iloc[0] >= 19 and df['last_add_to_cart_days'].iloc[0] <= 21
        # user2: 空值
        assert df['last_add_to_cart_days'].iloc[1] == -1
        # user3: 95天前
        assert df['last_add_to_cart_days'].iloc[2] >= 94 and df['last_add_to_cart_days'].iloc[2] <= 96
        # user4: 370天前
        assert df['last_add_to_cart_days'].iloc[3] >= 369 and df['last_add_to_cart_days'].iloc[3] <= 371
        # user5: 7天前
        assert df['last_add_to_cart_days'].iloc[4] >= 6 and df['last_add_to_cart_days'].iloc[4] <= 8
