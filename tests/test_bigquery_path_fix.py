import pytest
import os
import pandas as pd
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
from src.core.cloud_integration import write_to_special_lta


class TestBigQueryPathFix:
    """測試 BigQuery 路徑修復"""

    @patch('src.core.cloud_integration.bigquery')
    @patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
    @patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
    @patch('google.auth.default', return_value=(None, None))
    def test_write_to_special_lta_correct_project_and_dataset(self, mock_auth, mock_fetch, mock_calc, mock_bigquery):
        """測試修復後的 BigQuery 專案和資料集路徑是否正確"""
        # 設置 mock
        mock_client = MagicMock()
        mock_bigquery.Client.return_value = mock_client
        mock_job = MagicMock()
        mock_client.load_table_from_dataframe.return_value = mock_job
        mock_job.result.return_value = None
        mock_job.output_rows = 2

        # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
        mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

        # 模擬分群結果
        mock_calc.return_value = {
            'tm:c_9999_107_c_001': ['user1', 'user2']
        }

        # 準備測試資料
        test_data = pd.DataFrame({
            'permanent': ['user1', 'user2'],
            'ec_id': [107, 107],
            'last_interaction_time': [
                datetime(2023, 10, 20, tzinfo=timezone.utc),
                datetime(2023, 10, 21, tzinfo=timezone.utc)
            ],
            'purchase_count': [1, 0]
        })

        # 測試正式環境的路徑
        current_date = datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc)

        # 清除環境變數以使用預設值
        with patch.dict(os.environ, {}, clear=True):
            result = write_to_special_lta(test_data, 107, current_date)

        # 驗證結果
        assert result['success'] is True

        # 驗證 BigQuery 客戶端被正確調用
        mock_bigquery.Client.assert_called_once_with(project='tagtoo-ml-workflow')

        # 驗證表格 ID 包含正確的專案和資料集
        mock_client.load_table_from_dataframe.assert_called_once()
        call_args = mock_client.load_table_from_dataframe.call_args
        table_id = call_args[0][1]  # 第二個參數是 table_id

        # 檢查表格 ID 格式
        assert table_id.startswith('tagtoo-ml-workflow.tagtoo_export_results.')
        assert 'special_lta_temp_for_update_' in table_id

    @patch('src.core.cloud_integration.bigquery')
    @patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
    @patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
    @patch('google.auth.default', return_value=(None, None))
    def test_write_to_special_lta_test_environment_path(self, mock_auth, mock_fetch, mock_calc, mock_bigquery):
        """測試測試環境下的路徑是否正確"""
        # 設置 mock
        mock_client = MagicMock()
        mock_bigquery.Client.return_value = mock_client
        mock_job = MagicMock()
        mock_client.load_table_from_dataframe.return_value = mock_job
        mock_job.result.return_value = None
        mock_job.output_rows = 1

        # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
        mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

        # 模擬分群結果
        mock_calc.return_value = {
            'tm:c_9999_107_c_001': ['user1']
        }

        # 準備測試資料
        test_data = pd.DataFrame({
            'permanent': ['user1'],
            'ec_id': [107],
            'last_interaction_time': [datetime(2023, 10, 20, tzinfo=timezone.utc)],
            'purchase_count': [1]
        })

        current_date = datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc)

        # 模擬測試環境
        with patch.dict(os.environ, {'PYTEST_CURRENT_TEST': 'test_something'}, clear=True):
            result = write_to_special_lta(test_data, 107, current_date)

        # 驗證結果
        assert result['success'] is True

        # 驗證表格 ID 在測試環境下使用正確的資料集
        call_args = mock_client.load_table_from_dataframe.call_args
        table_id = call_args[0][1]

        # 在測試環境下，也應該使用 tagtoo_export_results 資料集
        assert table_id.startswith('tagtoo-ml-workflow.tagtoo_export_results.')

    @patch('src.core.cloud_integration.bigquery')
    @patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
    @patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
    @patch('google.auth.default', return_value=(None, None))
    def test_write_to_special_lta_custom_project_id(self, mock_auth, mock_fetch, mock_calc, mock_bigquery):
        """測試自定義 GCP_PROJECT 環境變數是否正確使用"""
        # 設置 mock
        mock_client = MagicMock()
        mock_bigquery.Client.return_value = mock_client
        mock_job = MagicMock()
        mock_client.load_table_from_dataframe.return_value = mock_job
        mock_job.result.return_value = None
        mock_job.output_rows = 1

        # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
        mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

        # 模擬分群結果
        mock_calc.return_value = {
            'tm:c_9999_107_c_001': ['user1']
        }

        # 準備測試資料
        test_data = pd.DataFrame({
            'permanent': ['user1'],
            'ec_id': [107],
            'last_interaction_time': [datetime(2023, 10, 20, tzinfo=timezone.utc)],
            'purchase_count': [1]
        })

        current_date = datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc)

        # 使用自定義專案 ID
        custom_project_id = 'custom-project-id'
        with patch.dict(os.environ, {'GCP_PROJECT': custom_project_id}, clear=True):
            result = write_to_special_lta(test_data, 107, current_date)

        # 驗證結果
        assert result['success'] is True

        # 驗證使用了自定義專案 ID
        mock_bigquery.Client.assert_called_once_with(project=custom_project_id)

        # 驗證表格 ID 包含自定義專案 ID
        call_args = mock_client.load_table_from_dataframe.call_args
        table_id = call_args[0][1]
        assert table_id.startswith(f'{custom_project_id}.tagtoo_export_results.')