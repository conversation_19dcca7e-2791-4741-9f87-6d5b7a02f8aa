import unittest
from unittest.mock import patch, MagicMock, Mock
import pandas as pd
from datetime import datetime
import sys
import os
import logging
from io import StringIO

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 將日誌設置為警告級別，減少測試輸出
logging.getLogger('lta_user_stats').setLevel(logging.CRITICAL)  # 提高到 CRITICAL 級別，抑制 ERROR 和 WARNING

from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery

class TestBigQueryCompute(unittest.TestCase):
    """測試使用 BigQuery 直接計算用戶統計數據的功能"""

    def setUp(self):
        """設置測試環境，包括暫時捕獲日誌輸出"""
        # 保存當前日誌級別
        self.original_log_level = logging.getLogger('lta_user_stats').level
        # 設置為 CRITICAL 級別，抑制所有較低級別的訊息
        logging.getLogger('lta_user_stats').setLevel(logging.CRITICAL)

    def tearDown(self):
        """測試後恢復日誌級別"""
        # 恢復原始日誌級別
        logging.getLogger('lta_user_stats').setLevel(self.original_log_level)

    @patch('scripts.init_user_stats_for_ec_id.execute_single_query')
    def test_calculate_user_stats_via_bigquery(self, mock_execute_query):
        """測試 BigQuery 計算用戶統計的主要功能"""
        # 創建模擬的 BigQuery 結果
        mock_result = Mock()

        # 準備模擬的 DataFrame 作為返回值
        mock_data = {
            'ec_id': [107, 107, 107],
            'permanent': ['user1', 'user2', 'user3'],
            'total_sessions': [3, 1, 2],
            'registration_time': [datetime(2023, 1, 15), None, datetime(2023, 2, 10)],
            'first_interaction_time': [datetime(2023, 1, 1), datetime(2023, 1, 5), datetime(2023, 1, 10)],
            'last_interaction_time': [datetime(2023, 3, 1), datetime(2023, 1, 5), datetime(2023, 2, 20)],
            'first_purchase_time': [datetime(2023, 1, 2), None, datetime(2023, 1, 15)],
            'last_purchase_time': [datetime(2023, 2, 15), None, datetime(2023, 2, 15)],
            'purchase_count': [2, 0, 1],
            'total_purchase_amount': [1500.0, 0.0, 800.0],
            'created_at': [datetime.now(), datetime.now(), datetime.now()],
            'updated_at': [datetime.now(), datetime.now(), datetime.now()]
        }
        mock_df = pd.DataFrame(mock_data)

        # 設置模擬函數返回值，模擬 BigQuery 查詢結果
        mock_result.to_dataframe.return_value = mock_df
        mock_execute_query.return_value = mock_result

        # 執行測試
        ec_id = 107
        time_range = [datetime(2023, 1, 1), datetime(2023, 3, 1)]
        result_df = calculate_user_stats_via_bigquery(ec_id, time_range)

        # 檢查結果是否符合預期
        self.assertEqual(len(result_df), 3, "應該有 3 筆用戶資料")
        self.assertEqual(result_df['purchase_count'].sum(), 3, "總購買次數應為 3")
        self.assertEqual(result_df['total_purchase_amount'].sum(), 2300.0, "總購買金額應為 2300.0")
        self.assertEqual(result_df['total_sessions'].sum(), 6, "總會話數應為 6")

        # 檢查購買用戶數
        purchase_users = (result_df['purchase_count'] > 0).sum()
        self.assertEqual(purchase_users, 2, "應有 2 位購買用戶")

        # 驗證 BigQuery 查詢
        mock_execute_query.assert_called_once()

        # 檢查 SQL 查詢中是否包含正確的時間範圍和 ec_id
        args, kwargs = mock_execute_query.call_args
        query = args[0]
        self.assertIn(f"ec_id = {ec_id}", query, "SQL 查詢中應包含正確的 ec_id")

    @patch('scripts.init_user_stats_for_ec_id.execute_single_query')
    def test_calculate_user_stats_no_time_range(self, mock_execute_query):
        """測試沒有指定時間範圍的情況"""
        # 創建模擬的 BigQuery 結果
        mock_result = Mock()
        mock_df = pd.DataFrame({
            'ec_id': [107],
            'permanent': ['user1'],
            'total_sessions': [1],
            'registration_time': [None],
            'first_interaction_time': [datetime(2023, 1, 1)],
            'last_interaction_time': [datetime(2023, 1, 1)],
            'first_purchase_time': [None],
            'last_purchase_time': [None],
            'purchase_count': [0],
            'total_purchase_amount': [0.0],
            'created_at': [datetime.now()],
            'updated_at': [datetime.now()]
        })

        mock_result.to_dataframe.return_value = mock_df
        mock_execute_query.return_value = mock_result

        # 執行測試：不指定時間範圍
        result_df = calculate_user_stats_via_bigquery(107)

        # 檢查結果
        self.assertEqual(len(result_df), 1, "應該有 1 筆用戶資料")

        # 驗證 SQL 查詢不包含時間範圍條件
        args, kwargs = mock_execute_query.call_args
        query = args[0]
        self.assertNotIn("BETWEEN TIMESTAMP", query, "未指定時間範圍時，SQL 查詢不應包含時間過濾")

    @patch('scripts.init_user_stats_for_ec_id.execute_single_query')
    def test_empty_result(self, mock_execute_query):
        """測試查詢結果為空的情況"""
        # 設置模擬函數返回 None，模擬查詢失敗的情況
        mock_execute_query.return_value = None

        # 執行測試
        result_df = calculate_user_stats_via_bigquery(107)

        # 檢查結果應為空 DataFrame，但應包含所有必要列
        self.assertTrue(result_df.empty, "查詢失敗時應返回空 DataFrame")
        # 檢查空 DataFrame 是否包含所有必要列
        required_columns = [
            'ec_id', 'permanent', 'total_sessions', 'registration_time',
            'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'purchase_count', 'total_purchase_amount',
            'created_at', 'updated_at'
        ]
        for col in required_columns:
            self.assertIn(col, result_df.columns, f"空 DataFrame 應包含列 {col}")

        # 再次測試，這次模擬查詢成功但返回空結果集
        mock_result = Mock()
        empty_df = pd.DataFrame(columns=required_columns)
        mock_result.to_dataframe.return_value = empty_df
        mock_execute_query.return_value = mock_result

        result_df = calculate_user_stats_via_bigquery(107)
        self.assertTrue(result_df.empty, "查詢結果為空時應返回空 DataFrame")
        for col in required_columns:
            self.assertIn(col, result_df.columns, f"空 DataFrame 應包含列 {col}")

if __name__ == '__main__':
    unittest.main()