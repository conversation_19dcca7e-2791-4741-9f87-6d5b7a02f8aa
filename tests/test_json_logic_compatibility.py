#!/usr/bin/env python3
"""
測試不同 json-logic 套件的兼容性
在現有環境中測試當前安裝的 json-logic 套件
"""

import sys
import json
import traceback
from typing import Dict, Any, List


def test_current_json_logic():
    """測試當前安裝的 json-logic 套件"""
    print("=== 測試當前 json-logic 套件 ===\n")

    # 嘗試不同的導入方式
    json_logic_func = None
    package_info = None

    import_attempts = [
        ("json_logic", "jsonLogic", "json-logic 或 panzi-json-logic"),
        ("jsonlogic_rs", "apply", "jsonlogic-rs"),
    ]

    for module_name, func_name, description in import_attempts:
        try:
            module = __import__(module_name)
            if hasattr(module, func_name):
                json_logic_func = getattr(module, func_name)
                package_info = {
                    "module": module_name,
                    "function": func_name,
                    "description": description
                }
                print(f"✅ 成功導入: {description}")
                print(f"   模組: {module_name}")
                print(f"   函數: {func_name}")
                if hasattr(module, '__version__'):
                    print(f"   版本: {module.__version__}")
                break
        except ImportError:
            print(f"❌ 無法導入: {description}")
        except Exception as e:
            print(f"❌ 導入錯誤 {description}: {e}")

    assert json_logic_func is not None, "無法找到任何可用的 json-logic 套件"

    print(f"\n使用套件: {package_info['description']}")
    print("="*50)

    # 執行測試案例
    test_cases = [
        {
            "name": "基本相等測試",
            "rule": {"===": [{"var": "a"}, 7]},
            "data": {"a": 7},
            "expected": True
        },
        {
            "name": "基本不等測試",
            "rule": {"!==": [{"var": "a"}, 7]},
            "data": {"a": 5},
            "expected": True
        },
        {
            "name": "大於測試",
            "rule": {">": [{"var": "days"}, 10]},
            "data": {"days": 15},
            "expected": True
        },
        {
            "name": "小於等於測試",
            "rule": {"<=": [{"var": "days"}, 60]},
            "data": {"days": 45},
            "expected": True
        },
        {
            "name": "複合 AND 邏輯（專案實際使用的模式）",
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            },
            "data": {
                "last_interaction_time": "2024-01-01",
                "last_interaction_days": 30,
                "min_days": 10,
                "max_days": 60
            },
            "expected": True
        },
        {
            "name": "空值比較測試",
            "rule": {"!=": [{"var": "value"}, None]},
            "data": {"value": None},
            "expected": False
        },
        {
            "name": "購買次數範圍測試",
            "rule": {
                "and": [
                    {">=": [{"var": "purchase_count"}, 1]},
                    {"<=": [{"var": "purchase_count"}, 5]}
                ]
            },
            "data": {"purchase_count": 3},
            "expected": True
        },
        {
            "name": "複雜嵌套邏輯",
            "rule": {
                "or": [
                    {
                        "and": [
                            {">": [{"var": "last_interaction_days"}, 0]},
                            {"<=": [{"var": "last_interaction_days"}, 30]}
                        ]
                    },
                    {">=": [{"var": "purchase_count"}, 1]}
                ]
            },
            "data": {
                "last_interaction_days": 15,
                "purchase_count": 0
            },
            "expected": True
        }
    ]

    passed = 0
    failed = 0
    errors = []

    print("\n執行測試案例:")
    print("-" * 50)

    for i, test_case in enumerate(test_cases, 1):
        try:
            result = json_logic_func(test_case["rule"], test_case["data"])

            if result == test_case["expected"]:
                print(f"✅ {i:2d}. {test_case['name']}")
                passed += 1
            else:
                print(f"❌ {i:2d}. {test_case['name']}")
                print(f"     期望: {test_case['expected']}, 實際: {result}")
                failed += 1
                errors.append(f"{test_case['name']}: 期望 {test_case['expected']}, 實際 {result}")

        except Exception as e:
            print(f"💥 {i:2d}. {test_case['name']} - 執行錯誤")
            print(f"     錯誤: {str(e)}")
            failed += 1
            errors.append(f"{test_case['name']}: 執行錯誤 - {str(e)}")

    # 總結
    print("\n" + "="*50)
    print("測試結果總結")
    print("="*50)
    print(f"套件: {package_info['description']}")
    print(f"通過: {passed}")
    print(f"失敗: {failed}")
    print(f"總計: {passed + failed}")

    if failed == 0:
        print("🎉 所有測試都通過！這個套件與專案兼容。")
    else:
        print(f"⚠️  有 {failed} 個測試失敗")
        print("\n失敗詳情:")
        for error in errors:
            print(f"  - {error}")
        assert False, f"有 {failed} 個 JSON Logic 測試失敗"


def test_with_actual_project_data():
    """使用專案中實際的規則和數據進行測試"""
    print("\n" + "="*60)
    print("使用專案實際數據進行測試")
    print("="*60)

    try:
        # 嘗試導入專案模組，使用不同的導入方式
        import pandas as pd

        # 嘗試不同的導入路徑
        try:
            # 先嘗試從 src.utils 導入
            sys.path.append('/app/src')
            from utils import vectorized_evaluate
        except ImportError:
            try:
                # 嘗試從 src.utils 導入
                sys.path.append('/app')
                from src.utils import vectorized_evaluate
            except ImportError:
                try:
                    # 最後嘗試直接從當前目錄導入
                    import os
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    parent_dir = os.path.dirname(current_dir)
                    src_dir = os.path.join(parent_dir, 'src')
                    sys.path.insert(0, src_dir)
                    from utils import vectorized_evaluate
                except ImportError as e:
                    print(f"❌ 無法導入 vectorized_evaluate: {str(e)}")
                    print("跳過專案實際測試")
                    return

        # 模擬實際的用戶數據
        sample_data = pd.DataFrame([
            {
                'permanent': 'user1',
                'last_interaction_time': '2024-01-01',
                'last_interaction_days': 30,
                'last_view_item_days': 25,
                'last_add_to_cart_days': 20,
                'last_purchase_days': 15,
                'purchase_count': 3,
                'total_sessions': 10
            },
            {
                'permanent': 'user2',
                'last_interaction_time': '2024-01-15',
                'last_interaction_days': 15,
                'last_view_item_days': 10,
                'last_add_to_cart_days': 8,
                'last_purchase_days': 5,
                'purchase_count': 1,
                'total_sessions': 5
            },
            {
                'permanent': 'user3',
                'last_interaction_time': None,
                'last_interaction_days': -1,
                'last_view_item_days': -1,
                'last_add_to_cart_days': -1,
                'last_purchase_days': -1,
                'purchase_count': 0,
                'total_sessions': 0
            }
        ])

        # 模擬實際的規則
        sample_rules = {
            'rule1': {
                'rule': {
                    'and': [
                        {'!=': [{'var': 'last_interaction_time'}, None]},
                        {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                        {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
                    ]
                },
                'data': {
                    'min_days': 10,
                    'max_days': 60
                }
            },
            'rule2': {
                'rule': {
                    'and': [
                        {'>=': [{'var': 'purchase_count'}, 1]},
                        {'<=': [{'var': 'purchase_count'}, 5]}
                    ]
                },
                'data': {}
            }
        }

        print("執行 vectorized_evaluate 測試...")
        results = vectorized_evaluate(sample_data, sample_rules)

        print(f"✅ vectorized_evaluate 執行成功")
        print(f"結果: {results}")

        # 驗證結果
        expected_rule1_users = ['user1', 'user2']  # 兩個用戶都符合條件
        expected_rule2_users = ['user1', 'user2']  # 兩個用戶都有購買

        rule1_match = set(results.get('rule1', [])) == set(expected_rule1_users)
        rule2_match = set(results.get('rule2', [])) == set(expected_rule2_users)

        if rule1_match and rule2_match:
            print("🎉 專案實際測試通過！")
        else:
            print("⚠️  專案實際測試結果不符合預期")
            print(f"Rule1 預期: {expected_rule1_users}, 實際: {results.get('rule1', [])}")
            print(f"Rule2 預期: {expected_rule2_users}, 實際: {results.get('rule2', [])}")
            assert False, "專案實際測試結果不符合預期"

    except Exception as e:
        print(f"❌ 專案實際測試失敗: {str(e)}")
        print("錯誤詳情:")
        traceback.print_exc()
        assert False, f"專案實際測試失敗: {str(e)}"