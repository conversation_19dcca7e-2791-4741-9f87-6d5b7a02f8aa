import pytest
import importlib
import sys
from unittest.mock import patch, MagicMock
import pandas as pd
import json


def test_dynamic_audience_rules_imports():
    """測試動態受眾規則模組的導入邏輯"""
    # 先從 sys.modules 中移除要測試的模組，以確保重新載入
    if 'src.rules.dynamic_audience_rules' in sys.modules:
        del sys.modules['src.rules.dynamic_audience_rules']

    # 使用 patch 模擬外部依賴
    with patch('google.cloud.storage.Client') as mock_storage_client:
        # 設置 mock 返回值
        mock_instance = mock_storage_client.return_value
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_bucket.blob.return_value = mock_blob
        mock_instance.bucket.return_value = mock_bucket
        mock_blob.download_as_text.return_value = json.dumps({"107": {"rules": {}}})

        # 重新導入模組
        dynamic_audience_rules = importlib.import_module('src.rules.dynamic_audience_rules')

        # 驗證函數是否存在
        assert hasattr(dynamic_audience_rules, 'fetch_audience_mapping_with_rules')
        assert hasattr(dynamic_audience_rules, 'calculate_audience_segments_dynamic')

        # 測試基本功能
        ec_id = 107
        add_to_dxp = True
        rules = dynamic_audience_rules.fetch_audience_mapping_with_rules(ec_id, add_to_dxp)
        assert isinstance(rules, dict)

        # 測試是否能正確建立空的結果
        df = pd.DataFrame({
            'ec_id': [107],
            'permanent': ['user1'],
            'last_interaction_time': [pd.Timestamp('2023-01-01')],
            'purchase_count': [1]
        })
        segments = dynamic_audience_rules.calculate_audience_segments_dynamic(df, ec_id)
        assert isinstance(segments, dict)