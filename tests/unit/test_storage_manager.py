import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
from datetime import datetime, timezone
import os

from src.core.cloud_integration import StorageManager

@patch('src.core.cloud_integration.storage')
@patch('src.core.cloud_integration.gcsfs.GCSFileSystem')  # 添加對 GCSFileSystem 的 mock
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_storage_manager_ec_id_type_conversion(mock_auth, mock_gcsfs, mock_storage):
    """測試 StorageManager 對 ec_id 欄位進行整數類型轉換的邏輯"""
    # 準備測試資料
    test_data = pd.DataFrame({
        'ec_id': [1.0, 2.0, 3.5],  # 混合浮點數，移除 None 值
        'user_id': ['user1', 'user2', 'user4'],
        'value': [100, 200, 400]
    })

    # 模擬 GCSFileSystem 實例
    mock_gcsfs_instance = MagicMock()
    mock_gcsfs.return_value = mock_gcsfs_instance
    mock_gcsfs_instance.exists.return_value = False

    # 模擬文件對象與打開/寫入成功
    mock_file = MagicMock()
    mock_context_manager = MagicMock()
    mock_context_manager.__enter__.return_value = mock_file
    mock_gcsfs_instance.open.return_value = mock_context_manager

    # 設置測試環境變數
    with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
        # 建立 StorageManager 實例
        storage_manager = StorageManager(bucket_name="test-bucket")

        # 使用 to_parquet 的模擬來避免實際寫入
        with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
            # 執行儲存操作
            date = datetime(2023, 1, 1, tzinfo=timezone.utc)
            result = storage_manager.save_snapshot(test_data, date, ec_id=123)

            # 驗證結果成功
            assert 'success' in result
            assert result['success'] is True
            assert '20230101' in result['path']

            # 驗證 gcsfs.open 被調用
            mock_gcsfs_instance.open.assert_called_once()

            # 驗證 to_parquet 被調用
            mock_to_parquet.assert_called_once()

@patch('src.core.cloud_integration.storage')
@patch('src.core.cloud_integration.gcsfs.GCSFileSystem')  # 添加對 GCSFileSystem 的 mock
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_storage_manager_ec_id_null_validation(mock_auth, mock_gcsfs, mock_storage):
    """測試 StorageManager 在 ec_id 欄位包含 NULL 值時的行為"""
    # 準備包含 NULL 值的測試資料
    test_data = pd.DataFrame({
        'ec_id': [1.0, 2.0, None, 3.5],  # 包含 NULL 值
        'user_id': ['user1', 'user2', 'user3', 'user4'],
        'value': [100, 200, 300, 400]
    })

    # 模擬 GCSFileSystem 實例
    mock_gcsfs_instance = MagicMock()
    mock_gcsfs.return_value = mock_gcsfs_instance
    mock_gcsfs_instance.exists.return_value = False

    # 模擬文件對象
    mock_file = MagicMock()
    mock_context_manager = MagicMock()
    mock_context_manager.__enter__.return_value = mock_file
    mock_gcsfs_instance.open.return_value = mock_context_manager

    # 設置測試環境變數
    with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
        # 建立 StorageManager 實例
        storage_manager = StorageManager(bucket_name="test-bucket")

        # 執行儲存操作，驗證成功處理（StorageManager 會處理 NULL 值）
        with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
            date = datetime(2023, 1, 1, tzinfo=timezone.utc)
            result = storage_manager.save_snapshot(test_data, date, ec_id=123)

            # 驗證調用了 to_parquet
            mock_to_parquet.assert_called_once()

            # 驗證結果成功
            assert result['success'] is True

@patch('src.core.cloud_integration.storage')
@patch('src.core.cloud_integration.gcsfs.GCSFileSystem')
def test_storage_manager_save_snapshot_uses_utc_date_with_cross_day_boundary(mock_gcsfs, mock_storage):
    """測試當 UTC 時間跨日時，StorageManager.save_snapshot 使用的是 UTC 日期而不是台灣時間日期"""
    # 模擬 GCSFileSystem 實例
    mock_gcsfs_instance = MagicMock()
    mock_gcsfs.return_value = mock_gcsfs_instance
    mock_gcsfs_instance.exists.return_value = False

    # 創建測試數據
    test_df = pd.DataFrame({
        'ec_id': [107],
        'permanent': ['test_user'],
        'purchase_count': [1],
        'total_purchase_amount': [100.0],
        'total_sessions': [10],
        'last_interaction_time': [pd.Timestamp('2025-02-23T16:00:00Z')],
        'first_interaction_time': [pd.Timestamp('2025-02-23T16:00:00Z')],
        'registration_time': [pd.Timestamp('2025-02-23T16:00:00Z')],
        'first_purchase_time': [pd.Timestamp('2025-02-23T16:00:00Z')],
        'last_purchase_time': [pd.Timestamp('2025-02-23T16:00:00Z')]
    })

    # 多個臨界時間測試案例
    test_cases = [
        # 案例 1: UTC 時間 2025-02-23 16:00:00 (台灣時間 2025-02-24 00:00:00)
        {
            'utc_time': pd.Timestamp('2025-02-23T16:00:00Z'),
            'expected_utc_date': '20250223',
            'expected_taiwan_date': '20250224'
        },
        # 案例 2: UTC 時間 2025-02-23 15:59:59 (台灣時間 2025-02-23 23:59:59)
        {
            'utc_time': pd.Timestamp('2025-02-23T15:59:59Z'),
            'expected_utc_date': '20250223',
            'expected_taiwan_date': '20250223'
        },
        # 案例 3: UTC 時間 2025-02-22 16:00:00 (台灣時間 2025-02-23 00:00:00)
        {
            'utc_time': pd.Timestamp('2025-02-22T16:00:00Z'),
            'expected_utc_date': '20250222',
            'expected_taiwan_date': '20250223'
        }
    ]

    for case in test_cases:
        # 重設 mock 以便重新計數
        mock_gcsfs_instance.reset_mock()

        # 模擬 gcsfs.open 返回的文件對象
        mock_file = MagicMock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__.return_value = mock_file
        mock_gcsfs_instance.open.return_value = mock_context_manager

        # 設置測試環境變數
        with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
            with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
                with patch('src.core.cloud_integration.get_taiwan_date_str',
                    side_effect=lambda dt: case['expected_taiwan_date']):

                    # 執行測試
                    storage_manager = StorageManager(bucket_name="test-bucket")

                    # 儲存快照
                    snapshot_result = storage_manager.save_snapshot(test_df, case['utc_time'], 107)

                    # 驗證結果
                    assert snapshot_result['success'] is True
                    assert case['expected_utc_date'] in snapshot_result['path']
                    assert case['expected_taiwan_date'] not in snapshot_result['path'] or case['expected_taiwan_date'] == case['expected_utc_date']

                    # 驗證 gcsfs.open 被調用且使用了正確的 UTC 日期
                    mock_gcsfs_instance.open.assert_called_once()
                    gcs_path_arg = mock_gcsfs_instance.open.call_args[0][0]
                    assert case['expected_utc_date'] in gcs_path_arg
                    if case['expected_taiwan_date'] != case['expected_utc_date']:
                        assert case['expected_taiwan_date'] not in gcs_path_arg
