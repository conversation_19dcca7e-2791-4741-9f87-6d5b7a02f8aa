"""
測試 parallel_process 結果格式處理，防止 EC 2980 分群問題再次發生
"""
import pytest
import pandas as pd
from unittest.mock import Mock, patch
from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic
from src.optimization.parallel_processing import parallel_process
from src.utils import merge_results
import os
import json


class TestParallelProcessingFormat:
    """測試平行處理結果格式處理"""

    def test_parallel_process_returns_correct_format(self):
        """測試 parallel_process 返回正確的合併後字典格式"""

        def mock_process_chunk(chunk):
            """模擬處理函數，返回字典格式"""
            return {
                'rule_1': ['user1', 'user2'],
                'rule_2': ['user3']
            }

        # 準備測試資料
        test_data = pd.DataFrame({
            'permanent': ['user1', 'user2', 'user3', 'user4'],
            'value': [1, 2, 3, 4]
        })

        # 執行 parallel_process
        result = parallel_process(test_data, mock_process_chunk, max_workers=2, chunk_size=2)

        # 驗證返回格式是字典
        assert isinstance(result, dict), f"Expected dict, got {type(result)}"

        # 驗證內容正確
        assert 'rule_1' in result
        assert 'rule_2' in result
        assert len(result['rule_1']) > 0
        assert len(result['rule_2']) > 0



    def test_merge_results_handles_various_input_formats(self):
        """測試 merge_results 函數能正確處理各種輸入格式"""

        # 測試正常的字典列表
        normal_input = [
            {'rule_1': ['user1'], 'rule_2': ['user2']},
            {'rule_1': ['user3'], 'rule_3': ['user4']}
        ]
        result = merge_results(normal_input)
        assert result == {'rule_1': ['user1', 'user3'], 'rule_2': ['user2'], 'rule_3': ['user4']}

        # 測試包含 None 的輸入
        with_none = [
            {'rule_1': ['user1']},
            None,
            {'rule_1': ['user2']}
        ]
        result = merge_results(with_none)
        assert result == {'rule_1': ['user1', 'user2']}

        # 測試錯誤格式的輸入（應該被忽略）
        with_invalid = [
            {'rule_1': ['user1']},
            ['invalid', 'format'],  # 這應該被忽略
            {'rule_1': ['user2']}
        ]
        result = merge_results(with_invalid)
        assert result == {'rule_1': ['user1', 'user2']}

    def test_parallel_process_error_handling(self):
        """測試 parallel_process 的錯誤處理"""

        def failing_process_chunk(chunk):
            """模擬會失敗的處理函數"""
            if len(chunk) > 1:
                raise ValueError("Simulated processing error")
            return {'rule_1': ['user1']}

        test_data = pd.DataFrame({
            'permanent': ['user1', 'user2', 'user3'],
            'value': [1, 2, 3]
        })

        # 即使有錯誤，應該還是能處理成功的 chunk
        result = parallel_process(test_data, failing_process_chunk, max_workers=2, chunk_size=1)

        # 驗證還是返回字典格式
        assert isinstance(result, dict)


class TestTimeRangeErrorMessage:
    """測試時間範圍錯誤訊息生成"""

    def test_time_range_error_message_generation(self):
        """測試時間範圍檢查錯誤訊息的正確生成"""
        from src.utils import check_rule_data_requirement

        # 模擬規則資料
        rule_data = {
            'rule': {
                '>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]
            },
            'data': {
                'min_days': 60
            }
        }

        # 模擬資料時間範圍（不足的情況）
        data_time_range = {
            'last_interaction_days': {
                'max_days': 37,
                'min_days': 0
            }
        }

        result = check_rule_data_requirement(rule_data, data_time_range)

        # 驗證結果結構
        assert 'has_sufficient_data' in result
        assert 'missing_requirements' in result
        assert 'reason' in result

        # 驗證不足的情況
        assert result['has_sufficient_data'] is False
        assert len(result['missing_requirements']) > 0

        # 驗證錯誤訊息格式正確
        assert 'reason' in result
        assert '60' in result['reason']  # 需要的天數
        assert '37' in result['reason']  # 可用的天數

        # 確保錯誤訊息邏輯正確（需要的天數 > 可用的天數）
        missing_req = result['missing_requirements'][0]
        assert missing_req['required_value'] == 60
        assert missing_req['available_max'] == 37
        assert missing_req['required_value'] > missing_req['available_max']

    def test_time_range_sufficient_data(self):
        """測試時間範圍足夠的情況"""
        from src.utils import check_rule_data_requirement

        rule_data = {
            'rule': {
                '>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]
            },
            'data': {
                'min_days': 30
            }
        }

        # 模擬資料時間範圍（足夠的情況）
        data_time_range = {
            'last_interaction_days': {
                'max_days': 90,
                'min_days': 0
            }
        }

        result = check_rule_data_requirement(rule_data, data_time_range)

        # 驗證資料足夠的情況
        assert result['has_sufficient_data'] is True
        assert len(result['missing_requirements']) == 0
        assert result['reason'] is None




class TestRealRulesIntegration:
    """整合測試：使用本地真實的 audience_rules.json 檔案"""

    def load_local_audience_rules(self):
        """載入本地的 audience_rules.json 檔案內容"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        rules_path = os.path.join(current_dir, '..', '..', 'scripts', 'audience_rules.json')
        rules_path = os.path.normpath(rules_path)

        if not os.path.exists(rules_path):
            pytest.skip(f"本地規則檔案不存在: {rules_path}")

        with open(rules_path, 'r', encoding='utf-8') as f:
            return f.read()

    @patch('google.cloud.storage.Client')
    def test_real_ec_107_rules_integration(self, mock_storage_client):
        """測試真實的 EC 107 規則（使用本地檔案）"""
        from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

        # 載入本地規則檔案內容
        local_rules_content = self.load_local_audience_rules()

        # Mock GCS 下載，但返回真實的本地檔案內容
        mock_blob = Mock()
        mock_blob.download_as_text.return_value = local_rules_content

        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob

        mock_client = Mock()
        mock_client.bucket.return_value = mock_bucket
        mock_storage_client.return_value = mock_client

        # 準備測試資料
        user_stats = pd.DataFrame({
            'permanent': ['user1', 'user2', 'user3', 'user4', 'user5'],
            'last_interaction_days': [5, 45, 100, 200, 400],  # 不同的互動天數
            'last_purchase_days': [3, 50, 120, 300, None],     # 不同的購買天數
            'purchase_count': [0, 1, 2, 1, 0],                 # 不同的購買次數
            'last_interaction_time': ['2023-01-01', '2023-01-15', '2023-01-25', '2023-02-01', None],
            'last_purchase_time': ['2023-01-03', None, '2023-01-13', '2023-01-23', None]
        })

        # 執行分群計算
        result = calculate_audience_segments_dynamic(user_stats, ec_id=107)

        # 驗證結果格式
        assert isinstance(result, dict), f"Expected dict result, got {type(result)}"

        # 驗證使用了真實規則（EC 107 應該有多個規則）
        print(f"EC 107 分群結果: {list(result.keys())}")

        # 驗證 GCS Client 被正確使用
        mock_storage_client.assert_called_once()
        mock_blob.download_as_text.assert_called_once()

    @patch('google.cloud.storage.Client')
    def test_real_ec_2980_rules_integration(self, mock_storage_client):
        """測試真實的 EC 2980 規則（使用本地檔案）"""
        from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

        # 載入本地規則檔案內容
        local_rules_content = self.load_local_audience_rules()

        # 驗證檔案確實包含 EC 2980 規則
        rules_data = json.loads(local_rules_content)
        assert "2980" in rules_data, "本地規則檔案應該包含 EC 2980"

        # Mock GCS 下載，但返回真實的本地檔案內容
        mock_blob = Mock()
        mock_blob.download_as_text.return_value = local_rules_content

        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob

        mock_client = Mock()
        mock_client.bucket.return_value = mock_bucket
        mock_storage_client.return_value = mock_client

        # 準備測試資料（包含 EC 2980 規則需要的欄位）
        user_stats = pd.DataFrame({
            'permanent': ['user1', 'user2', 'user3', 'user4', 'user5'],
            'last_interaction_days': [5, 15, 25, 45, 400],
            'last_purchase_days': [3, 10, 20, 50, None],
            'last_view_item_days': [7, 20, 45, 60, 400],
            'last_add_to_cart_days': [50, 50, 50, 50, 50],  # 超過規則天數限制
            'purchase_count': [1, 2, 0, 1, 0],
            'last_interaction_time': ['2023-01-01', '2023-01-15', '2023-01-25', '2023-02-01', None],
            'last_purchase_time': ['2023-01-03', '2023-01-13', None, None, None],
            'last_view_item_time': ['2023-01-07', '2023-01-14', '2023-01-20', None, None],
            'last_add_to_cart_time': [None, None, None, None, None]
        })

        # 執行分群計算
        result = calculate_audience_segments_dynamic(user_stats, ec_id=2980)

        # 驗證結果格式
        assert isinstance(result, dict), f"Expected dict result, got {type(result)}"

        # 驗證使用了真實規則（應該有分群結果）
        print(f"EC 2980 分群結果: {list(result.keys())}")

        # 確認至少有一些分群結果（證明規則被正確載入和執行）
        assert len(result) > 0, "應該有分群結果，因為使用了真實的 EC 2980 規則"

        # 驗證 GCS Client 被正確使用
        mock_storage_client.assert_called_once()
        mock_blob.download_as_text.assert_called_once()

    @patch('google.cloud.storage.Client')
    def test_no_gcs_network_calls_during_test(self, mock_storage_client):
        """測試確認沒有真實的網路調用，但使用真實規則內容"""
        from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic

        # 載入本地規則檔案內容
        local_rules_content = self.load_local_audience_rules()

        # Mock GCS 下載，但返回真實的本地檔案內容
        mock_blob = Mock()
        mock_blob.download_as_text.return_value = local_rules_content

        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob

        mock_client = Mock()
        mock_client.bucket.return_value = mock_bucket
        mock_storage_client.return_value = mock_client

        # 準備簡單的測試資料
        user_stats = pd.DataFrame({
            'permanent': ['user1', 'user2'],
            'last_interaction_days': [5, 15],
            'last_purchase_days': [3, 10],
            'purchase_count': [1, 0],
            'last_interaction_time': ['2023-01-01', '2023-01-15'],
            'last_purchase_time': ['2023-01-03', None]
        })

        # 執行分群計算
        result = calculate_audience_segments_dynamic(user_stats, ec_id=107)

        # 驗證沒有真實的網路調用（所有調用都是 mock）
        mock_storage_client.assert_called_once()
        mock_client.bucket.assert_called_once_with('tagtoo-ml-workflow')
        mock_bucket.blob.assert_called_once_with('LTA/user_stats_configs/audience_rules.json')
        mock_blob.download_as_text.assert_called_once()

        # 驗證結果正確
        assert isinstance(result, dict)
        print(f"測試使用真實規則內容，GCS 調用被正確 mock: {len(result)} 個分群")


if __name__ == '__main__':
    pytest.main([__file__])