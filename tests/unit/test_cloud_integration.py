import pytest
from unittest.mock import patch, MagicMock
import json
from datetime import datetime, timezone, timedelta
import pytz
import pytz
from src.core.cloud_integration import (
    save_user_stats_snapshot,
    StorageManager,
    write_to_special_lta,
    BigQueryClient
)
from src.rules.dynamic_audience_rules import fetch_audience_mapping_with_rules
import pandas as pd
import logging
import os
from google.cloud import bigquery
from src.core.cloud_integration import (
    write_to_special_lta,
    write_to_special_lta_with_segments,
    write_to_special_lta_with_calculated_data
)
from src.core.data_processing import calculate_user_stats, update_user_stats_with_daily_data

@pytest.fixture
def mock_storage_client():
    """Mock Google Cloud Storage 客戶端"""
    with patch('src.core.cloud_integration.storage.Client') as mock_client:
        yield mock_client

@pytest.fixture
def mock_gcsfs():
    """Mock GCSFileSystem 實例"""
    with patch('src.core.cloud_integration.gcsfs.GCSFileSystem') as mock_gcsfs:
        mock_gcsfs_instance = MagicMock()
        mock_gcsfs.return_value = mock_gcsfs_instance
        mock_gcsfs_instance.exists.return_value = False
        yield mock_gcsfs

def test_fetch_audience_mapping_with_rules(mock_storage_client):
    """測試從 GCS 獲取受眾規則的功能"""
    # 設定 mock 回傳值
    mock_rules = {
        "107": {
            "rules": {
                "tm:c_9999_107_c_001": {
                    "description": "測試規則 1",
                    "data": {"min_days": 30, "max_days": 365},
                    "rule": {
                        "and": [
                            {"!=": [{"var": "last_interaction"}, None]},
                            {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                            {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                        ]
                    }
                }
            }
        }
    }
    mock_storage_client.return_value.bucket.return_value.blob.return_value.download_as_text.return_value = json.dumps(mock_rules)

    # 執行測試
    result = fetch_audience_mapping_with_rules(107, add_to_dxp=True)

    # 驗證結果
    assert isinstance(result, dict)
    assert "tm:c_9999_107_c_001" in result
    assert "data" in result["tm:c_9999_107_c_001"]
    assert "rule" in result["tm:c_9999_107_c_001"]

    # 驗證 storage client 被正確呼叫
    mock_storage_client.return_value.bucket.assert_called_once_with('tagtoo-ml-workflow')

def test_save_user_stats_snapshot(mock_storage_client, mock_gcsfs, mock_dataframe):
    """測試儲存使用者統計資料快照的功能"""
    # 準備測試資料
    df = mock_dataframe
    ec_id = 107
    current_date = datetime(2023, 1, 1, tzinfo=timezone.utc)  # 使用過去日期

    # 設置 mock blob 對象
    mock_blob = mock_storage_client.return_value.bucket.return_value.blob.return_value
    mock_size = MagicMock()
    # 設置除法運算返回自身，允許連續除法
    mock_size.__truediv__ = MagicMock(return_value=mock_size)
    # 設置格式化方法返回固定值
    mock_size.__format__ = MagicMock(return_value="1.00")
    mock_blob.size = mock_size
    mock_blob.exists.return_value = False
    mock_blob.md5_hash = "test-md5-hash"

    # 執行測試，使用 patch 捕獲 to_parquet 方法的調用
    with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
        # 設置測試環境變數
        with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
            # 模擬 StorageManager.save_snapshot 返回成功結果
            expected_path = "LTA_TEST/user_stats_snapshots/ec_107/20230101.parquet"
            with patch('src.core.cloud_integration.StorageManager.save_snapshot', return_value={"success": True, "path": expected_path}):
                # 調用被測試的函數
                result = save_user_stats_snapshot(df, ec_id, current_date)

                # 驗證結果是字符串且符合預期路徑
                assert result == expected_path

                # 驗證 to_parquet 被調用了一次
                mock_to_parquet.assert_not_called()  # save_snapshot 被 mock 了，所以 to_parquet 不會被調用

def test_save_user_stats_snapshot_error(mock_storage_client, mock_gcsfs):
    """測試儲存使用者統計資料快照時的錯誤處理"""
    # 設定 mock 拋出異常
    mock_storage_client.return_value.bucket.return_value.blob.return_value.upload_from_filename.side_effect = Exception("Upload failed")

    # 設定 blob.size 為可以處理除法運算的 MagicMock 物件
    mock_blob = mock_storage_client.return_value.bucket.return_value.blob.return_value
    mock_size = MagicMock()
    # 設置除法運算返回自身，允許連續除法
    mock_size.__truediv__ = MagicMock(return_value=mock_size)
    # 設置格式化方法返回固定值
    mock_size.__format__ = MagicMock(return_value="1.00")
    mock_blob.size = mock_size
    mock_blob.exists.return_value = False

    # 準備測試資料 - 使用真實的 DataFrame 而非 MagicMock
    df = pd.DataFrame({
        'ec_id': [107, 107, 107],
        'permanent': ['user1', 'user2', 'user3'],
        'last_interaction_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'purchase_count': [1, 2, 3],
        'registration_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_interaction_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_purchase_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'last_purchase_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'total_purchase_amount': [100.0, 200.0, 300.0],
        'total_sessions': [10, 20, 30]
    })
    ec_id = 107
    current_date = datetime(2023, 1, 1, tzinfo=timezone.utc)  # 使用過去日期

    # 執行測試，使用 mock 捕獲錯誤
    with patch('pandas.DataFrame.to_parquet'):
        with patch('src.core.cloud_integration.logger') as mock_logger:
            with patch('tempfile.NamedTemporaryFile'):
                # 設置測試環境變數
                with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
                    # 模擬失敗場景
                    with patch('src.core.cloud_integration.StorageManager.save_snapshot', return_value={"success": False, "error": "測試錯誤"}):
                        # 調用被測試的函數，預期會拋出異常但被內部捕獲
                        result = save_user_stats_snapshot(df, ec_id, current_date)

                        # 驗證結果是否為空字符串（錯誤時的返回值）
                        assert result == ""

                        # 驗證錯誤被記錄
                        mock_logger.error.assert_called_once()

def test_write_to_special_lta_empty_segments(caplog):
    """測試當分群結果為空時的處理"""
    test_data = pd.DataFrame({
        'permanent': ['user1', 'user2'],
        'ec_id': [107, 107],
        'last_interaction_time': [
            datetime.now(timezone.utc) - timedelta(days=10),
            datetime.now(timezone.utc) - timedelta(days=20)
        ],
        'purchase_count': [1, 0],
        'total_purchase_amount': [100.0, 0.0],
        'last_interaction_days': [10, 20]
    })

    # 添加當前日期參數
    current_date = datetime.now(timezone.utc)

    # 模擬分群結果為空
    with patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic') as mock_calc:

        mock_calc.return_value = {}  # 空分群結果

        # 執行函數 - 確保傳入正確的參數順序：user_stats, ec_id, current_date
        result = write_to_special_lta(test_data, 107, current_date)

        # 驗證結果 - Update expectations for new function flow
        assert result is not None
        assert result.get('success') is True
        assert result.get('rows_written') == 0
        assert result.get('message') == "所有用戶都沒有有效的 segments"

        # 檢查日誌 - 檢查實際的日誌訊息
        assert "所有用戶都沒有有效的 'segments'" in caplog.text

def test_fetch_daily_user_stats():
    """測試獲取每日用戶數據函數"""
    # 模擬BQ客戶端
    with patch('src.core.cloud_integration.bigquery.Client') as mock_client:
        # 設置模擬返回值
        mock_query_job = MagicMock()
        mock_result = MagicMock()
        mock_result.to_dataframe.return_value = pd.DataFrame({
            'ec_id': [107, 107],
            'permanent': ['user1', 'user2'],
            'last_interaction_time': [datetime.now(timezone.utc), datetime.now(timezone.utc)],
            'daily_sessions': [2, 3],
            'active_days': [1, 1],
            'daily_purchase_count': [1, 0],
            'daily_purchase_amount': [100.0, 0.0],
            'first_purchase_of_day': [datetime.now(timezone.utc), None],
            'last_purchase_of_day': [datetime.now(timezone.utc), None],
            'first_interaction_of_day': [datetime.now(timezone.utc), datetime.now(timezone.utc)],
            'last_interaction_of_day': [datetime.now(timezone.utc), datetime.now(timezone.utc)]
        })

        mock_query_job.result.return_value = mock_result
        mock_client.return_value.query.return_value = mock_query_job

        # 初始化客戶端並調用函數
        client = BigQueryClient()
        date_range = (datetime.now(timezone.utc) - timedelta(days=1), datetime.now(timezone.utc))
        result = client.fetch_daily_user_stats(107, date_range)

        # 驗證結果
        assert not result.empty
        assert len(result) == 2
        assert 'daily_purchase_count' in result.columns
        assert 'last_interaction_of_day' in result.columns

        # 驗證查詢被調用，但不檢查調用次數
        mock_client.return_value.query.assert_called()

def test_restore_from_parquet_backup():
    """測試從 parquet 備份檔案恢復資料到 BigQuery 表格"""
    # 準備測試資料
    test_data = pd.DataFrame({
        'ec_id': [107, 107, 107],
        'permanent': ['user1', 'user2', 'user3'],
        'purchase_count': [1, 2, 3],
        'last_interaction_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'registration_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_interaction_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_purchase_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'last_purchase_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'total_purchase_amount': [100.0, 200.0, 300.0],
        'total_sessions': [10, 20, 30]
    })

    backup_file_path = 'test_backup.parquet'
    snapshot_date = '20230301'

    # 模擬將資料儲存為 parquet 檔案
    try:
        test_data.to_parquet(backup_file_path)

        # 模擬 BigQuery 客戶端和 StorageManager
        with patch('src.core.cloud_integration.bigquery.Client') as mock_bq_client, \
             patch('pandas.read_parquet') as mock_read_parquet, \
             patch('src.core.cloud_integration.StorageManager') as mock_storage_manager, \
             patch('src.utils.json_helpers.to_json_safe', return_value=test_data) as mock_json_helpers:

            # 設定 mock 讀取 parquet 返回測試資料
            mock_read_parquet.return_value = test_data

            # 設定 BigQuery 客戶端返回
            mock_bq = mock_bq_client.return_value

            # 模擬 StorageManager 回傳
            mock_storage = mock_storage_manager.return_value
            mock_storage.download_snapshot.return_value = backup_file_path

            # 初始化 BigQueryClient 並直接設置實例屬性
            client = BigQueryClient()
            # 在實例上設定屬性，而非嘗試修改類別屬性
            client.dataset_id = "test_dataset"

            # 模擬 get_table 方法會失敗，需要創建新表格
            mock_bq.get_table.side_effect = Exception("Table not found")

            # 模擬 create_user_stats_table 方法
            with patch.object(client, 'create_user_stats_table') as mock_create_table:
                # 呼叫 restore_from_backup
                result = client.restore_from_backup(snapshot_date)

                # 驗證結果
                assert result['success'] is True
                assert "成功從 snapshot_date=20230301 的備份還原數據" in result['message']

                # 驗證 download_snapshot 被正確調用
                mock_storage.download_snapshot.assert_called_with(snapshot_date, None)

                # 重置 mock
                mock_storage.reset_mock()

                # 測試指定 ec_id
                ec_id = 107
                result = client.restore_from_backup(snapshot_date, ec_id)

                # 驗證結果
                assert result['success'] is True
                assert "成功從 snapshot_date=20230301 的備份還原數據" in result['message']

                # 驗證 download_snapshot 被正確調用，並傳入 ec_id
                mock_storage.download_snapshot.assert_called_with(snapshot_date, ec_id)

                # 驗證 create_user_stats_table 被呼叫
                assert mock_create_table.call_count > 0, "create_user_stats_table 應該至少被呼叫一次"

                # 驗證 json_helpers.to_json_safe 被呼叫
                assert mock_json_helpers.call_count > 0, "to_json_safe 應該至少被呼叫一次"

                # 驗證 BigQuery API 呼叫
                assert mock_bq.load_table_from_dataframe.call_count > 0, "load_table_from_dataframe 應該至少被呼叫一次"
                assert mock_bq.load_table_from_dataframe.call_args[0][1] == 'test_dataset.user_stats'

    finally:
        # 清理測試檔案
        if os.path.exists(backup_file_path):
            os.remove(backup_file_path)

def test_check_snapshot_exists():
    """測試檢查快照是否存在的功能"""
    # 模擬 StorageManager
    with patch('src.core.cloud_integration.StorageManager') as mock_storage_manager:
        mock_instance = mock_storage_manager.return_value

        # 測試1: 快照存在
        # Mock load_snapshot 返回非空 DataFrame
        mock_instance.load_snapshot.return_value = pd.DataFrame({'test': [1, 2, 3]})

        # 初始化測試對象
        client = BigQueryClient()

        # 設定測試時間
        current_date = datetime(2024, 2, 26, tzinfo=timezone.utc)
        next_date = datetime(2024, 2, 27, tzinfo=timezone.utc)

        # 檢查快照存在
        result = client.check_snapshot_exists(107, (current_date, next_date))

        # 驗證結果
        assert result is True
        # 驗證快照載入被調用 (允許多次調用)
        assert mock_instance.load_snapshot.call_count >= 1

        # 測試2: 快照不存在
        # Mock load_snapshot 返回空 DataFrame
        mock_instance.load_snapshot.return_value = pd.DataFrame()
        mock_instance.load_snapshot.reset_mock()

        result = client.check_snapshot_exists(107, (current_date, next_date))

        # 驗證結果
        assert result is False
        assert mock_instance.load_snapshot.call_count >= 1

@pytest.fixture
def sample_df_with_nat():
    """產生一個包含 NaT 值的測試 DataFrame"""
    return pd.DataFrame({
        'permanent': ['user1', 'user2'],
        'last_interaction_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.NaT],
        'first_interaction_time': [pd.Timestamp('2022-12-01', tz='UTC'), pd.NaT],
        'registration_time': [pd.NaT, pd.Timestamp('2023-02-01', tz='UTC')],
        'first_purchase_time': [pd.Timestamp('2023-01-15', tz='UTC'), pd.NaT],
        'purchase_count': [5, 0],
        'total_purchase_amount': [1000.0, 0.0],
        'ec_id': [107, 107],  # 使用正確的 ec_id
        'total_sessions': [10, 0]  # 確保 total_sessions 欄位存在
    })

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_handles_nat(mock_auth, mock_fetch, mock_calc, mock_bigquery, sample_df_with_nat):
    """測試 write_to_special_lta 能正確處理 NaT 值"""
    # 設置 mock
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client

    # 設置 load_table_from_dataframe 的 mock 返回值
    mock_job = MagicMock()
    mock_job.output_rows = 1
    mock_client.load_table_from_dataframe.return_value = mock_job

    # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
    mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

    # 設置 segments 返回值 - 確保用戶 ID 匹配測試數據
    mock_calc.return_value = {'tm:c_9999_107_c_001': ['user1']}

    # 執行函數
    ec_id = 107  # Use valid EC ID that exists in mock rules
    current_date = datetime(2023, 3, 1, tzinfo=timezone.utc)

    # 調用函數
    with patch('src.core.cloud_integration.logger'):  # 避免日誌輸出
        result = write_to_special_lta(sample_df_with_nat, ec_id, current_date)

    # 驗證 mock 被調用
    mock_bigquery.Client.assert_called_once()

    # 驗證返回結果
    assert isinstance(result, dict)
    assert result['success'] is True

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_datetime_handling(mock_auth, mock_fetch, mock_calc, mock_bigquery, sample_df_with_nat):
    """測試 write_to_special_lta 對日期時間格式的處理"""
    # 設置 mock
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client

    # 設置 load_table_from_dataframe 的 mock 返回值
    mock_job = MagicMock()
    mock_job.output_rows = 1
    mock_client.load_table_from_dataframe.return_value = mock_job

    # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
    mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

    # 設置 segments 返回值 - 確保用戶 ID 匹配測試數據
    mock_calc.return_value = {'tm:c_9999_107_c_001': ['user1']}

    # 執行函數
    ec_id = 107  # Use valid EC ID that exists in mock rules
    current_date = datetime(2023, 3, 1, tzinfo=timezone.utc)

    # 調用函數
    with patch('src.core.cloud_integration.logger'):  # 避免日誌輸出
        result = write_to_special_lta(sample_df_with_nat, ec_id, current_date)

    # 驗證 mock 被調用
    mock_bigquery.Client.assert_called_once()

    # 驗證函數成功執行
    assert result['success'] is True

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('src.optimization.memory_optimizer.ArrowDataProcessor')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_empty_audience_df(mock_auth, mock_arrow_processor, mock_fetch_rules, mock_bigquery):
    """測試當 fetch_audience_mapping_with_rules 返回空的 DataFrame 時，函數的行為。
    """
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client
    mock_arrow_instance = MagicMock()
    mock_arrow_processor.return_value = mock_arrow_instance

    # Mock fetch_audience_mapping_with_rules to return an empty DataFrame
    mock_fetch_rules.return_value = pd.DataFrame(columns=['ec_id', 'segments', 'permanent'])

    initial_audience_df = pd.DataFrame({
        'permanent': ['user1'],
        'ec_id': [107],
        'last_interaction_time': [pd.Timestamp('2023-10-26 10:00:00', tz='UTC')],
        'purchase_count': [0]  # Add required field but with no purchases to trigger empty segments
    })
    ec_id = 107
    current_date_utc = datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc)

    with patch('src.core.cloud_integration.logger') as mock_logger:
        result = write_to_special_lta(initial_audience_df, ec_id, current_date_utc)

    # Update expected log message to match new function flow
    expected_date = current_date_utc.astimezone(pytz.timezone('Asia/Taipei')).strftime('%Y%m%d')
    mock_logger.info.assert_any_call(f"EC ID {ec_id} 在日期 {expected_date} 所有用戶都沒有有效的 'segments'。")
    mock_client.load_table_from_dataframe.assert_not_called()
    assert result['success'] is True # Still considered success as it's a valid scenario
    assert result['rows_written'] == 0
    assert result['message'] == "所有用戶都沒有有效的 segments"

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_no_segments_after_explode(mock_auth, mock_fetch_rules, mock_calc, mock_bigquery):
    """測試當 DataFrame 在 explode 之後變為空時的行為 (例如，segments 列表為空)。"""
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client

    # 模擬 calculate_audience_segments_dynamic 返回一個空的字典
    # 這會導致後續的 segment_data 為空，觸發我們想要測試的邏輯
    mock_calc.return_value = {}

    # 雖然 fetch_rules 被 mock 了，但它的返回值在這個測試路徑中不重要
    # 因為 mock_calc 會提前返回空值
    mock_fetch_rules.return_value = {'some_rule': {'rule': '...'} }


    initial_audience_df = pd.DataFrame({
        'permanent': ['user1'],
        'ec_id': [107],
        'last_interaction_time': [pd.Timestamp('2023-10-26 10:00:00', tz='UTC')],
        'purchase_count': [1]  # 即使有購買記錄，mock_calc 也會返回空
    })
    ec_id = 107
    current_date_utc = datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc)

    with patch('src.core.cloud_integration.logger') as mock_logger:
        result = write_to_special_lta(initial_audience_df, ec_id, current_date_utc)

    # 驗證日誌訊息
    expected_date = current_date_utc.astimezone(pytz.timezone('Asia/Taipei')).strftime('%Y%m%d')
    mock_logger.info.assert_any_call(f"EC ID {ec_id} 在日期 {expected_date} 所有用戶都沒有有效的 'segments'。")

    # 驗證其他行為
    mock_client.load_table_from_dataframe.assert_not_called()
    assert result['success'] is True
    assert result['rows_written'] == 0
    assert "所有用戶都沒有有效的 segments" in result['message']

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_timezone_handling(mock_auth, mock_fetch, mock_calc, mock_bigquery):
    """測試 write_to_special_lta 的時區處理邏輯"""
    # 設置 mock
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client

    # 設置 load_table_from_dataframe 的 mock 返回值
    mock_job = MagicMock()
    mock_job.output_rows = 1
    mock_client.load_table_from_dataframe.return_value = mock_job

    # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
    mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

    # 設置 segments 返回值 - 確保用戶 ID 匹配測試數據
    mock_calc.return_value = {'tm:c_9999_107_c_001': ['user1']}

    # 建立測試資料
    test_data = pd.DataFrame({
        'permanent': ['user1'],
        'ec_id': [107],
        'last_interaction_time': [pd.Timestamp('2023-03-01T00:00:00Z')],
        'purchase_count': [1]
    })

    # 執行函數
    current_date = datetime(2025, 3, 4, tzinfo=timezone.utc)
    result = write_to_special_lta(test_data, 107, current_date)

    # 驗證結果
    assert result['success'] is True

    # 驗證 BigQuery 被調用
    mock_bigquery.Client.assert_called_once()

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_with_segments_structure(mock_auth, mock_fetch, mock_calc, mock_bigquery):
    """測試 write_to_special_lta_with_segments 的結構"""
    mock_client = MagicMock()

    # 設置 load_table_from_dataframe 的 mock 返回值
    mock_job = MagicMock()
    mock_job.output_rows = 2  # 兩個用戶
    mock_client.load_table_from_dataframe.return_value = mock_job

    # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
    mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

    # 設置 mock_calc 來回傳一個字典格式的分群結果
    mock_calc.return_value = {
        'tm:c_9999_107_c_001': ['user1', 'user2']  # 包含兩個用戶
    }

    captured_df = None

    class CaptureDF:
        def __init__(self):
            self.captured_df = None

        def __call__(self, df, table_id, job_config=None):
            self.captured_df = df
            return mock_job

    capture_df = CaptureDF()
    mock_bigquery.Client.return_value = mock_client
    mock_client.load_table_from_dataframe.side_effect = capture_df

    # 建立測試資料
    test_data = pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3'],
        'ec_id': [107, 107, 107],
        'last_interaction_time': [
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC')
        ],
        'purchase_count': [3, 0, 1]
    })

    # 執行函數
    current_date = pd.Timestamp('2025-03-03 16:00:00', tz='UTC')
    result = write_to_special_lta(test_data, 107, current_date)

    # 驗證結果
    assert result['success'] is True

    # 檢查 captured_df
    captured_df = capture_df.captured_df
    assert captured_df is not None

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_logic(mock_auth, mock_fetch_rules, mock_calc, mock_bigquery):
    """
    測試 write_to_special_lta 的核心邏輯，包括欄位選擇、explode 和寫入。
    """
    # 設置 mock
    mock_client = MagicMock()
    mock_load_job = MagicMock()
    mock_load_job.output_rows = 5
    mock_client.load_table_from_dataframe.return_value = mock_load_job
    mock_bigquery.Client.return_value = mock_client

    # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
    mock_fetch_rules.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        },
        'tm:c_9999_107_c_002': {
            'description': '測試規則 - 有購買記錄的用戶（分群2）',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 1]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

    # 設置 mock_calc 返回字典格式的分群結果，確保用戶 ID 匹配
    mock_calc.return_value = {
        'tm:c_9999_107_c_001': ['user1', 'user2', 'user3'],  # 3 users
        'tm:c_9999_107_c_002': ['user1', 'user3']  # 2 users, total = 5 rows
    }

    # 準備測試資料
    ec_id = 107
    current_date = datetime(2025, 6, 24, tzinfo=timezone.utc)  # 使用較新的日期以啟用追蹤欄位
    test_df = pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3'],
        'ec_id': [ec_id, ec_id, ec_id],
        'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-02-01', '2023-01-15']),
        'total_sessions': [10, 20, 5],
        'purchase_count': [2, 0, 1]
    })

    # 執行函數
    with patch('src.core.cloud_integration.logger'):
        result = write_to_special_lta(test_df, ec_id, current_date, add_to_dxp=False)

    # 驗證結果
    assert result['success'] is True
    assert result['rows_written'] == 5

    # 驗證 BigQuery 調用
    mock_client.load_table_from_dataframe.assert_called_once()
    called_df = mock_client.load_table_from_dataframe.call_args[0][0]
    job_config_arg = mock_client.load_table_from_dataframe.call_args[1]['job_config']

    # 驗證 DataFrame 結構
    assert isinstance(called_df, pd.DataFrame)
    assert len(called_df) == 5  # user1 in 2 segments, user2 in 1, user3 in 2 - 總共5行

    # 驗證最終欄位
    expected_columns = {'permanent', 'segment_id', 'created_at', 'source_type', 'source_entity', 'execution_id'}
    assert set(called_df.columns) == expected_columns

    # 驗證 Schema - 修正比較方式
    expected_schema = [
        mock_bigquery.SchemaField("permanent", "STRING", mode="REQUIRED"),
        mock_bigquery.SchemaField("segment_id", "STRING", mode="NULLABLE"),
        mock_bigquery.SchemaField("created_at", "TIMESTAMP", mode="NULLABLE"),
        mock_bigquery.SchemaField("source_type", "STRING", mode="NULLABLE"),
        mock_bigquery.SchemaField("source_entity", "STRING", mode="NULLABLE"),
        mock_bigquery.SchemaField("execution_id", "STRING", mode="NULLABLE"),
    ]

    # 檢查 schema 長度
    assert len(job_config_arg.schema) == len(expected_schema)

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_write_to_special_lta_with_calculated_data_structure(mock_auth, mock_calc, mock_bigquery, caplog):
    """測試 write_to_special_lta_with_calculated_data 函數中 segment_data 的結構是否正確，確保每個 permanent 對應多個 segment_id"""
    # 設置 mock
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client
    mock_job = MagicMock()
    mock_client.load_table_from_dataframe.return_value = mock_job
    mock_job.output_rows = 5  # Mock successful write

    # 捕獲 DataFrame 參數
    class CaptureDF:
        def __init__(self):
            self.called_with_df = None
        def __call__(self, df, table_id, job_config=None):
            self.called_with_df = df
            return mock_job

    capture_df = CaptureDF()
    mock_client.load_table_from_dataframe.side_effect = capture_df

    # 準備測試資料
    test_data = pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3'],
        'ec_id': [107, 107, 107],
        'last_interaction_time': [
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC')
        ],
        'first_interaction_time': [
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC'),
            pd.Timestamp('2025-03-03 16:00:00', tz='UTC')
        ],
        'purchase_count': [3, 0, 1]  # Added required field for audience segmentation
    })

    # 準備已計算的分群資料 - 其中每個用戶都屬於不同的多個分群
    daily_segments = {
        'tm:c_9999_107_c_001': ['user1', 'user2'],
        'tm:c_9999_107_c_002': ['user1', 'user3'],
        'tm:c_9999_107_c_003': ['user2', 'user3']
    }

    # 執行函數
    current_date = pd.Timestamp('2025-03-03 16:00:00', tz='UTC')
    result = write_to_special_lta_with_calculated_data(test_data, 107, current_date, daily_segments)

    # 檢查結果
    assert result['success'] is True
    # 注意：新的函數結構不再返回 segment_counts，因為它直接調用 write_to_special_lta
    assert 'rows_written' in result

    # 檢查是否有分群結果，如果沒有則跳過寫入驗證
    if result['rows_written'] == 0:
        # 確認訊息包含正確的內容
        assert '所有用戶都沒有有效的' in result['message']
        assert '所有用戶都沒有有效的' in caplog.text
        # 如果沒有資料寫入，就跳過 DataFrame 結構檢查
        return
    else:
        assert result['rows_written'] > 0  # 應該有資料被寫入

    # 檢查 DataFrame 結構 - 確保調用 load_table_from_dataframe 時傳入的 DataFrame 結構正確
    df = capture_df.called_with_df
    assert df is not None, "DataFrame should have been captured"

    # 驗證每個 permanent 和 segment_id 組合都有一筆記錄
    # 每個用戶應該有多筆記錄，每筆對應一個 segment_id
    assert len(df) > 3  # 應該有超過3筆記錄，因為每個用戶屬於多個分群
    assert len(df['permanent'].unique()) == 3  # permanent 欄位中有3個不同的值

    # 檢查每個用戶的 segment_id 記錄
    user1_segments = set(df[df['permanent'] == 'user1']['segment_id'].tolist())
    user2_segments = set(df[df['permanent'] == 'user2']['segment_id'].tolist())
    user3_segments = set(df[df['permanent'] == 'user3']['segment_id'].tolist())

    assert user1_segments == {'tm:c_9999_107_c_001', 'tm:c_9999_107_c_002'}
    assert user2_segments == {'tm:c_9999_107_c_001', 'tm:c_9999_107_c_003'}
    assert user3_segments == {'tm:c_9999_107_c_002', 'tm:c_9999_107_c_003'}

    # 驗證表格 ID 使用台灣時間的下一天 (UTC+8)
    # This function's internal logic for table name uses (taipei_current_date + 1 day).strftime
    expected_date = (current_date.astimezone(pytz.timezone('Asia/Taipei')) + timedelta(days=1)).strftime('%Y%m%d')
    expected_table_id = f'tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{expected_date}'
    mock_client.load_table_from_dataframe.assert_called_once()
    args, kwargs = mock_client.load_table_from_dataframe.call_args
    assert args[1] == expected_table_id

@patch('src.core.cloud_integration.bigquery')
@patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic')
@patch('google.auth.default', return_value=(None, None)) # Mock credentials
def test_segment_data_prevents_merging_all_segments(mock_auth, mock_calc, mock_bigquery):
    """
    測試: 當分群結果產生多個規則有相同用戶數時，是否能正確處理。
    """
    # ... (setup mocks) ...
    mock_client = MagicMock()
    mock_bigquery.Client.return_value = mock_client

    captured_df = None
    def capture_df(df, table_id, job_config=None):
        nonlocal captured_df
        captured_df = df
        mock_job = MagicMock()
        mock_job.output_rows = len(df)
        return mock_job
    mock_client.load_table_from_dataframe.side_effect = capture_df

    # 準備一個有多個用戶和欄位的 DataFrame
    user_stats_df = pd.DataFrame({
        'permanent': [f'user{i}' for i in range(10)],
        'ec_id': [107] * 10,
        'purchase_count': [1, 2, 0, 0, 1, 2, 0, 1, 0, 1],
        'last_interaction_time': [pd.Timestamp('2025-03-03T16:00:00Z')] * 10,
        'first_interaction_time': [pd.Timestamp('2025-03-03T16:00:00Z')] * 10
    })

    # 模擬一個合理的分群結果
    mock_calc.return_value = {
        'purchasers': ['user0', 'user1', 'user4', 'user5', 'user7', 'user9'], # 6 users
        'non-purchasers': ['user2', 'user3', 'user6', 'user8'] # 4 users
    }

    # 執行函數，確保 patch 被正確應用
    with patch('src.core.cloud_integration.calculate_audience_segments_dynamic', mock_calc):
        result = write_to_special_lta(
            user_stats_df,
            107,
            datetime(2025, 3, 3, 16, 0, 0, tzinfo=timezone.utc)
        )

    # 驗證
    assert result['success']
    assert captured_df is not None

    # 驗證 segment_id 欄位的內容（DataFrame已經是exploded格式）
    df = captured_df

    # 總共應該有 10 個用戶被寫入 (6+4)
    assert len(df['permanent'].unique()) == 10

    # purchasers 群組應該有 6 個用戶
    assert len(df[df['segment_id'] == 'purchasers']['permanent'].unique()) == 6
    # non-purchasers 群組應該有 4 個用戶
    assert len(df[df['segment_id'] == 'non-purchasers']['permanent'].unique()) == 4
