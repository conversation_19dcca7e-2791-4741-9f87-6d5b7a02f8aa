import pytest
from datetime import datetime, timezone
from src.core.queries import check_ec_data_query, base_user_stats_query, daily_user_stats_query

def test_check_ec_data_query():
    """測試檢查電商數據的查詢函數是否生成正確的SQL查詢"""
    current_time = datetime.now(timezone.utc)
    query = check_ec_data_query(107, current_time, current_time)

    # 檢查查詢中是否包含關鍵語句
    assert "FROM `tagtoo-tracking.event_prod.tagtoo_event`" in query
    assert "WHERE ec_id = 107" in query
    assert "COUNT" in query

def test_base_user_stats_query():
    """測試基礎用戶統計查詢函數是否生成正確的SQL查詢"""
    current_time = datetime.now(timezone.utc)
    query = base_user_stats_query(107, current_time, current_time)

    # 檢查查詢中是否包含關鍵語句
    assert "FROM `tagtoo-tracking.event_prod.tagtoo_event`" in query
    assert "WHERE ec_id = 107" in query
    assert "purchase_count" in query
    assert "first_interaction_time" in query
    assert "last_interaction_time" in query
    assert "GROUP BY permanent" in query

def test_daily_user_stats_query():
    """測試每日用戶統計查詢函數是否生成正確的SQL查詢"""
    current_time = datetime.now(timezone.utc)
    query = daily_user_stats_query(107, current_time, current_time)

    # 檢查查詢中是否包含關鍵語句
    assert "FROM `tagtoo-tracking.event_prod.tagtoo_event`" in query
    assert "WHERE ec_id = 107" in query
    assert "daily_purchase_count" in query
    assert "daily_purchase_amount" in query
    assert "daily_sessions" in query
    assert "GROUP BY permanent" in query

def test_daily_user_stats_query_field_names():
    """測試每日用戶統計查詢的欄位名稱是否符合預期

    這個測試專門用於確保SQL查詢中的欄位名稱與資料處理代碼中預期的欄位名稱一致，
    特別是 first_interaction_of_day 和 last_interaction_of_day 欄位。
    """
    import re

    current_time = datetime.now(timezone.utc)
    query = daily_user_stats_query(107, current_time, current_time)

    # 使用正則表達式從查詢中提取欄位名稱
    field_patterns = [
        r'MIN\(event_time\) as ([\w_]+)',  # 匹配 MIN(event_time) as X
        r'MAX\(event_time\) as ([\w_]+)'   # 匹配 MAX(event_time) as X
    ]

    # 從SQL查詢中提取欄位名稱
    first_interaction_match = re.search(field_patterns[0], query)
    last_interaction_match = re.search(field_patterns[1], query)

    assert first_interaction_match, "找不到 MIN(event_time) as X 模式的欄位"
    assert last_interaction_match, "找不到 MAX(event_time) as X 模式的欄位"

    first_field_name = first_interaction_match.group(1)
    last_field_name = last_interaction_match.group(1)

    # 確認欄位名稱符合預期
    assert first_field_name == 'first_interaction_of_day', f"首次互動時間欄位名稱應為 first_interaction_of_day，實際為 {first_field_name}"
    assert last_field_name == 'last_interaction_of_day', f"最後互動時間欄位名稱應為 last_interaction_of_day，實際為 {last_field_name}"

    # 確認所有必要的欄位都存在
    required_fields = [
        'ec_id',
        'daily_sessions',
        'active_days',
        'daily_purchase_count',
        'daily_purchase_amount',
        'first_purchase_of_day',
        'last_purchase_of_day',
        'first_interaction_of_day',
        'last_interaction_of_day'
    ]

    for field in required_fields:
        assert f"as {field}" in query, f"查詢中應該包含欄位 {field}"

    # 特別檢查 permanent 欄位，它是直接使用而不是 as permanent
    assert "\n        permanent," in query, "查詢中應該直接包含 permanent 欄位"