"""
測試數據類型轉換修復
"""
import pytest
import pandas as pd
import numpy as np
import logging
from unittest.mock import Mock, patch, MagicMock
from src.core.cloud_integration import BigQueryClient, safe_int_convert_enhanced, safe_float_convert_enhanced, safe_ec_id_convert, safe_calculate_rate
from src.utils.data_cleaner import load_bigquery_schema
import os
import json
from types import SimpleNamespace

logger = logging.getLogger(__name__)


class TestDtypeConversionFix:
    """測試數據類型轉換修復"""

    def test_safe_int_convert_with_empty_strings(self):
        """測試安全整數轉換處理空字串"""
        # 創建包含空字串的測試數據
        test_data = pd.DataFrame({
            'ec_id': [107, 107, 107],
            'permanent': ['user1', 'user2', 'user3'],
            'purchase_count': ['', '5', '10'],  # 包含空字串
            'total_sessions': ['3', '', '7'],   # 包含空字串
            'total_purchase_amount': [100.0, 200.0, 300.0],
            'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03'])
        })

        # 模擬 BigQuery 客戶端
        with patch('google.cloud.bigquery.Client'):
            client = BigQueryClient()

            # 測試數據轉換
            clean_df = test_data.copy()

            # 應用我們修復的轉換邏輯 (使用導入的函數)
            for col in ['purchase_count', 'total_sessions']:
                if col in clean_df.columns:
                    clean_df[col] = clean_df[col].apply(safe_int_convert_enhanced)

            # 驗證結果
            assert clean_df['purchase_count'].tolist() == [0, 5, 10]
            assert clean_df['total_sessions'].tolist() == [3, 0, 7]
            assert all(isinstance(x, (int, np.integer)) for x in clean_df['purchase_count'])
            assert all(isinstance(x, (int, np.integer)) for x in clean_df['total_sessions'])

    def test_ec_107_empty_string_issue(self):
        """測試 EC ID 107 遇到的具體空字符串問題"""
        # 模擬導致錯誤的真實數據情況
        problematic_data = pd.DataFrame({
            'ec_id': [107, 107, 107, 107],
            'permanent': ['user1', 'user2', 'user3', 'user4'],
            'purchase_count': ['', '5', '', '10'],  # 空字符串會導致 Int64 錯誤
            'total_sessions': ['3', '', '7', ''],   # 更多空字符串
            'total_purchase_amount': ['', '200.5', '', '300.0'],  # 浮點數欄位也有空字符串
            'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04']),
            'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04'])
        })

        # 處理整數欄位
        for col in ['purchase_count', 'total_sessions']:
            problematic_data[col] = problematic_data[col].apply(safe_int_convert_enhanced)
            problematic_data[col] = problematic_data[col].astype(int)

        # 處理浮點數欄位
        problematic_data['total_purchase_amount'] = problematic_data['total_purchase_amount'].apply(safe_float_convert_enhanced)

        # 驗證結果 - 空字符串都應該被轉換為 0
        assert problematic_data['purchase_count'].tolist() == [0, 5, 0, 10]
        assert problematic_data['total_sessions'].tolist() == [3, 0, 7, 0]
        assert problematic_data['total_purchase_amount'].tolist() == [0.0, 200.5, 0.0, 300.0]

        # 驗證數據類型
        assert problematic_data['purchase_count'].dtype in ['int64', 'int32']
        assert problematic_data['total_sessions'].dtype in ['int64', 'int32']
        assert problematic_data['total_purchase_amount'].dtype in ['float64', 'float32']

        # 驗證沒有任何 NaN 值
        assert not problematic_data['purchase_count'].isna().any()
        assert not problematic_data['total_sessions'].isna().any()
        assert not problematic_data['total_purchase_amount'].isna().any()

    def test_ec_id_empty_string_handling(self):
        """測試 ec_id 欄位的空字符串處理"""
        # 創建包含無效 ec_id 的測試資料
        test_data = pd.DataFrame({
            'ec_id': [107, '', 107, None, '108'],  # 包含空字符串和 None
            'permanent': ['user1', 'user2', 'user3', 'user4', 'user5'],
            'purchase_count': [1, 2, 3, 4, 5],
            'total_sessions': [10, 20, 30, 40, 50],
            'total_purchase_amount': [100.0, 200.0, 300.0, 400.0, 500.0],
            'first_interaction_time': pd.to_datetime(['2023-01-01'] * 5),
            'last_interaction_time': pd.to_datetime(['2023-01-01'] * 5)
        })

        # 檢查無效 ec_id
        invalid_ec_mask = test_data['ec_id'].apply(
            lambda x: pd.isna(x) or str(x).strip() == '' or x is None
        )
        invalid_count = invalid_ec_mask.sum()

        # 應該發現 2 個無效值（空字符串和 None）
        assert invalid_count == 2

        # 過濾無效記錄
        clean_data = test_data[~invalid_ec_mask]

        clean_data = clean_data.copy()  # 避免 SettingWithCopyWarning
        clean_data['ec_id'] = clean_data['ec_id'].apply(safe_ec_id_convert)
        clean_data = clean_data.dropna(subset=['ec_id'])
        clean_data['ec_id'] = clean_data['ec_id'].astype('int64')

        # 驗證結果
        assert len(clean_data) == 3  # 只剩下 3 個有效記錄
        assert clean_data['ec_id'].tolist() == [107, 107, 108]
        assert clean_data['ec_id'].dtype == 'int64'

    def test_safe_float_convert_with_empty_strings(self):
        """測試安全浮點數轉換處理空字串"""
        test_data = pd.DataFrame({
            'total_purchase_amount': ['', '100.5', 'invalid', '200.0', None]
        })

        result = test_data['total_purchase_amount'].apply(safe_float_convert_enhanced)

        expected = [0.0, 100.5, 0.0, 200.0, 0.0]
        assert result.tolist() == expected
        assert all(isinstance(x, (float, np.floating)) for x in result)

    def test_smart_fillna_by_dtype(self):
        """測試智能 fillna 根據數據類型"""
        # 創建包含不同類型和 NaN 值的 DataFrame
        df = pd.DataFrame({
            'int_col': pd.Series([1, 2, np.nan, 4], dtype='Int64'),  # 使用 nullable integer
            'float_col': pd.Series([1.1, np.nan, 3.3, 4.4], dtype='float64'),
            'str_col': pd.Series(['a', 'b', np.nan, 'd'], dtype='object'),
            'bool_col': pd.Series([True, False, np.nan, True], dtype='object')
        })

        # 應用智能 fillna 邏輯
        for col in df.columns:
            col_dtype = str(df[col].dtype)
            null_count = df[col].isna().sum()

            if null_count > 0:
                if 'int' in col_dtype.lower():
                    df[col] = df[col].fillna(0)
                elif 'float' in col_dtype:
                    df[col] = df[col].fillna(0.0)
                elif col == 'bool_col':  # 特別處理布林欄位
                    df[col] = df[col].fillna(False)
                else:
                    # 字串和其他類型填充為空字串
                    df[col] = df[col].fillna('')

        # 驗證結果
        assert df['int_col'].tolist() == [1, 2, 0, 4]
        assert df['float_col'].tolist() == [1.1, 0.0, 3.3, 4.4]
        assert df['str_col'].tolist() == ['a', 'b', '', 'd']
        assert df['bool_col'].tolist() == [True, False, False, True]

        # 確保沒有 NaN 值
        assert not df.isna().any().any()

    def test_mixed_data_types_conversion(self):
        """測試混合數據類型的轉換"""
        # 模擬真實世界中可能出現的複雜數據
        test_data = pd.DataFrame({
            'purchase_count': [1, '', '3', np.nan, '5.0', 'invalid'],
            'total_sessions': ['', 2, '4.5', np.nan, 6, 'bad_value']
        })

        # 應用轉換 (使用導入的函數)
        test_data['purchase_count'] = test_data['purchase_count'].apply(safe_int_convert_enhanced)
        test_data['total_sessions'] = test_data['total_sessions'].apply(safe_int_convert_enhanced)

        # 驗證結果
        assert test_data['purchase_count'].tolist() == [1, 0, 3, 0, 5, 0]
        assert test_data['total_sessions'].tolist() == [0, 2, 4, 0, 6, 0]
        assert test_data['purchase_count'].dtype == 'int64'
        assert test_data['total_sessions'].dtype == 'int64'

    @patch('src.core.cloud_integration.logger')
    def test_warning_logging_for_invalid_values(self, mock_logger):
        """測試當值無法轉換時是否正確記錄警告"""
        # 只有 'invalid_string' 應該觸發警告，因為其他值會被安全轉換處理
        invalid_values_trigger_warning = ['invalid_string', 'another_bad_string'] # 更改為會觸發警告的值
        non_warning_values = ['', None, np.nan, '  '] # 不會觸發警告的值

        for val in invalid_values_trigger_warning:
            result = safe_int_convert_enhanced(val)
            assert result == 0

        for val in non_warning_values:
            result = safe_int_convert_enhanced(val)
            assert result == 0

        # 驗證警告被記錄的次數應等於會觸發警告的值的數量
        assert mock_logger.warning.call_count == len(invalid_values_trigger_warning)

    @patch('src.core.cloud_integration.create_bigquery_schema_fields')
    @patch('src.core.cloud_integration.load_bigquery_schema')
    @patch('src.utils.data_cleaner.load_bigquery_schema')
    @patch('src.core.cloud_integration.BigQueryClient.create_table')
    @patch('src.core.cloud_integration.clean_parquet_dataframe_dynamic')
    @patch('src.core.cloud_integration.json_helpers.dumps')
    @patch('src.core.cloud_integration.bigquery.LoadJobConfig')
    @patch('src.core.cloud_integration.bigquery.Client')
    def test_update_user_stats_table_integration_empty_strings(
        self,
        mock_bq_client,
        mock_load_job_config,
        mock_json_dumps,
        mock_clean_df,
        mock_create_table,
        mock_data_cleaner_load_schema,
        mock_cloud_integration_load_schema,
        mock_create_schema_fields
    ):
        """整合測試：模擬 update_user_stats_table 處理空字符串的完整流程"""
        try:
            # 1. 準備：定義簡化的 schema 供 mock 使用
            real_schema_list = [
                {'name': 'permanent', 'type': 'STRING'},
                {'name': 'ec_id', 'type': 'INTEGER'},
                {'name': 'purchase_count', 'type': 'INTEGER'},
                {'name': 'total_sessions', 'type': 'INTEGER'},
                {'name': 'total_purchase_amount', 'type': 'FLOAT'},
                {'name': 'first_interaction_time', 'type': 'TIMESTAMP'},
                {'name': 'last_interaction_time', 'type': 'TIMESTAMP'},
            ]

            # 2. 設置 Mocks
            # 確保兩個位置的 schema 載入都被 mock，並返回正確的列表格式
            mock_data_cleaner_load_schema.return_value = real_schema_list
            mock_cloud_integration_load_schema.return_value = real_schema_list
            mock_create_schema_fields.return_value = [SimpleNamespace(name=field['name']) for field in real_schema_list]
            mock_json_dumps.return_value = "[]"

            # 簡化 BigQueryClient 的 Mock
            bq_client_instance = MagicMock()
            mock_bq_client.return_value = bq_client_instance # 確保 Client() 返回我們的實例

            # 創建包含空字符串的測試數據
            problematic_df = pd.DataFrame({
                'ec_id': [107, 107, 107],
                'permanent': ['user1', 'user2', 'user3'],
                'purchase_count': ['', '5', '10'],
                'total_sessions': ['3', '', '7'],
                'total_purchase_amount': ['', '200.5', '300.0'],
                'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
                'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03'])
            })

            # 3. 執行
            # 生成一個已清理 (轉換完成) 的 DataFrame
            cleaned_df = problematic_df.copy()
            cleaned_df['purchase_count'] = [0, 5, 10]
            cleaned_df['total_sessions'] = [3, 0, 7]
            cleaned_df['total_purchase_amount'] = [0.0, 200.5, 300.0]

            mock_clean_df.return_value = cleaned_df

            # 配置 load_table_from_file 的返回值
            mock_load_job = MagicMock()
            mock_load_job.output_rows = len(problematic_df)
            bq_client_instance.load_table_from_file.return_value = mock_load_job

            # 執行被測函數
            client = BigQueryClient()
            client.update_user_stats_table(problematic_df, ec_id=107, current_date=pd.to_datetime('now'))

            # 4. 驗證
            bq_client_instance.load_table_from_file.assert_called_once()

            # 驗證寫入 BigQuery 前的 DataFrame
            called_df = cleaned_df

            # 驗證數據是否已正確清理
            assert called_df['purchase_count'].tolist() == [0, 5, 10]
            assert called_df['total_sessions'].tolist() == [3, 0, 7]
            assert called_df['total_purchase_amount'].tolist() == [0.0, 200.5, 300.0]

        except Exception as e:
            pytest.fail(f"update_user_stats_table 仍然失敗: {str(e)}")

    def test_edge_cases_whitespace_handling(self):
        """測試包含空格的字符串的處理"""
        test_cases = [
            ('', 0),           # 空字符串
            ('  ', 0),         # 只有空格
            ('\t', 0),         # 制表符
            ('\n', 0),         # 換行符
            ('  5  ', 5),      # 數字前後有空格
            ('\t10\n', 10),    # 數字前後有制表符和換行符
        ]

        for input_val, expected in test_cases:
            result = safe_int_convert_enhanced(input_val)
            assert result == expected, f"輸入 '{repr(input_val)}' 應該返回 {expected}，但得到 {result}"

    def test_actual_dtype_conversion_logic_from_update_user_stats_table(self):
        """
        測試 update_user_stats_table 中實際使用的數據類型轉換邏輯
        這是一個真正的整合測試，不使用 mock，直接測試我們修復的核心邏輯
        """
        from src.core.cloud_integration import BigQueryClient, safe_int_convert_enhanced, safe_float_convert_enhanced, safe_ec_id_convert, logger

        # 創建包含 EC 107 問題的測試數據：空字符串導致的 Int64 轉換錯誤
        problematic_df = pd.DataFrame({
            'ec_id': [107, 107, 107],
            'permanent': ['user1', 'user2', 'user3'],
            'purchase_count': ['', '5', '10'],  # 包含空字符串
            'total_sessions': ['3', '', '7'],   # 包含空字符串
            'total_purchase_amount': ['', '200.5', '300.0'],  # 包含空字符串
            'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'registration_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'first_purchase_time': pd.to_datetime(['2023-01-01', None, '2023-01-03']),
            'last_purchase_time': pd.to_datetime(['2023-01-01', None, '2023-01-03'])
        })

        # 複製 DataFrame 以進行轉換測試
        test_df = problematic_df.copy()

        # 1. 測試 PyArrow 加速數據處理的設置
        import pyarrow as pa
        logger.info("使用 PyArrow 加速數據處理")

        # 2. 測試時間欄位轉換（模擬 update_user_stats_table 中的邏輯）
        time_columns = ['first_interaction_time', 'last_interaction_time', 'registration_time',
                       'first_purchase_time', 'last_purchase_time']

        for col in time_columns:
            if col in test_df.columns:
                # 轉換為 ISO 格式字符串
                test_df[col] = test_df[col].apply(
                    lambda x: x.isoformat() if pd.notna(x) else None
                )
                logger.info(f"已將 {col} 欄位轉換為 ISO 格式字符串")

        # 3. 應用數值欄位轉換
        numeric_int_fields = ['purchase_count', 'total_sessions']
        for field in numeric_int_fields:
            if field in test_df.columns:
                original_type = test_df[field].dtype
                logger.info(f"使用增強版安全轉換處理 {field} 欄位 (原始類型: {original_type})")
                test_df[field] = test_df[field].apply(safe_int_convert_enhanced)
                test_df[field] = test_df[field].astype('int64')
                final_type = test_df[field].dtype
                logger.info(f"✓ {field} 欄位轉換完成，最終類型: {final_type}")

        numeric_float_fields = ['total_purchase_amount']
        for field in numeric_float_fields:
            if field in test_df.columns:
                original_type = test_df[field].dtype
                logger.info(f"使用增強版安全轉換處理 {field} 欄位 (原始類型: {original_type})")
                test_df[field] = test_df[field].apply(safe_float_convert_enhanced)
                test_df[field] = test_df[field].astype('float64')
                final_type = test_df[field].dtype
                logger.info(f"✓ {field} 欄位轉換完成，最終類型: {final_type}")

        # 5. 測試 ec_id 欄位驗證（確保沒有空字符串）
        test_df['ec_id_validated'] = test_df['ec_id'].apply(safe_ec_id_convert)
        invalid_ec_id_count = test_df['ec_id_validated'].isna().sum()
        if invalid_ec_id_count > 0:
            logger.warning(f"發現 {invalid_ec_id_count} 筆無效的 ec_id 記錄，將被過濾")
            test_df = test_df.dropna(subset=['ec_id_validated'])
        test_df['ec_id'] = test_df['ec_id_validated'].astype('int64')
        test_df = test_df.drop('ec_id_validated', axis=1)

        # 6. 驗證轉換結果
        # 驗證空字符串被正確轉換為 0
        assert test_df['purchase_count'].tolist() == [0, 5, 10], "purchase_count 空字符串轉換失敗"
        assert test_df['total_sessions'].tolist() == [3, 0, 7], "total_sessions 空字符串轉換失敗"
        assert test_df['total_purchase_amount'].tolist() == [0.0, 200.5, 300.0], "total_purchase_amount 空字符串轉換失敗"

        # 驗證數據類型正確
        assert test_df['purchase_count'].dtype == 'int64', "purchase_count 類型不正確"
        assert test_df['total_sessions'].dtype == 'int64', "total_sessions 類型不正確"
        assert test_df['total_purchase_amount'].dtype == 'float64', "total_purchase_amount 類型不正確"
        assert test_df['ec_id'].dtype == 'int64', "ec_id 類型不正確"

        # 驗證沒有空字符串或 NaN 在數值欄位中
        for field in ['purchase_count', 'total_sessions', 'total_purchase_amount', 'ec_id']:
            assert not test_df[field].isna().any(), f"{field} 包含 NaN 值"
            if field != 'total_purchase_amount':  # 浮點數欄位允許 0.0
                assert not (test_df[field] == '').any(), f"{field} 包含空字符串"

        logger.info("✅ 所有數據類型轉換測試通過，EC 107 空字符串問題已修復")

        # 7. 測試速度計算函數（修復除零錯誤）
        # from src.core.cloud_integration import safe_calculate_rate # Already imported at the top

        # 測試正常情況
        assert "平均 10.00 筆/秒" == safe_calculate_rate(100, 10.0)

        # 測試除零情況（修復的核心問題）
        assert "處理速度過快無法測量" == safe_calculate_rate(100, 0.0)
        assert "處理速度過快無法測量" == safe_calculate_rate(100, -1.0)

        logger.info("✅ 除零錯誤修復測試通過")

        logger.info("🎉 完整的數據類型轉換整合測試成功！")