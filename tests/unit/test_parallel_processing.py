import pandas as pd
import pytest
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock

from src.optimization.parallel_processing import parallel_process

def test_parallel_process_with_additional_args():
    """測試平行處理函數是否正確傳遞額外參數"""
    # 準備測試資料
    test_data = pd.DataFrame({
        'id': range(100),
        'value': range(100)
    })

    # 測試函數，將會接收額外參數
    def test_func(chunk, multiplier, addition):
        chunk['result'] = chunk['value'] * multiplier + addition
        return chunk

    # 執行平行處理
    result = parallel_process(
        test_data,
        test_func,
        max_workers=2,
        chunk_size=20,
        additional_args=(2, 5)  # 額外參數：乘數和加數
    )

    # 驗證結果
    assert len(result) == 100
    assert all(result['result'] == result['value'] * 2 + 5)