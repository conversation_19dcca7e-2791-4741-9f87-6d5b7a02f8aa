"""
測試向量化規則評估器的功能和性能
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone
import json
import os
import sys

# 添加src到路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

try:
    from optimization.vectorized_rule_evaluator import (
        VectorizedRuleEvaluator,
        precompute_derived_fields,
        benchmark_evaluators,
        compare_results
    )
except ImportError:
    from src.optimization.vectorized_rule_evaluator import (
        VectorizedRuleEvaluator,
        precompute_derived_fields,
        benchmark_evaluators,
        compare_results
    )


class TestVectorizedRuleEvaluator:
    """測試向量化規則評估器"""

    @pytest.fixture
    def sample_data(self):
        """建立測試用的樣本數據"""
        np.random.seed(42)
        n_records = 1000

        current_time = pd.Timestamp.now(tz=timezone.utc)

        # 生成測試數據
        data = pd.DataFrame({
            'permanent': [f'user_{i:06d}' for i in range(n_records)],
            'purchase_count': np.random.poisson(1.5, n_records),  # 平均1.5次購買
            'last_interaction_time': [
                (current_time - pd.Timedelta(days=np.random.randint(1, 400))).isoformat()
                if np.random.random() > 0.1 else None  # 10%的用戶沒有互動記錄
                for _ in range(n_records)
            ],
            'last_view_item_time': [
                (current_time - pd.Timedelta(days=np.random.randint(1, 200))).isoformat()
                if np.random.random() > 0.2 else None
                for _ in range(n_records)
            ],
            'last_add_to_cart_time': [
                (current_time - pd.Timedelta(days=np.random.randint(1, 150))).isoformat()
                if np.random.random() > 0.4 else None
                for _ in range(n_records)
            ],
            'last_purchase_time': [
                (current_time - pd.Timedelta(days=np.random.randint(1, 300))).isoformat()
                if np.random.random() > 0.6 else None
                for _ in range(n_records)
            ]
        })

        return data

    @pytest.fixture
    def sample_rules(self):
        """建立測試用的規則"""
        return {
            "tm:c_9999_107_c_001": {
                "description": "前2個月~1年有互動的用戶",
                "data": {"min_days": 30, "max_days": 365},
                "rule": {
                    "and": [
                        {"!=": [{"var": "last_interaction_time"}, None]},
                        {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                        {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                    ]
                }
            },
            "tm:c_9999_107_c_002": {
                "description": "前2個月~1年內有互動、未購買的用戶",
                "data": {"min_days": 30, "max_days": 365},
                "rule": {
                    "and": [
                        {"==": [{"var": "purchase_count"}, 0]},
                        {"!=": [{"var": "last_interaction_time"}, None]},
                        {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                        {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                    ]
                }
            },
            "tm:c_9999_107_c_003": {
                "description": "前2個月~1年內有互動、只購買過1次的用戶",
                "data": {"min_days": 30, "max_days": 365},
                "rule": {
                    "and": [
                        {"==": [{"var": "purchase_count"}, 1]},
                        {"!=": [{"var": "last_interaction_time"}, None]},
                        {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                        {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                    ]
                }
            }
        }

    def test_precompute_derived_fields(self, sample_data):
        """測試預計算衍生欄位功能"""
        current_time = pd.Timestamp.now(tz=timezone.utc)

        # 預計算衍生欄位
        result = precompute_derived_fields(sample_data, current_time)

        # 檢查新欄位是否被創建
        expected_fields = [
            'last_interaction_days',
            'last_view_item_days',
            'last_add_to_cart_days',
            'last_purchase_days'
        ]

        for field in expected_fields:
            assert field in result.columns, f"缺少預計算欄位: {field}"

        # 檢查數據類型
        assert result['last_interaction_days'].dtype == 'int32'

        # 檢查計算邏輯
        valid_interaction_mask = result['last_interaction_time'].notna()
        assert (result.loc[valid_interaction_mask, 'last_interaction_days'] >= 0).all()
        assert (result.loc[~valid_interaction_mask, 'last_interaction_days'] == -1).all()

    def test_vectorized_rule_evaluator_basic(self, sample_data, sample_rules):
        """測試向量化規則評估器的基本功能"""
        current_time = pd.Timestamp.now(tz=timezone.utc)

        # 預計算衍生欄位
        processed_data = precompute_derived_fields(sample_data, current_time)

        # 初始化評估器
        evaluator = VectorizedRuleEvaluator()

        # 執行評估
        results = evaluator.evaluate_rules(processed_data, sample_rules)

        # 檢查結果格式
        assert isinstance(results, dict)
        assert len(results) == len(sample_rules)

        for rule_id in sample_rules.keys():
            assert rule_id in results
            assert isinstance(results[rule_id], list)
            # 檢查返回的都是有效的permanent值
            for user_id in results[rule_id]:
                assert user_id in processed_data['permanent'].values

    def test_performance_stats(self, sample_data, sample_rules):
        """測試性能統計功能"""
        current_time = pd.Timestamp.now(tz=timezone.utc)
        processed_data = precompute_derived_fields(sample_data, current_time)

        evaluator = VectorizedRuleEvaluator()
        evaluator.evaluate_rules(processed_data, sample_rules)

        stats = evaluator.get_performance_stats()

        # 檢查統計數據
        assert 'total_evaluation_time' in stats
        assert 'rules_processed' in stats
        assert 'records_processed' in stats
        assert 'vectorized_operations' in stats
        assert 'avg_time_per_record_ms' in stats
        assert 'avg_time_per_rule_sec' in stats

        assert stats['rules_processed'] == len(sample_rules)
        assert stats['records_processed'] == len(sample_data)
        assert stats['total_evaluation_time'] > 0


def create_sample_data():
    """建立測試用的樣本數據"""
    np.random.seed(42)
    n_records = 1000

    current_time = pd.Timestamp.now(tz=timezone.utc)

    # 生成測試數據
    data = pd.DataFrame({
        'permanent': [f'user_{i:06d}' for i in range(n_records)],
        'purchase_count': np.random.poisson(1.5, n_records),  # 平均1.5次購買
        'last_interaction_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 400))).isoformat()
            if np.random.random() > 0.1 else None  # 10%的用戶沒有互動記錄
            for _ in range(n_records)
        ],
        'last_view_item_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 200))).isoformat()
            if np.random.random() > 0.2 else None
            for _ in range(n_records)
        ],
        'last_add_to_cart_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 150))).isoformat()
            if np.random.random() > 0.4 else None
            for _ in range(n_records)
        ],
        'last_purchase_time': [
            (current_time - pd.Timedelta(days=np.random.randint(1, 300))).isoformat()
            if np.random.random() > 0.6 else None
            for _ in range(n_records)
        ]
    })

    return data

def create_sample_rules():
    """建立測試用的規則"""
    return {
        "tm:c_9999_107_c_001": {
            "description": "前2個月~1年有互動的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        },
        "tm:c_9999_107_c_002": {
            "description": "前2個月~1年內有互動、未購買的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"==": [{"var": "purchase_count"}, 0]},
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        },
        "tm:c_9999_107_c_003": {
            "description": "前2個月~1年內有互動、只購買過1次的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"==": [{"var": "purchase_count"}, 1]},
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        }
    }

if __name__ == "__main__":
    # 直接運行測試
    print("🧪 開始測試向量化規則評估器...")

    # 創建測試實例
    test_instance = TestVectorizedRuleEvaluator()

    # 準備測試數據
    sample_data = create_sample_data()
    sample_rules = create_sample_rules()

    # 執行基本測試
    try:
        test_instance.test_precompute_derived_fields(sample_data)
        print("✅ 預計算衍生欄位測試通過")
    except Exception as e:
        print(f"❌ 預計算衍生欄位測試失敗: {e}")
        import traceback
        traceback.print_exc()

    try:
        test_instance.test_vectorized_rule_evaluator_basic(sample_data, sample_rules)
        print("✅ 向量化評估器基本功能測試通過")
    except Exception as e:
        print(f"❌ 向量化評估器基本功能測試失敗: {e}")
        import traceback
        traceback.print_exc()

    try:
        test_instance.test_performance_stats(sample_data, sample_rules)
        print("✅ 性能統計測試通過")
    except Exception as e:
        print(f"❌ 性能統計測試失敗: {e}")
        import traceback
        traceback.print_exc()

    print("🎉 測試完成")