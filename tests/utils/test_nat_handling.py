import pandas as pd
import json
import numpy as np
from datetime import datetime
import pytest
from src.utils.json_helpers import json_serializer, dumps, to_json_safe

def test_nat_conversion_complete_flow():
    """測試 NaT 值在完整處理流程中的轉換"""
    # 1. 建立測試數據
    df = pd.DataFrame({
        'date': [pd.NaT, pd.Timestamp('2023-01-01')],
        'number': [1, 2]
    })

    # 2. 驗證原始數據含有 NaT 值
    assert pd.isna(df['date'][0])
    assert isinstance(df['date'][1], pd.Timestamp)

    # 3. 驗證未處理數據無法序列化
    records = df.to_dict('records')
    with pytest.raises(TypeError):
        json.dumps(records)

    # 4. 應用 to_json_safe 處理
    safe_df = to_json_safe(df)

    # 5. 驗證處理結果
    assert safe_df['date'][0] is None
    assert safe_df['date'][1] == '2023-01-01T00:00:00'

    # 6. 驗證現在可以序列化
    safe_records = safe_df.to_dict('records')
    json_str = json.dumps(safe_records)

    # 7. 驗證序列化結果
    assert '"date": null' in json_str or '"date":null' in json_str
    assert '"date": "2023-01-01T00:00:00"' in json_str or '"date":"2023-01-01T00:00:00"' in json_str

def test_direct_json_serializer():
    """測試直接使用 json_serializer 函數的情況"""
    # 直接使用 json_serializer 處理 NaT
    assert json_serializer(pd.NaT) is None

    # 使用 json_serializer 處理 Timestamp
    assert json_serializer(pd.Timestamp('2023-01-01')) == '2023-01-01T00:00:00'

    # 使用 json.dumps 搭配 json_serializer
    data = {'date': pd.NaT, 'value': 42}
    json_str = json.dumps(data, default=json_serializer)
    assert '"date": null' in json_str or '"date":null' in json_str

def test_combined_timestamp_types():
    """測試混合類型的時間戳記數據處理"""
    # 混合各種時間相關類型
    data = {
        'pandas_timestamp': pd.Timestamp('2023-01-01'),
        'python_datetime': datetime(2023, 1, 1),
        'pandas_nat': pd.NaT,
        'none_value': None,
        'string_date': '2023-01-01',
        'number': 42
    }

    # 使用 dumps 函數序列化
    json_str = dumps(data)

    # 驗證結果
    assert '"pandas_timestamp": "2023-01-01T00:00:00"' in json_str or '"pandas_timestamp":"2023-01-01T00:00:00"' in json_str
    assert '"python_datetime": "2023-01-01T00:00:00"' in json_str or '"python_datetime":"2023-01-01T00:00:00"' in json_str
    assert '"pandas_nat": null' in json_str or '"pandas_nat":null' in json_str
    assert '"none_value": null' in json_str or '"none_value":null' in json_str
    assert '"string_date": "2023-01-01"' in json_str or '"string_date":"2023-01-01"' in json_str
    assert '"number": 42' in json_str or '"number":42' in json_str

def test_dataframe_with_timezones():
    """測試包含帶時區的時間戳記的 DataFrame 處理"""
    # 創建帶有時區的 DataFrame
    df = pd.DataFrame({
        'utc_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.NaT],
        'local_time': [pd.Timestamp('2023-01-01', tz='Asia/Taipei'), pd.Timestamp('2023-01-02', tz='Asia/Taipei')]
    })

    # 應用 to_json_safe
    safe_df = to_json_safe(df)

    # 驗證轉換結果 - 使用更寬鬆的檢查
    # UTC 可能顯示為 +00:00 而非 UTC
    assert '2023-01-01' in safe_df['utc_time'][0]  # 檢查日期部分
    assert '+00:00' in safe_df['utc_time'][0] or 'Z' in safe_df['utc_time'][0]  # 檢查時區部分
    assert '+08:00' in safe_df['local_time'][0]  # 台北時區 (+08:00)
    assert safe_df['utc_time'][1] is None  # NaT 變成 None

    # 確認可以序列化
    json_str = json.dumps(safe_df.to_dict('records'))
    assert json_str is not None