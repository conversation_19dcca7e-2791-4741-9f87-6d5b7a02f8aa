import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import json
import os
import sys

# 加入專案根目錄到路徑
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

from src.utils import vectorized_evaluate, _legacy_vectorized_evaluate


def test_integration_evaluation():
    """測試 EC ID 2980 實際規則評估時的問題"""
    # 設置 logging
    import logging
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 建立測試數據
    df = pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3'],
        'last_interaction_time': [
            pd.Timestamp('2025-04-26', tz='UTC'),  # 昨天（30天內）
            pd.Timestamp('2025-03-27', tz='UTC'),  # 30天前
            pd.Timestamp('2024-05-27', tz='UTC')   # 365天前
        ],
        'last_interaction_days': [30, 60, 365],
        'last_view_item_time': [
            pd.Timestamp('2025-04-26', tz='UTC'),  # 昨天（30天內）
            pd.Timestamp('2024-05-27', tz='UTC'),  # 365天前
            None
        ],
        'last_view_item_days': [30, 365, -1],
        'purchase_count': [2, 0, 5]
    })

    # 定義簡單的規則
    rules = {
        'rule1': {
            'description': '最近30天有互動',
            'data': {'max_days': 30},
            'rule': {
                'and': [
                    {'!=': [{'var': 'last_interaction_time'}, None]},
                    {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
                ]
            }
        },
        'rule2': {
            'description': '最近30天有查看商品',
            'data': {'max_days': 30},
            'rule': {
                'and': [
                    {'!=': [{'var': 'last_view_item_time'}, None]},
                    {'<=': [{'var': 'last_view_item_days'}, {'var': 'max_days'}]}
                ]
            }
        },
        'rule3': {
            'description': '最近30天無互動',
            'data': {'min_days': 30},
            'rule': {
                'or': [
                    {'==': [{'var': 'last_interaction_time'}, None]},
                    {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]}
                ]
            }
        }
    }

    # 為測試數據添加規則參數
    for rule_id, rule_data in rules.items():
        for param_name, param_value in rule_data.get('data', {}).items():
            df[param_name] = param_value

    # 執行向量化評估（直接處理規則條件）
    print("\n測試數據:")
    print(df)

    # 模擬 logging_setup
    class MockLogger:
        def info(self, msg, *args, **kwargs): print(f"INFO - {msg}")
        def debug(self, msg, *args, **kwargs): print(f"DEBUG - {msg}")
        def warning(self, msg, *args, **kwargs): print(f"WARNING - {msg}")
        def error(self, msg, *args, **kwargs): print(f"ERROR - {msg}")

    class MockLoggingSetup:
        @staticmethod
        def configure_logging():
            return MockLogger()

    # 修補 logging_setup 模組
    import unittest.mock as mock
    with mock.patch('src.utils.logging_setup', MockLoggingSetup):
        # 正確的預期結果
        print("\n預期結果:")
        print("rule1 應該匹配: user1")
        print("rule2 應該匹配: user1")
        print("rule3 應該匹配: user2, user3")

        # 執行評估
        results = vectorized_evaluate(df, rules)

    # 顯示結果
    print("\n實際結果:")
    for rule_id, users in results.items():
        print(f"{rule_id}: {users}")

    # 驗證結果
    assert "rule1" in results and "user1" in results["rule1"], "rule1 應該匹配 user1"
    assert "rule2" in results and "user1" in results["rule2"], "rule2 應該匹配 user1"
    assert "rule3" in results, "rule3 應該有結果"
    assert set(["user2", "user3"]).issubset(set(results["rule3"])), "rule3 應該匹配 user2 和 user3"


if __name__ == "__main__":
    test_integration_evaluation()
