import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import json
import os
from unittest.mock import patch, MagicMock

from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic, fetch_audience_mapping_with_rules
from src.utils import vectorized_evaluate, check_rule_data_requirement
import sys
import os
# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
from tests.integration.test_audience_grouping import sample_multi_ec_data, mock_audience_rules


@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
def test_specific_users_are_correctly_classified(mock_fetch, sample_multi_ec_data, mock_audience_rules):
    """測試特定用戶是否被正確歸類到預期的規則中"""
    # 直接使用 mock 規則數據（mock_audience_rules 是一個 dict）
    all_rules = mock_audience_rules

    # 針對每個 EC ID 進行測試
    for ec_id in [2980, 3819, 3820]:
        print(f"\n===== 測試 EC ID {ec_id} 的預期用戶分類 =====")
        ec_data = sample_multi_ec_data[sample_multi_ec_data['ec_id'] == ec_id].copy()

        # 獲取特定 EC ID 的規則
        ec_rules = all_rules.get(str(ec_id), {}).get('rules', {})

        if not ec_rules:
            pytest.skip(f"找不到 EC ID {ec_id} 的規則")

        # 設置 mock
        mock_fetch.return_value = ec_rules

        # 執行向量化評估
        results = vectorized_evaluate(ec_data, ec_rules)

        # 定義預期的用戶分類 - 特意設計的用戶應該出現在對應的規則中
        expected_classifications = {}

        if ec_id == 2980:
            expected_classifications = {
                f"tm:c_9999_{ec_id}_c_001": ['user_2980_1'],  # 認知受眾 (PV)
                f"tm:c_9999_{ec_id}_c_002": ['user_2980_2'],  # 興趣/意象受眾 (Viewcontent_新訪客)
                f"tm:c_9999_{ec_id}_c_003": ['user_2980_3'],  # 興趣/意象受眾 (Viewcontent_舊訪客)
                f"tm:c_9999_{ec_id}_c_004": ['user_2980_4'],  # 潛在購買受眾 (ATC)
                f"tm:c_9999_{ec_id}_c_005": ['user_2980_5'],  # 新客群 (PC_新客)
                f"tm:c_9999_{ec_id}_c_006": ['user_2980_6'],  # 舊客群 (PC_舊客)
            }
        elif ec_id == 3819:
            expected_classifications = {
                f"tm:c_9999_{ec_id}_c_001": ['user_3819_1'],  # 認知受眾
                f"tm:c_9999_{ec_id}_c_002": ['user_3819_2'],  # 興趣/意象受眾
                f"tm:c_9999_{ec_id}_c_003": ['user_3819_3'],  # 購買
                f"tm:c_9999_{ec_id}_c_004": ['user_3819_4'],  # 活躍客群
            }
        elif ec_id == 3820:
            expected_classifications = {
                f"tm:c_9999_{ec_id}_c_001": ['user_3820_1'],  # 認知受眾
                f"tm:c_9999_{ec_id}_c_002": ['user_3820_2'],  # 興趣/意象受眾
                f"tm:c_9999_{ec_id}_c_003": ['user_3820_3'],  # 購買
                f"tm:c_9999_{ec_id}_c_004": ['user_3820_4'],  # 活躍客群
            }

        # 檢查每個規則是否包含預期的用戶
        for rule_id, expected_users in expected_classifications.items():
            # 檢查規則是否存在
            assert rule_id in results, f"規則 {rule_id} 在結果中不存在"

            actual_users = results[rule_id]
            print(f"\n規則 {rule_id}:")
            print(f"  預期用戶: {expected_users}")
            print(f"  實際用戶: {sorted(actual_users)}")

            # 檢查預期用戶是否都在規則結果中
            for user in expected_users:
                assert user in actual_users, f"預期用戶 {user} 應該在規則 {rule_id} 中，但實際沒有"

            # 顯示每個預期用戶的詳細資訊
            for user in expected_users:
                user_info = ec_data[ec_data['permanent'] == user].iloc[0].to_dict()
                print(f"\n  用戶 {user} 詳細資訊:")
                print(f"    互動天數: {user_info.get('last_interaction_days', -1)}")
                print(f"    商品點擊天數: {user_info.get('last_view_item_days', -1)}")
                print(f"    加購天數: {user_info.get('last_add_to_cart_days', -1)}")
                print(f"    購買天數: {user_info.get('last_purchase_days', -1)}")
                print(f"    購買次數: {user_info.get('purchase_count', 0)}")

class TestCheckRuleDataRequirement:
    def test_sufficient_data(self):
        """測試當資料時間範圍足夠時，不應產生警告"""
        rule_data = {
            "data": {"min_days": 30},
            "rule": {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]}
        }
        data_time_range = {
            "last_interaction_days": {"max_days": 100}
        }
        result = check_rule_data_requirement(rule_data, data_time_range)
        assert result['has_sufficient_data'] is True
        assert result['reason'] is None

    def test_insufficient_data(self):
        """測試當資料時間範圍不足時，應產生正確的繁體中文警告訊息"""
        rule_data = {
            "data": {"min_days": 60},
            "rule": {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]}
        }
        data_time_range = {
            "last_interaction_days": {"max_days": 30}
        }
        result = check_rule_data_requirement(rule_data, data_time_range)
        assert result['has_sufficient_data'] is False
        expected_reason = "規則需要超過 60 天的資料，但實際資料最大只有 30 天"
        assert result['reason'] == expected_reason

    def test_multiple_insufficient_requirements(self):
        """測試當有多個不足的時間條件時，警告訊息能正確組合"""
        rule_data = {
            "data": {"interaction_min": 90, "purchase_min": 120},
            "rule": {
                "and": [
                    {">": [{"var": "last_interaction_days"}, {"var": "interaction_min"}]},
                    {">": [{"var": "last_purchase_days"}, {"var": "purchase_min"}]}
                ]
            }
        }
        data_time_range = {
            "last_interaction_days": {"max_days": 60},
            "last_purchase_days": {"max_days": 60}
        }
        result = check_rule_data_requirement(rule_data, data_time_range)
        assert result['has_sufficient_data'] is False
        # 檢查錯誤訊息包含最大的需求天數（120天）
        assert "需要超過 120 天" in result['reason']
        assert "但實際資料最大只有 60 天" in result['reason']

    def test_no_time_requirement(self):
        """測試沒有時間條件的規則"""
        rule_data = {
            "data": {"min_purchases": 2},
            "rule": {">=": [{"var": "purchase_count"}, {"var": "min_purchases"}]}
        }
        data_time_range = {
            "last_interaction_days": {"max_days": 100}
        }
        result = check_rule_data_requirement(rule_data, data_time_range)
        assert result['has_sufficient_data'] is True
        assert result['reason'] is None
