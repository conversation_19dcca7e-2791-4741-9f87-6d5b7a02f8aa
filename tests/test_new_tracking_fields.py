import pytest
import os
from pathlib import Path


def find_project_root(start_path: Path, marker: str = 'pytest.ini') -> Path:
    """從起始路徑向上搜尋包含標記檔案的專案根目錄。"""
    current = start_path.resolve()
    # 向上遍歷直到根目錄
    while current != current.parent:
        if (current / marker).exists():
            return current
        current = current.parent
    raise FileNotFoundError(f"找不到專案根目錄標記 '{marker}' (從 '{start_path}' 開始搜尋)")


class TestNewTrackingFields:
    """測試新增的追蹤欄位功能"""

    def test_sql_query_includes_new_fields(self):
        """測試 SQL 查詢檔案是否包含新的欄位"""
        sql_file_path = None
        project_root = None
        try:
            # 使用 pathlib 和輔助函數來動態找到專案根目錄
            start_dir = Path(__file__).parent
            project_root = find_project_root(start_dir)
            sql_file_path = project_root / "sql" / "scheduled_queries" / "ec_107_product_segments.sql"

            print(f"\n動態找到的專案根目錄: {project_root}")
            print(f"嘗試讀取 SQL 檔案: {sql_file_path}")

            assert sql_file_path.exists()

        except (FileNotFoundError, AssertionError):
            # 如果找不到檔案或根目錄，提供詳細的除錯資訊
            print("\n--- DEBUGGING INFORMATION ---")
            if not project_root:
                print(f"錯誤: 未能找到專案根目錄 (從 {Path(__file__).parent} 開始尋找 '{'pytest.ini'}')")
            else:
                print(f"在專案根目錄 '{project_root}' 中找不到 SQL 檔案。")
                print("\n專案根目錄內容:")
                try:
                    for item in sorted(os.listdir(project_root)):
                        print(f"  - {item}")
                except Exception as list_err:
                    print(f"    無法列出目錄: {list_err}")

                sql_dir = project_root / "sql"
                if sql_dir.exists() and sql_dir.is_dir():
                    print("\n'sql' 目錄內容:")
                    try:
                        for item in sorted(os.listdir(sql_dir)):
                            print(f"  - {item}")
                    except Exception as list_err:
                        print(f"    無法列出 'sql' 目錄: {list_err}")
                else:
                    print("\n'sql' 目錄不存在或不是一個目錄。")

            print("---------------------------\n")
            pytest.fail(f"無法在預期路徑找到 SQL 檔案: {sql_file_path}")


        # 讀取檔案內容
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 檢查是否包含新的欄位
        expected_fields = [
            'created_at',
            'source_type',
            'source_entity',
            'execution_id'
        ]

        for field in expected_fields:
            assert field in sql_content, f"SQL 檔案中缺少欄位: {field}"

        # 檢查 source_type 是否設定為 'keyword'
        assert "'keyword' AS source_type" in sql_content

        # 檢查 source_entity 是否設定正確
        assert "'ec_107_product_segments_scheduler' AS source_entity" in sql_content

        # 檢查是否使用新的統一 execution_id 設計
        assert "WITH execution_info AS (" in sql_content
        assert "CONCAT('ec_107_segments_', FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP())) AS execution_id" in sql_content
        assert "execution_info.execution_id" in sql_content