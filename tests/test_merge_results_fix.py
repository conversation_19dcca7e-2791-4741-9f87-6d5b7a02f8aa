import pytest
import logging
from unittest.mock import patch
from src.utils import merge_results
from collections import defaultdict


class TestMergeResultsFix:
    """測試 merge_results 函數的修復"""

    def test_merge_results_normal_dict_format(self):
        """測試正常的字典格式輸入"""
        results = [
            {'rule1': ['user1', 'user2'], 'rule2': ['user3']},
            {'rule1': ['user4'], 'rule3': ['user5', 'user6']},
            {'rule2': ['user7']}
        ]

        merged = merge_results(results)

        assert merged == {
            'rule1': ['user1', 'user2', 'user4'],
            'rule2': ['user3', 'user7'],
            'rule3': ['user5', 'user6']
        }

    def test_merge_results_with_none_values(self):
        """測試包含 None 值的輸入"""
        results = [
            {'rule1': ['user1', 'user2']},
            None,
            {'rule2': ['user3']},
            None
        ]

        merged = merge_results(results)

        assert merged == {
            'rule1': ['user1', 'user2'],
            'rule2': ['user3']
        }

    def test_merge_results_with_list_input(self, caplog):
        """測試包含 list 格式輸入的處理（應該被跳過並記錄警告）"""
        with caplog.at_level(logging.WARNING):
            results = [
                {'rule1': ['user1', 'user2']},
                ['user3', 'user4'],  # 錯誤的格式，應該被跳過
                {'rule2': ['user5']}
            ]

            merged = merge_results(results)

            # 檢查結果只包含正確格式的資料
            assert merged == {
                'rule1': ['user1', 'user2'],
                'rule2': ['user5']
            }

            # 檢查是否記錄了警告訊息
            assert "收到 list 格式的結果，可能是錯誤的輸入格式" in caplog.text

    def test_merge_results_with_invalid_input_types(self, caplog):
        """測試包含無效輸入類型的處理"""
        with caplog.at_level(logging.WARNING):
            results = [
                {'rule1': ['user1', 'user2']},
                "invalid_string",  # 無效格式
                42,  # 無效格式
                {'rule2': ['user3']}
            ]

            merged = merge_results(results)

            # 檢查結果只包含正確格式的資料
            assert merged == {
                'rule1': ['user1', 'user2'],
                'rule2': ['user3']
            }

            # 檢查是否記錄了警告訊息
            assert "未知的結果格式" in caplog.text

    def test_merge_results_with_non_list_users(self):
        """測試處理 users 不是 list 的情況"""
        results = [
            {'rule1': ['user1', 'user2']},
            {'rule2': 'user3'},  # users 是字串而不是 list
            {'rule3': ['user4']}
        ]

        merged = merge_results(results)

        assert merged == {
            'rule1': ['user1', 'user2'],
            'rule2': ['user3'],  # 應該被轉換為 list
            'rule3': ['user4']
        }

    def test_merge_results_empty_input(self):
        """測試空輸入的處理"""
        results = []
        merged = merge_results(results)
        assert merged == {}

    def test_merge_results_all_none_input(self):
        """測試全部都是 None 的輸入"""
        results = [None, None, None]
        merged = merge_results(results)
        assert merged == {}

    def test_merge_results_mixed_valid_invalid_formats(self, caplog):
        """測試混合有效和無效格式的複雜情況"""
        with caplog.at_level(logging.WARNING):
            results = [
                {'rule1': ['user1']},           # 有效格式
                None,                           # None 值
                ['user2', 'user3'],            # list 格式（無效）
                {'rule2': 'user4'},            # users 不是 list 但可處理
                "string_format",                # 字串格式（無效）
                {'rule1': ['user5'], 'rule3': ['user6']},  # 有效格式
                42,                             # 數字格式（無效）
            ]

            merged = merge_results(results)

            # 檢查結果
            assert merged == {
                'rule1': ['user1', 'user5'],
                'rule2': ['user4'],
                'rule3': ['user6']
            }

            # 檢查警告訊息
            log_text = caplog.text
            assert "收到 list 格式的結果，可能是錯誤的輸入格式" in log_text
            assert "未知的結果格式" in log_text

    def test_merge_results_original_functionality_preserved(self):
        """測試原有功能仍然正常運作"""
        # 模擬原始的正確使用方式
        results = [
            {'segment_001': ['user1', 'user2', 'user3']},
            {'segment_001': ['user4'], 'segment_002': ['user5']},
            {'segment_002': ['user6', 'user7'], 'segment_003': ['user8']}
        ]

        merged = merge_results(results)

        expected = {
            'segment_001': ['user1', 'user2', 'user3', 'user4'],
            'segment_002': ['user5', 'user6', 'user7'],
            'segment_003': ['user8']
        }

        assert merged == expected

    def test_merge_results_performance_with_large_input(self):
        """測試大量輸入的效能"""
        # 創建大量的模擬資料
        results = []
        for i in range(100):
            rule_dict = {}
            for j in range(10):
                rule_id = f'rule_{j}'
                users = [f'user_{i}_{j}_{k}' for k in range(5)]
                rule_dict[rule_id] = users
            results.append(rule_dict)

        # 執行合併
        merged = merge_results(results)

        # 驗證結果
        assert len(merged) == 10  # 應該有 10 個不同的規則
        for rule_id in merged:
            assert len(merged[rule_id]) == 500  # 每個規則應該有 100*5 = 500 個用戶