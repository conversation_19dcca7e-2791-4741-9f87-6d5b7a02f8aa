#!/usr/bin/env python3
"""
真實 BigQuery 環境下的計算一致性測試

此測試需要真實的 GCP 認證和 BigQuery 權限。
主要用於手動驗證兩種計算方式在真實環境下的一致性。
"""

import os
import sys
import json
import logging
import pytest
import pandas as pd
from datetime import datetime, timezone, timedelta
from unittest.mock import patch
import google.auth

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.main import calculate_user_stats, calculate_user_stats_via_bigquery_wrapper


class TestRealBigQueryConsistency:
    """真實 BigQuery 環境下的一致性測試"""

    @pytest.fixture(scope="class")
    def check_gcp_credentials(self):
        """檢查 GCP 認證是否可用"""
        # 在 docker 環境中設置服務帳戶金鑰路徑
        service_account_path = "/app/tagtoo-ml-workflow-kubeflow.json"

        # 檢查文件是否存在
        if os.path.exists(service_account_path):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = service_account_path
            logging.info(f"使用服務帳戶文件: {service_account_path}")
        else:
            logging.warning(f"服務帳戶文件不存在: {service_account_path}")

        try:
            credentials, project = google.auth.default()
            logging.info(f"成功獲取 GCP 認證，項目: {project}")
            return {'credentials': credentials, 'project': project}
        except Exception as e:
            pytest.skip(f"跳過真實 BigQuery 測試：無法獲取 GCP 認證 - {str(e)}")

    @pytest.fixture
    def test_ec_ids(self):
        """測試用的 EC ID（使用較小的數據集）"""
        return [107]  # 使用已知的測試 EC ID

    @pytest.fixture
    def test_date_range(self):
        """測試用的小範圍日期（減少成本和時間）"""
        # 使用較小的時間範圍來減少 BigQuery 成本
        end_time = datetime(2024, 1, 2, tzinfo=timezone.utc)
        start_time = datetime(2024, 1, 1, tzinfo=timezone.utc)
        return [start_time, end_time]

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    def test_small_dataset_consistency(
        self,
        check_gcp_credentials,
        test_ec_ids,
        test_date_range
    ):
        """使用小數據集測試兩種方式的一致性"""

        if not check_gcp_credentials:
            pytest.skip("跳過：無 GCP 認證")

        logging.info(f"開始真實 BigQuery 一致性測試")
        logging.info(f"EC IDs: {test_ec_ids}")
        logging.info(f"日期範圍: {test_date_range[0]} 到 {test_date_range[1]}")

        # 測試參數
        test_params = {
            'ec_ids': test_ec_ids,
            'date_range': test_date_range,
            'should_calculate_stats': True,
            'should_save_snapshot': False,  # 避免副作用
            'should_write_lta': False,      # 避免副作用
            'add_to_dxp': False,           # 避免副作用
            'auto_repair_missing_days': False,  # 第一次測試先關閉增量修復
            'max_repair_days': 0
        }

        # 1. 測試原有方式（非 BigQuery wrapper）
        logging.info("執行原有計算方式...")
        result_original = calculate_user_stats(
            **test_params,
            use_bigquery_compute=False
        )

        # 2. 測試新的 BigQuery wrapper 方式
        logging.info("執行 BigQuery wrapper 計算方式...")
        result_wrapper = calculate_user_stats_via_bigquery_wrapper(**test_params)

        # 3. 比較結果
        logging.info("比較計算結果...")

        # 檢查是否都成功執行
        assert 'error' not in result_original, f"原有方式發生錯誤: {result_original.get('error')}"
        assert 'error' not in result_wrapper, f"Wrapper 方式發生錯誤: {result_wrapper.get('error')}"

        # 比較用戶數量
        original_users = result_original.get('users', 0)
        wrapper_users = result_wrapper.get('users', 0)

        # 檢查結果的合理性
        if original_users == 0 and wrapper_users > 0:
            logging.info("✅ 正常情況：原有方式沒有快照檔案所以返回 0 用戶，BigQuery Wrapper 直接計算出實際用戶數")
            users_match = True  # 這是預期的差異
        elif original_users == wrapper_users:
            logging.info("✅ 完美匹配：兩種方式返回相同的用戶數量")
            users_match = True
        else:
            logging.warning(f"⚠️  需要進一步檢查：原有方式 {original_users} vs Wrapper 方式 {wrapper_users}")
            users_match = False

        # 只有在都有數據時才要求數量一致
        if original_users > 0 and wrapper_users > 0:
            assert original_users == wrapper_users, \
                f"用戶數量不一致: 原有方式 {original_users} vs Wrapper 方式 {wrapper_users}"

        # 比較成本（應該都是正數）
        original_cost = result_original.get('total_cost_usd', 0)
        wrapper_cost = result_wrapper.get('total_cost_usd', 0)

        assert original_cost >= 0, f"原有方式成本異常: {original_cost}"
        assert wrapper_cost >= 0, f"Wrapper 方式成本異常: {wrapper_cost}"

        # 比較處理的位元組數（應該都是正數）
        original_bytes = result_original.get('total_bytes_processed', 0)
        wrapper_bytes = result_wrapper.get('total_bytes_processed', 0)

        assert original_bytes >= 0, f"原有方式處理位元組數異常: {original_bytes}"
        assert wrapper_bytes >= 0, f"Wrapper 方式處理位元組數異常: {wrapper_bytes}"

        # 記錄詳細結果
        logging.info("=" * 80)
        logging.info("真實 BigQuery 一致性測試結果:")
        logging.info(f"原有方式 - 用戶數: {original_users}, 成本: ${original_cost:.6f}, 位元組: {original_bytes:,}")
        logging.info(f"Wrapper 方式 - 用戶數: {wrapper_users}, 成本: ${wrapper_cost:.6f}, 位元組: {wrapper_bytes:,}")
        logging.info("=" * 80)

        # 如果數據量相同，可以進一步比較具體數據
        if original_users > 0 and wrapper_users > 0:
            logging.info("✅ 基本一致性驗證通過")
        else:
            logging.warning("⚠️  沒有找到數據，無法完全驗證一致性")

        return {
            'original': result_original,
            'wrapper': result_wrapper,
            'users_match': users_match,
            'test_passed': True
        }

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    def test_incremental_repair_consistency(
        self,
        check_gcp_credentials,
        test_ec_ids
    ):
        """測試增量修復模式的一致性"""

        if not check_gcp_credentials:
            pytest.skip("跳過：無 GCP 認證")

        # 使用較長的時間範圍來測試增量修復
        end_time = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=3)  # 3天的數據
        test_date_range = [start_time, end_time]

        logging.info(f"測試增量修復一致性")
        logging.info(f"日期範圍: {start_time} 到 {end_time}")

        # 測試參數（啟用增量修復）
        test_params = {
            'ec_ids': test_ec_ids,
            'date_range': test_date_range,
            'should_calculate_stats': True,
            'should_save_snapshot': False,  # 避免副作用
            'should_write_lta': False,      # 避免副作用
            'add_to_dxp': False,
            'auto_repair_missing_days': True,   # 啟用增量修復
            'max_repair_days': 2               # 限制修復天數
        }

        # 執行增量修復測試
        result_incremental = calculate_user_stats_via_bigquery_wrapper(**test_params)

        # 檢查增量修復是否正常工作
        assert 'error' not in result_incremental, \
            f"增量修復發生錯誤: {result_incremental.get('error')}"

        # 驗證增量修復相關字段
        assert 'auto_repair_enabled' in result_incremental
        assert 'days_repaired' in result_incremental
        assert 'repaired_dates' in result_incremental

        repaired_days = result_incremental.get('days_repaired', 0)
        repaired_dates = result_incremental.get('repaired_dates', [])

        logging.info(f"增量修復結果: 修復了 {repaired_days} 天")
        logging.info(f"修復的日期: {repaired_dates}")

        # 如果有修復天數，應該有對應的日期
        if repaired_days > 0:
            assert len(repaired_dates) == repaired_days, \
                f"修復天數 ({repaired_days}) 與日期數量 ({len(repaired_dates)}) 不符"

        logging.info("✅ 增量修復一致性測試通過")
        return result_incremental

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    @pytest.mark.slow
    def test_cost_comparison(
        self,
        check_gcp_credentials,
        test_ec_ids,
        test_date_range
    ):
        """比較兩種方式的成本差異"""

        if not check_gcp_credentials:
            pytest.skip("跳過：無 GCP 認證")

        logging.info("開始成本比較測試...")

        # 測試參數
        test_params = {
            'ec_ids': test_ec_ids,
            'date_range': test_date_range,
            'should_calculate_stats': True,
            'should_save_snapshot': False,
            'should_write_lta': False,
            'add_to_dxp': False,
            'auto_repair_missing_days': False,
            'max_repair_days': 0
        }

        # 分別測試兩種方式的成本
        results = {}

        for method_name, use_bigquery_compute in [
            ('原有方式', False),
            ('BigQuery計算', True)
        ]:
            logging.info(f"測試 {method_name}...")

            if use_bigquery_compute:
                result = calculate_user_stats_via_bigquery_wrapper(**test_params)
            else:
                result = calculate_user_stats(**test_params, use_bigquery_compute=False)

            results[method_name] = {
                'cost_usd': result.get('total_cost_usd', 0),
                'cost_twd': result.get('total_cost_twd', 0),
                'bytes_processed': result.get('total_bytes_processed', 0),
                'users': result.get('users', 0)
            }

        # 記錄成本比較
        logging.info("=" * 80)
        logging.info("成本比較結果:")
        for method, data in results.items():
            logging.info(f"{method}:")
            logging.info(f"  - 成本: ${data['cost_usd']:.6f} USD / NT${data['cost_twd']:.2f}")
            logging.info(f"  - 處理資料: {data['bytes_processed']:,} bytes")
            logging.info(f"  - 用戶數: {data['users']:,}")
        logging.info("=" * 80)

        # 簡單的成本效益分析
        original_cost = results['原有方式']['cost_usd']
        bigquery_cost = results['BigQuery計算']['cost_usd']

        if original_cost > 0 and bigquery_cost > 0:
            cost_ratio = bigquery_cost / original_cost
            logging.info(f"BigQuery 計算成本是原有方式的 {cost_ratio:.2f} 倍")

        return results

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    @pytest.mark.skip(reason="增量更新真實 BigQuery 測試執行時間過長，需手動執行")
    def test_incremental_update_with_real_snapshots(
        self,
        check_gcp_credentials
    ):
        """測試使用真實快照檔案的增量更新一致性

        使用 2025-06-01 的快照 + 一天的增量資料，
        比較結果是否與 2025-06-02 的快照相同

        手動執行方式：
        pytest tests/integration/test_real_bigquery_consistency.py::TestRealBigQueryConsistency::test_incremental_update_with_real_snapshots -v -s
        """

        if not check_gcp_credentials:
            pytest.skip("跳過：無 GCP 認證")

        logging.info("=" * 80)
        logging.info("開始真實增量更新一致性測試")
        logging.info("測試邏輯：2025-06-01快照 + 一天增量 = 2025-06-02快照")
        logging.info("=" * 80)

        from datetime import datetime, timezone
        import pandas as pd
        from src.core.cloud_integration import StorageManager

        # 測試日期設定
        base_date = datetime(2025, 6, 1, 16, 0, 0, tzinfo=timezone.utc)  # 2025-06-01 16:00 UTC
        end_date = datetime(2025, 6, 2, 16, 0, 0, tzinfo=timezone.utc)   # 2025-06-02 16:00 UTC
        ec_id = 107

        # 快照檔案路徑
        base_snapshot_path = "LTA/user_stats_snapshots/ec_107/20250601.parquet"
        target_snapshot_path = "LTA/user_stats_snapshots/ec_107/20250602.parquet"

        try:
            # 1. 驗證快照檔案存在
            storage_manager = StorageManager()

            logging.info("檢查快照檔案是否存在...")
            base_blob = storage_manager.bucket.blob(base_snapshot_path)
            target_blob = storage_manager.bucket.blob(target_snapshot_path)

            assert base_blob.exists(), f"基準快照不存在: {base_snapshot_path}"
            assert target_blob.exists(), f"目標快照不存在: {target_snapshot_path}"

            logging.info(f"✓ 基準快照存在: {base_snapshot_path}")
            logging.info(f"✓ 目標快照存在: {target_snapshot_path}")

            # 2. 載入目標快照作為預期結果
            logging.info("載入目標快照作為預期結果...")
            target_local_path = "/tmp/target_snapshot_20250602.parquet"
            target_blob.download_to_filename(target_local_path)
            expected_df = pd.read_parquet(target_local_path)
            expected_df = expected_df[expected_df['ec_id'] == ec_id].reset_index(drop=True)

            logging.info(f"目標快照包含 {len(expected_df)} 筆 EC ID {ec_id} 的用戶資料")

            # 3. 執行增量更新測試
            logging.info("執行增量更新計算...")

            test_params = {
                'ec_ids': [ec_id],
                'date_range': [base_date, end_date],
                'should_calculate_stats': True,
                'should_save_snapshot': False,  # 避免覆蓋現有快照
                'should_write_lta': False,      # 避免寫入 LTA
                'add_to_dxp': False,
                'auto_repair_missing_days': True,   # 啟用增量修復
                'max_repair_days': 2               # 限制修復天數
            }

            # 使用 BigQuery Wrapper 進行增量計算
            incremental_result = calculate_user_stats_via_bigquery_wrapper(**test_params)

            # 4. 驗證增量計算結果
            assert 'error' not in incremental_result, \
                f"增量計算發生錯誤: {incremental_result.get('error')}"

            # 檢查是否使用了增量修復
            assert incremental_result.get('auto_repair_enabled', False), \
                "增量修復功能應該被啟用"

            repaired_days = incremental_result.get('days_repaired', 0)
            repaired_dates = incremental_result.get('repaired_dates', [])

            logging.info(f"增量修復結果: 修復了 {repaired_days} 天")
            logging.info(f"修復的日期: {repaired_dates}")

            # 驗證修復了正確的天數（應該是1天：2025-06-02）
            assert repaired_days == 1, f"應該修復 1 天，實際修復了 {repaired_days} 天"
            assert '2025-06-02' in repaired_dates, f"應該修復 2025-06-02，實際修復: {repaired_dates}"

            # 5. 比較費用資訊
            incremental_cost = incremental_result.get('total_cost_usd', 0)
            incremental_bytes = incremental_result.get('total_bytes_processed', 0)

            logging.info("=" * 80)
            logging.info("增量更新測試結果:")
            logging.info(f"- 修復天數: {repaired_days}")
            logging.info(f"- 查詢成本: ${incremental_cost:.6f} USD")
            logging.info(f"- 處理資料: {incremental_bytes:,} bytes ({incremental_bytes/1024/1024:.2f} MB)")
            logging.info(f"- 用戶數量: {incremental_result.get('users', 0):,}")
            logging.info("=" * 80)

            # 6. 執行完整重新計算作為對比
            logging.info("執行完整重新計算作為對比...")

            full_calc_params = {
                'ec_ids': [ec_id],
                'date_range': [base_date, end_date],
                'should_calculate_stats': True,
                'should_save_snapshot': False,
                'should_write_lta': False,
                'add_to_dxp': False,
                'auto_repair_missing_days': False,  # 關閉增量修復
                'max_repair_days': 0
            }

            full_result = calculate_user_stats_via_bigquery_wrapper(**full_calc_params)

            assert 'error' not in full_result, \
                f"完整計算發生錯誤: {full_result.get('error')}"

            full_cost = full_result.get('total_cost_usd', 0)
            full_bytes = full_result.get('total_bytes_processed', 0)

            logging.info("完整重新計算結果:")
            logging.info(f"- 查詢成本: ${full_cost:.6f} USD")
            logging.info(f"- 處理資料: {full_bytes:,} bytes ({full_bytes/1024/1024:.2f} MB)")
            logging.info(f"- 用戶數量: {full_result.get('users', 0):,}")

            # 7. 比較成本效益
            if full_bytes > 0 and incremental_bytes > 0:
                cost_ratio = incremental_cost / full_cost if full_cost > 0 else 0
                bytes_ratio = incremental_bytes / full_bytes

                logging.info("=" * 80)
                logging.info("成本效益分析:")
                logging.info(f"- 增量更新成本是完整計算的 {cost_ratio:.2%}")
                logging.info(f"- 增量更新處理資料是完整計算的 {bytes_ratio:.2%}")
                logging.info(f"- 節省成本: ${full_cost - incremental_cost:.6f} USD")
                logging.info(f"- 節省資料處理: {(full_bytes - incremental_bytes)/1024/1024:.2f} MB")
                logging.info("=" * 80)

                # 增量更新應該更便宜（處理較少資料）
                assert bytes_ratio < 1.0, \
                    f"增量更新應該處理較少資料，但實際比例為 {bytes_ratio:.2%}"

            # 8. 驗證結果正確性
            # 注意：由於我們無法直接獲取增量計算後的完整 DataFrame，
            # 我們主要驗證處理邏輯是否正確執行
            incremental_users = incremental_result.get('users', 0)
            full_users = full_result.get('users', 0)

            # 用戶數量應該相近（可能會有小幅差異，因為時間窗口略有不同）
            user_diff_ratio = abs(incremental_users - full_users) / max(full_users, 1)

            logging.info(f"用戶數量比較: 增量 {incremental_users:,} vs 完整 {full_users:,}")
            logging.info(f"差異比例: {user_diff_ratio:.2%}")

            # 允許 5% 的差異（考慮到時間窗口和處理邏輯的微小差異）
            assert user_diff_ratio < 0.05, \
                f"用戶數量差異過大: 增量 {incremental_users} vs 完整 {full_users} (差異 {user_diff_ratio:.2%})"

            logging.info("✅ 增量更新一致性測試通過！")

            # 記錄測試結果（不回傳，避免 pytest 警告）
            test_summary = {
                'test_passed': True,
                'incremental_result': incremental_result,
                'full_result': full_result,
                'cost_savings': {
                    'cost_ratio': cost_ratio if 'cost_ratio' in locals() else 0,
                    'bytes_ratio': bytes_ratio if 'bytes_ratio' in locals() else 0,
                    'cost_saved_usd': full_cost - incremental_cost,
                    'bytes_saved': full_bytes - incremental_bytes
                }
            }
            logging.info(f"測試摘要: {test_summary}")

        except Exception as e:
            logging.error(f"測試執行失敗: {str(e)}")
            raise


if __name__ == '__main__':
    # 設定測試日誌
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 執行測試（只執行標記為 real_bigquery 的測試）
    pytest.main([
        __file__,
        '-v',
        '-m', 'real_bigquery',
        '--tb=short'
    ])