#!/usr/bin/env python3
"""
驗證 NaT 型別資料能正確經由 update_user_stats_table 寫入 BigQuery 而不觸發
"Object of type NaTType is not JSON serializable" 錯誤。
此測試使用專案的測試用資料集/表格：
    tagtoo-tracking.event_test.user_stats
並寫入前綴 LTA_TEST 的快照至 bucket gs://tagtoo-ml-workflow/
因為需要真實 GCP 連線，若認證不存在將自動 skip。
"""

import os
import logging
import pandas as pd
from datetime import datetime, timezone
import pytest

# 將 src 目錄加入 Python 路徑
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.core.cloud_integration import BigQueryClient

logger = logging.getLogger(__name__)


@pytest.fixture(scope="module")
def ec_id_under_test():
    """本次測試使用的 EC ID。"""
    return 2808


@pytest.mark.integration
def test_nat_serialization_to_bigquery(
    ec_id_under_test,
    check_gcp_credentials,
    cleanup_persistent_test_table,
):
    """將包含 pd.NaT 的欄位寫入 BigQuery，確認不會拋出序列化錯誤。"""

    if not check_gcp_credentials:
        pytest.skip("缺少 GCP 認證，跳過 BigQuery 整合測試")

    # 1. 準備包含 NaT 的 DataFrame
    problematic_df = pd.DataFrame({
        'ec_id': [ec_id_under_test, ec_id_under_test],
        'permanent': ['user_nat_1', 'user_nat_2'],
        'purchase_count': [1, 2],
        'total_sessions': [5, 3],
        'total_purchase_amount': [100.0, 200.0],
        'first_interaction_time': [pd.Timestamp("2023-01-01", tz="UTC"), pd.NaT],
        'last_interaction_time': [pd.NaT, pd.Timestamp("2023-01-02", tz="UTC")],
        'registration_time': [pd.NaT, pd.NaT],
        'first_purchase_time': [pd.NaT, pd.NaT],
        'last_purchase_time': [pd.NaT, pd.NaT],
    })

    # 2. 寫入 BigQuery 測試表
    bq_client = BigQueryClient()
    target_table = cleanup_persistent_test_table  # fixture 會清空後再提供

    current_date = datetime.now(timezone.utc)

    # 呼叫真正的方法，若有 NaT 序列化問題會丟出例外
    try:
        bq_client.update_user_stats_table(
            user_stats_df=problematic_df,
            ec_id=ec_id_under_test,
            current_date=current_date,
            target_table_id=target_table,
        )
    except TypeError as e:
        pytest.fail(f"update_user_stats_table 仍然拋出序列化錯誤: {e}")

    # 3. 在真實環境下查詢驗證資料是否成功寫入（CI mock 環境則跳過）
    from unittest.mock import MagicMock  # 延遲引入以避免不必要依賴

    if isinstance(bq_client.client, MagicMock):
        logger.info("🛈 CI mock 環境，已確認流程無例外，即視為通過，不執行查詢驗證")
        return

    query = (
        f"SELECT COUNT(*) AS cnt FROM `{target_table}` "
        f"WHERE ec_id = {ec_id_under_test} AND permanent IN ('user_nat_1', 'user_nat_2')"
    )
    query_job = bq_client.client.query(query)
    rows = list(query_job.result())
    assert rows, "查詢結果為空，可能寫入失敗。"
    result_cnt = rows[0][0]
    assert result_cnt == 2, f"應成功寫入 2 筆記錄，但僅找到 {result_cnt} 筆。"

    logger.info("✅ NaT 型別序列化測試通過，成功寫入 BigQuery")