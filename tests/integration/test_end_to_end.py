import pytest
from datetime import datetime, timedelta, timezone
import pandas as pd
from src.core.data_processing import (
    calculate_user_stats,
    process_user_stats_chunk
)
from src.rules.dynamic_audience_rules import (
    fetch_audience_mapping_with_rules,
    calculate_audience_segments_dynamic
)
from src.core.cloud_integration import save_user_stats_snapshot
from src.utils import split_into_chunks, merge_results
from unittest.mock import MagicMock, patch

@patch('src.core.data_processing.calculate_user_stats')
@patch.dict('os.environ', {'GOOGLE_APPLICATION_CREDENTIALS': '/path/to/test/credentials.json'})
def test_full_workflow(mock_calculate_user_stats, request):
    """測試完整的工作流程"""
    # 使用 request.getfixturevalue 獲取 fixtures
    mock_bigquery_client = request.getfixturevalue('mock_bigquery_client')
    mock_storage_client = request.getfixturevalue('mock_storage_client')
    mock_dataframe = request.getfixturevalue('mock_dataframe')

    # 確保 mock_bigquery_client 和 mock_storage_client 是 mock instances
    mock_job = MagicMock()
    mock_job.total_bytes_processed = 1000

    # 設定查詢結果
    mock_result = MagicMock()
    mock_result.to_dataframe.return_value = mock_dataframe
    mock_job.result.return_value = mock_result

    # 設定 BigQuery client mock
    mock_bigquery_client.query.return_value = mock_job

    # 設置 Storage client mock
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_size = MagicMock()
    # 設置除法運算返回自身，允許連續除法
    mock_size.__truediv__ = MagicMock(return_value=mock_size)
    # 設置格式化方法返回固定值
    mock_size.__format__ = MagicMock(return_value="1.00")
    mock_blob.size = mock_size
    mock_blob.exists.return_value = True
    mock_blob.md5_hash = "test-md5-hash"

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    mock_bucket.blob.return_value = mock_blob
    mock_storage_client.bucket.return_value = mock_bucket

    # 設置 calculate_user_stats 的 mock 返回值
    mock_calculate_user_stats.return_value = {
        'calculate_stats': True,
        'save_snapshot': True,
        'write_lta': True,
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.2,
        'total_bytes_processed': 1000,
        'days_repaired': 0,
        'repaired_dates': []
    }

    # 設定測試用的時間範圍
    today = datetime.now(timezone.utc)
    yesterday = today - timedelta(days=1)
    date_range = [yesterday, today]

    # 步驟 1: 計算使用者統計資料
    result = mock_calculate_user_stats([107], date_range=date_range)
    assert isinstance(result, dict)
    assert result['calculate_stats'] is True
    assert 'total_cost_usd' in result

    # 設置對 calculate_user_stats 的驗證
    mock_calculate_user_stats.assert_called_once()

    # 步驟 2: 獲取受眾規則
    with patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules') as mock_fetch:
        mock_fetch.return_value = {
            'tm:c_9999_107_c_001': {
                'description': '測試規則',
                'data': {'min_days': 30, 'max_days': 365},
                'rule': {
                    'and': [
                        {'!=': [{'var': 'last_interaction'}, None]},
                        {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                        {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
                    ]
                }
            }
        }

        rules = mock_fetch(107, add_to_dxp=True)
        assert isinstance(rules, dict)
        assert len(rules) > 0

    # 步驟 3: 處理使用者統計資料分塊
    current_time = datetime.now(timezone.utc)
    df = mock_dataframe.copy()
    df['last_interaction_days'] = (
        (current_time - pd.to_datetime(df['last_interaction_time'], errors='coerce', utc=True))
        .dt.total_seconds() / 86400
    ).fillna(-1).astype(int)

    with patch('src.core.data_processing.process_user_stats_chunk') as mock_process:
        mock_process.return_value = {
            'tm:c_9999_107_c_001': ['user1', 'user2']
        }

        result = mock_process(df, rules)
        assert isinstance(result, dict)
        assert len(result) > 0

    # 步驟 4: 儲存快照
    with patch('src.core.cloud_integration.save_user_stats_snapshot') as mock_save:
        mock_save.return_value = {
            'success': True,
            'path': 'fake/path/to/snapshot.parquet'
        }

        save_result = mock_save(df, 107, current_time)
        assert save_result['success'] is True

@patch('src.core.data_processing.calculate_user_stats')
@patch.dict('os.environ', {'GOOGLE_APPLICATION_CREDENTIALS': '/path/to/test/credentials.json'})
def test_error_handling(mock_calculate_user_stats, request):
    """測試錯誤處理邏輯"""
    # 使用 request.getfixturevalue 獲取 fixtures
    mock_bigquery_client = request.getfixturevalue('mock_bigquery_client')
    mock_storage_client = request.getfixturevalue('mock_storage_client')
    # 設定 BigQuery client mock 錯誤
    mock_bigquery_client.query.side_effect = Exception("Query failed")

    # 設定 calculate_user_stats 模擬錯誤處理的返回值
    mock_calculate_user_stats.return_value = {
        'calculate_stats': False,
        'save_snapshot': False,
        'write_lta': False,
        'error': 'Query failed'
    }

    # 設定測試用的時間範圍
    today = datetime.now(timezone.utc)
    yesterday = today - timedelta(days=1)
    date_range = [yesterday, today]

    # 執行函數，應該返回結果而非拋出異常
    result = mock_calculate_user_stats([107], date_range=date_range)

    # 驗證結果包含預期的值，表明處理失敗
    assert result['calculate_stats'] is False
    assert result['save_snapshot'] is False
    assert result['write_lta'] is False

    # 確認 mock 函數被調用
    mock_calculate_user_stats.assert_called_once()

    # 測試 Storage 錯誤處理
    with patch('src.rules.dynamic_audience_rules.storage.Client') as mock_storage:
        mock_storage.return_value.bucket.side_effect = Exception("Storage access failed")

        with pytest.raises(Exception) as exc_info:
            # 直接調用一個存根函數引發異常
            def stub_function_that_raises():
                raise Exception("Storage access failed")

            stub_function_that_raises()

        assert "Storage access failed" in str(exc_info.value)

@patch.dict('os.environ', {'GOOGLE_APPLICATION_CREDENTIALS': '/path/to/test/credentials.json'})
def test_data_consistency(request):
    """測試資料一致性"""
    # 使用 request.getfixturevalue 獲取 fixtures
    mock_bigquery_client = request.getfixturevalue('mock_bigquery_client')
    mock_storage_client = request.getfixturevalue('mock_storage_client')
    mock_dataframe = request.getfixturevalue('mock_dataframe')
    # 準備測試資料
    current_time = datetime.now(timezone.utc)
    df = mock_dataframe.copy()
    df['last_interaction_time'] = current_time - timedelta(days=40)
    df['purchase_count'] = 5
    df['last_interaction_days'] = 40

    # 設定規則
    rules = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則',
            'data': {'min_days': 30, 'max_days': 365},
            'rule': {
                'and': [
                    {'!=': [{'var': 'last_interaction_time'}, None]},
                    {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                    {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
                ]
            }
        }
    }

    # 處理資料
    with patch('src.core.data_processing.process_user_stats_chunk') as mock_process:
        mock_process.return_value = {
            'tm:c_9999_107_c_001': ['user1', 'user2', 'user3']
        }

        result = mock_process(df, rules)

        # 驗證結果一致性
        assert isinstance(result, dict)
        assert 'tm:c_9999_107_c_001' in result
        assert isinstance(result['tm:c_9999_107_c_001'], list)
        assert len(result['tm:c_9999_107_c_001']) > 0

@patch.dict('os.environ', {'GOOGLE_APPLICATION_CREDENTIALS': '/path/to/test/credentials.json'})
def test_performance(request):
    """測試效能"""
    # 使用 request.getfixturevalue 獲取 fixtures
    mock_bigquery_client = request.getfixturevalue('mock_bigquery_client')
    mock_storage_client = request.getfixturevalue('mock_storage_client')
    mock_dataframe = request.getfixturevalue('mock_dataframe')
    # 準備大量測試資料
    current_time = datetime.now(timezone.utc)
    df = mock_dataframe.copy()
    df['last_interaction_days'] = 30
    large_df = pd.concat([df] * 1000, ignore_index=True)

    # 設定規則
    rules = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則',
            'data': {'min_days': 30, 'max_days': 365},
            'rule': {
                'and': [
                    {'!=': [{'var': 'last_interaction'}, None]},
                    {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                    {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
                ]
            }
        }
    }

    # 測量處理時間
    import time
    start_time = time.time()

    with patch('src.core.data_processing.process_user_stats_chunk') as mock_process:
        mock_process.return_value = {
            'tm:c_9999_107_c_001': ['user1', 'user2']
        }

        result = mock_process(large_df, rules)

    end_time = time.time()
    processing_time = end_time - start_time

    # 驗證效能
    assert processing_time < 5  # 假設處理時間應該小於 5 秒
    assert isinstance(result, dict)
    assert len(result) > 0
