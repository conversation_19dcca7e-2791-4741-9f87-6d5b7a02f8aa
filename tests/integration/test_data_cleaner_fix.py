import pandas as pd
import json
import pytest
from src.utils.data_cleaner import clean_parquet_dataframe_dynamic, load_bigquery_schema

@pytest.fixture(scope="module")
def bq_schema():
    """載入一次 schema 供整個模組使用"""
    return load_bigquery_schema()

@pytest.mark.integration
def test_cleaner_handles_bad_data(bq_schema):
    """
    測試 clean_parquet_dataframe_dynamic 是否能正確處理包含
    空字符串的數值欄位和包含 NaT 的時間戳欄位。
    """
    # 1. 準備有問題的 DataFrame
    problematic_data = {
        'ec_id': [107, 107],
        'permanent': ['user1', 'user2'],
        'purchase_count': [1, ''],  # '' 在整數欄位中
        'total_purchase_amount': [100.0, ''], # '' 在浮點數欄位中
        'last_interaction_time': [pd.Timestamp("2023-01-01"), pd.NaT]  # NaT 在時間欄位中
    }
    problematic_df = pd.DataFrame(problematic_data)

    # 2. 執行清理函式
    cleaned_df = clean_parquet_dataframe_dynamic(problematic_df, ec_id=107)

    # 3. 驗證清理結果
    # 驗證 purchase_count
    assert cleaned_df['purchase_count'].dtype == 'int64'
    assert cleaned_df.loc[1, 'purchase_count'] == 0

    # 驗證 total_purchase_amount
    assert cleaned_df['total_purchase_amount'].dtype == 'float64'
    assert cleaned_df.loc[1, 'total_purchase_amount'] == 0.0

    # 驗證 last_interaction_time 的內容，而不是類型
    # 因為我們將其轉換為 object 類型以容納 None
    assert isinstance(cleaned_df.loc[0, 'last_interaction_time'], pd.Timestamp)
    assert cleaned_df.loc[1, 'last_interaction_time'] is None

    # 4. 驗證 JSON 序列化
    try:
        dict_records = cleaned_df.to_dict('records')

        # 自定義 JSON 轉換器來處理 pandas 的時間類型
        def json_default(o):
            if isinstance(o, pd.Timestamp):
                return o.isoformat()
            # pd.NaT is a singleton, so we can check with 'is'
            if o is pd.NaT:
                return None
            raise TypeError(f"Object of type {type(o).__name__} is not JSON serializable")

        json_string = json.dumps(dict_records, default=json_default)

        # We check the second record, which corresponds to index 1
        deserialized_records = json.loads(json_string)
        assert deserialized_records[1]['last_interaction_time'] is None

    except TypeError as e:
        pytest.fail(f"DataFrame 無法被序列化為 JSON: {e}")