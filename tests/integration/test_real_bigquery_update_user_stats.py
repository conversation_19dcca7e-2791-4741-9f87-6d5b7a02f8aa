#!/usr/bin/env python3
"""
真實 BigQuery 環境下的 update_user_stats_table 測試

此測試需要真實的 GCP 認證和 BigQuery 權限。
專門測試 update_user_stats_table 函數真正寫入 BigQuery user_stats 表格的功能。
"""

import os
import sys
import logging
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from unittest.mock import patch
import google.auth
from google.cloud import bigquery
from google.api_core.exceptions import BadRequest
import json

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.core.cloud_integration import BigQueryClient, safe_int_convert_enhanced, safe_float_convert_enhanced, safe_ec_id_convert
from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery
import time

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestRealBigQueryUpdateUserStats:
    """真實 BigQuery 環境下的 update_user_stats_table 測試"""

    @pytest.fixture
    def test_ec_id(self):
        """測試用的 EC ID"""
        return 107

    @pytest.fixture
    def bigquery_client(self, check_gcp_credentials):
        """為測試提供 BigQuery 客戶端實例"""
        if not check_gcp_credentials:
            pytest.skip("跳過：無 GCP 認證")
        return BigQueryClient()

    @pytest.fixture
    def problematic_test_data(self, test_ec_id):
        """模擬 EC 107 問題的測試數據"""
        return pd.DataFrame({
            'ec_id': [test_ec_id, test_ec_id, test_ec_id, test_ec_id, test_ec_id],
            'permanent': ['user_a', 'user_b', 'user_c', 'user_d', 'user_e'],
            'purchase_count': ['', '5', '10', None, '20'],  # 包含空字符串和 None
            'total_sessions': ['3', '', '7', '15', None],   # 包含空字符串和 None
            'total_purchase_amount': ['', '200.5', '300.0', None, '500.0'],  # 浮點數欄位也有空字符串和 None
            'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']),
            'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']),
            'registration_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']),
            'first_purchase_time': pd.to_datetime(['2023-01-01', None, '2023-01-03', '2023-01-04', '2023-01-05']),
            'last_purchase_time': pd.to_datetime(['2023-01-01', None, '2023-01-03', '2023-01-04', '2023-01-05'])
        })

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    def test_update_user_stats_table_with_empty_strings_real_bigquery(
        self,
        bigquery_client,
        problematic_test_data,
        test_ec_id,
        cleanup_persistent_test_table
    ):
        """測試 update_user_stats_table 在真實 BigQuery 中處理空字符串的行為"""
        logger.info("開始執行 test_update_user_stats_table_with_empty_strings_real_bigquery")
        current_date = datetime.now(timezone.utc)
        test_table_id = cleanup_persistent_test_table

        # 調用 BigQueryClient 的實際 update_user_stats_table 方法，並指定測試表
        bigquery_client.update_user_stats_table(
            user_stats_df=problematic_test_data,
            ec_id=test_ec_id,
            current_date=current_date,
            target_table_id=test_table_id
        )

        # 驗證數據是否正確寫入 BigQuery
        query = f"""
        SELECT
            ec_id,
            permanent,
            purchase_count,
            total_sessions,
            total_purchase_amount
        FROM `{test_table_id}`
        WHERE ec_id = {test_ec_id}
        ORDER BY permanent
        """
        logger.info(f"查詢測試表 {test_table_id} 以驗證寫入數據")
        query_job = bigquery_client.client.query(query)
        result_df = query_job.result().to_dataframe(
            create_bqstorage_client=True,  # 啟用 Storage API
            dtypes={'ec_id': 'int32'}      # 最佳化記憶體使用
        )

        logger.info(f"""查詢結果 DataFrame:
{result_df}""")

        # 驗證數據
        assert not result_df.empty
        assert len(result_df) == 5

        # 預期值：空字符串應轉換為 0 或 0.0
        expected_purchase_count = [0, 5, 10, 0, 20]
        expected_total_sessions = [3, 0, 7, 15, 0]
        expected_total_purchase_amount = [0.0, 200.5, 300.0, 0.0, 500.0]

        # 對結果進行排序以確保比較一致性
        result_df_sorted = result_df.sort_values(by='permanent').reset_index(drop=True)

        # 驗證 purchase_count
        assert result_df_sorted['purchase_count'].astype(int).tolist() == expected_purchase_count
        assert str(result_df_sorted['purchase_count'].dtype) == 'Int64'

        # 驗證 total_sessions
        assert result_df_sorted['total_sessions'].astype(int).tolist() == expected_total_sessions
        assert str(result_df_sorted['total_sessions'].dtype) == 'Int64'

        # 驗證 total_purchase_amount
        assert result_df_sorted['total_purchase_amount'].astype(float).tolist() == expected_total_purchase_amount
        assert result_df_sorted['total_purchase_amount'].dtype == 'float64'

        logger.info("✅ test_update_user_stats_table_with_empty_strings_real_bigquery 測試通過")

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    def test_update_user_stats_table_performance_real_bigquery(
        self,
        bigquery_client,
        test_ec_id,
        cleanup_persistent_test_table
    ):
        """測試 update_user_stats_table 在真實 BigQuery 中的性能"""
        logger.info("開始執行 test_update_user_stats_table_performance_real_bigquery")
        num_records = 10000  # 測試大量數據
        test_table_id = cleanup_persistent_test_table

        large_data = pd.DataFrame({
            'ec_id': [test_ec_id] * num_records,
            'permanent': [f'perf_user_{i}' for i in range(num_records)],
            'purchase_count': [str(i) for i in range(num_records)],
            'total_sessions': [str(i * 2) for i in range(num_records)],
            'total_purchase_amount': [str(i * 10.5) for i in range(num_records)],
            'first_interaction_time': pd.to_datetime([f'2023-01-01'] * num_records),
            'last_interaction_time': pd.to_datetime([f'2023-01-01'] * num_records),
            'registration_time': pd.to_datetime([f'2023-01-01'] * num_records),
            'first_purchase_time': pd.to_datetime([f'2023-01-01'] * num_records),
            'last_purchase_time': pd.to_datetime([f'2023-01-01'] * num_records)
        })

        current_date = datetime.now(timezone.utc)

        start_time = time.time()
        bigquery_client.update_user_stats_table(
            user_stats_df=large_data,
            ec_id=test_ec_id,
            current_date=current_date,
            target_table_id=test_table_id
        )
        end_time = time.time()
        elapsed_time = end_time - start_time

        logger.info(f"性能測試完成：處理 {num_records} 筆記錄，耗時 {elapsed_time:.2f} 秒")

        # 驗證數據是否正確寫入 BigQuery
        query = f"SELECT COUNT(*) FROM `{test_table_id}` WHERE ec_id = {test_ec_id}"
        count_job = bigquery_client.client.query(query)
        count_result = list(count_job.result())[0][0]
        assert count_result >= num_records, f"預期寫入 {num_records} 筆記錄，實際寫入 {count_result} 筆"

        # 簡單的性能斷言 (例如，不應超過 X 秒)
        # 這裡只是示例，實際應根據基準性能調整
        # assert elapsed_time < 30, f"處理 {num_records} 筆記錄時間過長：{elapsed_time:.2f} 秒"

        logger.info("✅ test_update_user_stats_table_performance_real_bigquery 測試通過")

    @pytest.mark.integration
    @pytest.mark.real_bigquery
    def test_schema_mismatch_handling(self, bigquery_client, test_ec_id):
        """
        測試當 DataFrame 的 schema 與目標 BQ table schema 不符時，
        直接的 load 操作是否會如預期般失敗。
        """
        logger.info("開始執行 test_schema_mismatch_handling")

        # 1. 建立一個使用「舊 schema」的臨時測試表
        schema_path = os.path.join(os.path.dirname(__file__), '..', '..', 'sql', 'user_stats_schema.json')
        with open(schema_path, 'r') as f:
            full_schema_dict = json.load(f)

        old_schema_dict = [field for field in full_schema_dict if field['name'] != 'active_days']
        old_schema = [bigquery.SchemaField.from_api_repr(field) for field in old_schema_dict]

        temp_table_id = f"tagtoo-tracking.event_test.schema_test_{int(time.time())}"

        try:
            table = bigquery.Table(temp_table_id, schema=old_schema)
            bigquery_client.client.create_table(table)
            logger.info(f"成功建立使用舊 schema 的臨時表: {temp_table_id}")

            # 2. 產生包含「新欄位」的測試資料
            data_with_new_field = pd.DataFrame({
                'ec_id': [test_ec_id],
                'permanent': ['schema_mismatch_user'],
                'purchase_count': [5],
                'registration_time': [datetime.now(timezone.utc)],
                'first_interaction_time': [datetime.now(timezone.utc)],
                'last_interaction_time': [datetime.now(timezone.utc)],
                'first_purchase_time': [datetime.now(timezone.utc)],
                'last_purchase_time': [datetime.now(timezone.utc)],
                'total_purchase_amount': [100.0],
                'total_sessions': [10],
                'created_at': [datetime.now(timezone.utc)],
                'updated_at': [datetime.now(timezone.utc)],
                'first_add_to_cart_time': [datetime.now(timezone.utc)],
                'last_add_to_cart_time': [datetime.now(timezone.utc)],
                'first_view_item_time': [datetime.now(timezone.utc)],
                'last_view_item_time': [datetime.now(timezone.utc)],
                'active_days': [10]  # <--- 這就是不一致的欄位
            })

            # 3. 直接嘗試將包含新欄位的 DataFrame 載入到舊結構的表中
            # 我們預期這裡會直接拋出 BadRequest 錯誤
            with pytest.raises(BadRequest) as excinfo:
                job_config = bigquery.LoadJobConfig() # 預設會檢查 schema
                bigquery_client.client.load_table_from_dataframe(
                    data_with_new_field, temp_table_id, job_config=job_config
                ).result()

            # 4. 驗證拋出的錯誤是否與 schema 不符有關
            # BigQuery 可能會回傳不同的錯誤訊息，我們檢查關鍵字即可
            error_message = str(excinfo.value)
            assert "schema does not match" in error_message.lower() or "no such field" in error_message.lower()
            assert "active_days" in error_message.lower()
            logger.info("✅ 成功捕獲到預期的 schema 不符錯誤 (BadRequest)")

        finally:
            # 5. 清理臨時表
            bigquery_client.client.delete_table(temp_table_id, not_found_ok=True)
            logger.info(f"已清理臨時表: {temp_table_id}")


if __name__ == "__main__":
    # 手動執行測試的範例
    pytest.main([
        __file__ + "::TestRealBigQueryUpdateUserStats::test_update_user_stats_table_with_empty_strings_real_bigquery",
        "-v", "-s", "--tb=short"
    ])

    pytest.main([
        __file__ + "::TestRealBigQueryUpdateUserStats::test_update_user_stats_table_performance_real_bigquery",
        "-v", "-s", "--tb=short"
    ])
